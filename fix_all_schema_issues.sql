-- Fix All Schema Issues
-- Run this to fix foreign keys, missing columns, and refresh schema cache

-- 1. Add missing updated_at column to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- 2. <PERSON>reate trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;

-- Create trigger for profiles
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 3. Verify and fix foreign key constraints
DO $$
BEGIN
    -- Drop existing foreign key constraints if they exist
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'jokes_user_id_fkey' 
        AND table_name = 'jokes'
    ) THEN
        ALTER TABLE public.jokes DROP CONSTRAINT jokes_user_id_fkey;
    END IF;
    
    -- Recreate the foreign key constraint
    ALTER TABLE public.jokes 
    ADD CONSTRAINT jokes_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    
    -- Verify profiles foreign key
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'profiles_id_fkey' 
        AND table_name = 'profiles'
    ) THEN
        ALTER TABLE public.profiles DROP CONSTRAINT profiles_id_fkey;
    END IF;
    
    ALTER TABLE public.profiles 
    ADD CONSTRAINT profiles_id_fkey 
    FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
END $$;

-- 4. Ensure all required tables exist with proper structure
CREATE TABLE IF NOT EXISTS public.seasonal_rankings (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    season text NOT NULL,
    score int DEFAULT 0,
    rank int,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(user_id, season)
);

CREATE TABLE IF NOT EXISTS public.follows (
    follower_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    following_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at timestamptz DEFAULT now(),
    PRIMARY KEY (follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- 5. Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jokes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seasonal_rankings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;

-- 6. Create/recreate essential RLS policies
-- Profiles policies
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON public.profiles;
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles 
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

-- Jokes policies
DROP POLICY IF EXISTS "Public jokes are viewable by everyone" ON public.jokes;
CREATE POLICY "Public jokes are viewable by everyone" ON public.jokes 
    FOR SELECT USING (NOT is_private OR auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own jokes" ON public.jokes;
CREATE POLICY "Users can insert their own jokes" ON public.jokes 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Seasonal rankings policies
DROP POLICY IF EXISTS "Rankings are viewable by everyone" ON public.seasonal_rankings;
CREATE POLICY "Rankings are viewable by everyone" ON public.seasonal_rankings 
    FOR SELECT USING (true);

-- Follows policies
DROP POLICY IF EXISTS "Follows are viewable by everyone" ON public.follows;
CREATE POLICY "Follows are viewable by everyone" ON public.follows 
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can manage their own follows" ON public.follows;
CREATE POLICY "Users can manage their own follows" ON public.follows 
    FOR ALL USING (auth.uid() = follower_id);

-- 7. Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- 8. Verify the fixes
SELECT 'Foreign key constraints:' as info;
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('jokes', 'profiles');

SELECT 'Profiles table structure:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

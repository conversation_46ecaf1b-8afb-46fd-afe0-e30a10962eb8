{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@sentry/react": "^9.32.0", "@sentry/tracing": "^7.120.3", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.39.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "framer-motion": "^10.12.16", "i18next": "^23.0.0", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.244.0", "pg": "^8.16.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.0", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "react-share": "^5.0.3", "react-timeago": "^7.2.0", "react-virtualized": "^9.22.5", "web-vitals": "^2.1.4"}, "scripts": {"dev": "react-scripts start", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "check-mvp": "node src/scripts/check-mvp-progress.js", "create-season": "node src/scripts/createNewSeason.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.15"}}
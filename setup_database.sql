-- Noukta Database Setup Script
-- Run this in Supabase SQL Editor to set up the complete database schema

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL PRIMARY KEY,
    username text UNIQUE,
    avatar_url text,
    bio text,
    created_at timestamptz DEFAULT now(),
    notification_settings jsonb DEFAULT '{"new_comments": true, "new_reactions": true, "new_followers": true}'::jsonb,
    onboarded boolean DEFAULT false
);

-- Create jokes table
CREATE TABLE IF NOT EXISTS public.jokes (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    audio_url text NOT NULL,
    title text NOT NULL,
    content text,
    created_at timestamptz DEFAULT now(),
    likes_count int DEFAULT 0,
    plays_count int DEFAULT 0,
    comments_count int DEFAULT 0,
    voice_effect text,
    is_private boolean DEFAULT false
);

-- Create categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    name text NOT NULL,
    slug text UNIQUE NOT NULL,
    description text,
    created_at timestamptz DEFAULT now()
);

-- Create joke_categories junction table
CREATE TABLE IF NOT EXISTS public.joke_categories (
    joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE,
    category_id uuid REFERENCES public.categories(id) ON DELETE CASCADE,
    PRIMARY KEY (joke_id, category_id)
);

-- Create emoji_reactions table
CREATE TABLE IF NOT EXISTS public.emoji_reactions (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE NOT NULL,
    emoji text NOT NULL,
    created_at timestamptz DEFAULT now(),
    UNIQUE(user_id, joke_id, emoji)
);

-- Create comments table
CREATE TABLE IF NOT EXISTS public.comments (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    content text NOT NULL,
    parent_id uuid REFERENCES public.comments(id) ON DELETE CASCADE,
    is_edited boolean DEFAULT false,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create follows table
CREATE TABLE IF NOT EXISTS public.follows (
    follower_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    following_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at timestamptz DEFAULT now(),
    PRIMARY KEY (follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- Create joke_likes table
CREATE TABLE IF NOT EXISTS public.joke_likes (
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE NOT NULL,
    created_at timestamptz DEFAULT now(),
    PRIMARY KEY (user_id, joke_id)
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS public.achievements (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    title text UNIQUE NOT NULL,
    description text NOT NULL,
    icon text NOT NULL,
    badge_color text NOT NULL,
    category text NOT NULL,
    requirement int NOT NULL,
    xp_reward int DEFAULT 0,
    created_at timestamptz DEFAULT now()
);

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS public.user_achievements (
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    achievement_id uuid REFERENCES public.achievements(id) ON DELETE CASCADE NOT NULL,
    progress int DEFAULT 0,
    unlocked_at timestamptz,
    created_at timestamptz DEFAULT now(),
    PRIMARY KEY (user_id, achievement_id)
);

-- Create reports table
CREATE TABLE IF NOT EXISTS public.reports (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    reporter_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    reported_item_id uuid NOT NULL,
    report_type text NOT NULL CHECK (report_type IN ('joke', 'comment', 'user')),
    reason text NOT NULL,
    status text DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create user_blocks table
CREATE TABLE IF NOT EXISTS public.user_blocks (
    blocker_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    blocked_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at timestamptz DEFAULT now(),
    PRIMARY KEY (blocker_id, blocked_id),
    CHECK (blocker_id != blocked_id)
);

-- Create seasonal_rankings table
CREATE TABLE IF NOT EXISTS public.seasonal_rankings (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    season text NOT NULL,
    score int DEFAULT 0,
    rank int,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(user_id, season)
);

-- Enable Row Level Security on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jokes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.joke_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.emoji_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.joke_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seasonal_rankings ENABLE ROW LEVEL SECURITY;

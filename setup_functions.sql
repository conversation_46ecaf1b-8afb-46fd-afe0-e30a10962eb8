-- Noukta Database Functions
-- Run this AFTER setup_database.sql and setup_rls_policies.sql

-- Function to increment play count
CREATE OR REPLACE FUNCTION increment_play_count(joke_id uuid)
RETURNS void AS $$
BEGIN
    UPDATE public.jokes 
    SET plays_count = plays_count + 1 
    WHERE id = joke_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment likes count
CREATE OR REPLACE FUNCTION increment_likes_count(joke_id uuid)
RETURNS void AS $$
BEGIN
    UPDATE public.jokes 
    SET likes_count = likes_count + 1 
    WHERE id = joke_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement likes count
CREATE OR REPLACE FUNCTION decrement_likes_count(joke_id uuid)
RETURNS void AS $$
BEGIN
    UPDATE public.jokes 
    SET likes_count = GREATEST(likes_count - 1, 0) 
    WHERE id = joke_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get joke reaction counts
CREATE OR REPLACE FUNCTION get_joke_reaction_counts(joke_uuid uuid)
RETURNS TABLE(emoji text, count bigint) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.emoji,
        COUNT(*) as count
    FROM public.emoji_reactions er
    WHERE er.joke_id = joke_uuid
    GROUP BY er.emoji
    ORDER BY count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's reactions for a joke
CREATE OR REPLACE FUNCTION get_user_joke_reactions(joke_uuid uuid, user_uuid uuid)
RETURNS TABLE(emoji text) AS $$
BEGIN
    RETURN QUERY
    SELECT er.emoji
    FROM public.emoji_reactions er
    WHERE er.joke_id = joke_uuid AND er.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to toggle emoji reaction
CREATE OR REPLACE FUNCTION toggle_emoji_reaction(p_emoji text, p_joke_id uuid)
RETURNS TABLE(success boolean, is_added boolean, error text) AS $$
DECLARE
    current_user_id uuid;
    existing_reaction_id uuid;
BEGIN
    -- Get current user
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN QUERY SELECT false, false, 'User not authenticated'::text;
        RETURN;
    END IF;
    
    -- Check if reaction already exists
    SELECT id INTO existing_reaction_id
    FROM public.emoji_reactions
    WHERE user_id = current_user_id 
    AND joke_id = p_joke_id 
    AND emoji = p_emoji;
    
    IF existing_reaction_id IS NOT NULL THEN
        -- Remove existing reaction
        DELETE FROM public.emoji_reactions 
        WHERE id = existing_reaction_id;
        
        RETURN QUERY SELECT true, false, null::text;
    ELSE
        -- Add new reaction
        INSERT INTO public.emoji_reactions (user_id, joke_id, emoji)
        VALUES (current_user_id, p_joke_id, p_emoji);
        
        RETURN QUERY SELECT true, true, null::text;
    END IF;
    
EXCEPTION WHEN OTHERS THEN
    RETURN QUERY SELECT false, false, SQLERRM::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get popular reactions
CREATE OR REPLACE FUNCTION get_popular_reactions(result_limit int DEFAULT 10)
RETURNS TABLE(emoji text, count bigint) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.emoji,
        COUNT(*) as count
    FROM public.emoji_reactions er
    GROUP BY er.emoji
    ORDER BY count DESC
    LIMIT result_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.profiles (id, username, avatar_url, onboarded)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        NEW.raw_user_meta_data->>'avatar_url',
        COALESCE((NEW.raw_user_meta_data->>'onboarded')::boolean, false)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update comments count
CREATE OR REPLACE FUNCTION update_comments_count()
RETURNS trigger AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.jokes 
        SET comments_count = comments_count + 1 
        WHERE id = NEW.joke_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.jokes 
        SET comments_count = GREATEST(comments_count - 1, 0) 
        WHERE id = OLD.joke_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically update comments count
DROP TRIGGER IF EXISTS comments_count_trigger ON public.comments;
CREATE TRIGGER comments_count_trigger
    AFTER INSERT OR DELETE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION update_comments_count();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on comments
DROP TRIGGER IF EXISTS update_comments_updated_at ON public.comments;
CREATE TRIGGER update_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to automatically update updated_at on reports
DROP TRIGGER IF EXISTS update_reports_updated_at ON public.reports;
CREATE TRIGGER update_reports_updated_at
    BEFORE UPDATE ON public.reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

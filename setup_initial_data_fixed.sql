-- Noukta Initial Data Setup (Fixed Version)
-- Run this LAST to populate initial categories and achievements

-- Insert default categories (only if they don't exist)
DO $$
BEGIN
    -- Insert categories one by one to avoid conflicts
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('كوميديا', 'comedy', 'نكت كوميدية عامة')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('عائلة', 'family', 'نكت عائلية مناسبة للجميع')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('عمل', 'work', 'نكت عن العمل والمكتب')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('مدرسة', 'school', 'نكت عن المدرسة والدراسة')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('رياضة', 'sports', 'نكت رياضية')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('تكنولوجيا', 'tech', 'نكت عن التكنولوجيا والإنترنت')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('طبخ', 'cooking', 'نكت عن الطبخ والأكل')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('سفر', 'travel', 'نكت عن السفر والرحلات')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('أصدقاء', 'friends', 'نكت عن الأصدقاء')
    ON CONFLICT (slug) DO NOTHING;
    
    INSERT INTO public.categories (name, slug, description) 
    VALUES ('حيوانات', 'animals', 'نكت عن الحيوانات')
    ON CONFLICT (slug) DO NOTHING;
END $$;

-- Insert achievements (only if they don't exist)
DO $$
BEGIN
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('أول نكتة', 'شارك أول نكتة لك', '🎤', '#10B981', 'content', 1, 10)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('كوميدي مبتدئ', 'احصل على 10 إعجابات', '👍', '#3B82F6', 'engagement', 10, 25)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('نجم الكوميديا', 'احصل على 100 إعجاب', '⭐', '#F59E0B', 'engagement', 100, 100)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('محبوب', 'احصل على 10 متابعين', '❤️', '#EF4444', 'social', 10, 50)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('مؤثر', 'احصل على 50 متابع', '🌟', '#8B5CF6', 'social', 50, 150)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('مشهور', 'احصل على 100 متابع', '🏆', '#F97316', 'social', 100, 300)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('نشيط', 'شارك 10 نكت', '🔥', '#06B6D4', 'content', 10, 75)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('منتج', 'شارك 50 نكتة', '💪', '#84CC16', 'content', 50, 200)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('أسطورة', 'شارك 100 نكتة', '👑', '#DC2626', 'content', 100, 500)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('مستمع جيد', 'استمع إلى 50 نكتة', '👂', '#6366F1', 'engagement', 50, 30)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('عاشق الكوميديا', 'استمع إلى 200 نكتة', '🎧', '#EC4899', 'engagement', 200, 100)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('ناقد', 'اترك 25 تعليق', '💬', '#14B8A6', 'engagement', 25, 60)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('محاور', 'اترك 100 تعليق', '🗣️', '#F472B6', 'engagement', 100, 150)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('رد فعل سريع', 'استخدم 50 رد فعل', '😂', '#FBBF24', 'engagement', 50, 40)
    ON CONFLICT (title) DO NOTHING;
    
    INSERT INTO public.achievements (title, description, icon, badge_color, category, requirement, xp_reward) 
    VALUES ('معبر', 'استخدم 200 رد فعل', '🎭', '#A78BFA', 'engagement', 200, 120)
    ON CONFLICT (title) DO NOTHING;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jokes_user_id ON public.jokes(user_id);
CREATE INDEX IF NOT EXISTS idx_jokes_created_at ON public.jokes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_jokes_likes_count ON public.jokes(likes_count DESC);
CREATE INDEX IF NOT EXISTS idx_jokes_is_private ON public.jokes(is_private);

CREATE INDEX IF NOT EXISTS idx_emoji_reactions_joke_id ON public.emoji_reactions(joke_id);
CREATE INDEX IF NOT EXISTS idx_emoji_reactions_user_id ON public.emoji_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_emoji_reactions_emoji ON public.emoji_reactions(emoji);

CREATE INDEX IF NOT EXISTS idx_comments_joke_id ON public.comments(joke_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON public.comments(parent_id);

CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON public.follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_following_id ON public.follows(following_id);

CREATE INDEX IF NOT EXISTS idx_joke_likes_joke_id ON public.joke_likes(joke_id);
CREATE INDEX IF NOT EXISTS idx_joke_likes_user_id ON public.joke_likes(user_id);

CREATE INDEX IF NOT EXISTS idx_profiles_username ON public.profiles(username);

-- Add search vector column for better search performance
ALTER TABLE public.jokes ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Function to update search vector
CREATE OR REPLACE FUNCTION update_jokes_search_vector()
RETURNS trigger AS $$
BEGIN
    NEW.search_vector := to_tsvector('arabic', NEW.title || ' ' || COALESCE(NEW.content, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update search vector
DROP TRIGGER IF EXISTS update_jokes_search_vector_trigger ON public.jokes;
CREATE TRIGGER update_jokes_search_vector_trigger
    BEFORE INSERT OR UPDATE ON public.jokes
    FOR EACH ROW EXECUTE FUNCTION update_jokes_search_vector();

-- Update existing jokes with search vector (if any exist)
UPDATE public.jokes SET search_vector = to_tsvector('arabic', title || ' ' || COALESCE(content, '')) WHERE search_vector IS NULL;

-- Create index on search vector
CREATE INDEX IF NOT EXISTS idx_jokes_search_vector ON public.jokes USING gin(search_vector);

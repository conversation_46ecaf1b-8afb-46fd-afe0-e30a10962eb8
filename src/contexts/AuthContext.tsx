import React, { createContext, useContext, useMemo } from 'react'
import { useAuth as useAuthHook, UseAuthReturn } from '../hooks/useAuth'

const AuthContext = createContext<UseAuthReturn | undefined>(undefined)

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuthHook()

  // Memoize auth value to prevent unnecessary re-renders
  const authValue = useMemo(() => auth, [
    auth.user?.id,
    auth.loading,
    auth.profile?.id
  ]);

  // Reduced debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 AuthProvider:', auth.loading ? 'loading' : 'ready', auth.user?.email || 'no user');
  }

  return (
    <AuthContext.Provider value={authValue}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): UseAuthReturn => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

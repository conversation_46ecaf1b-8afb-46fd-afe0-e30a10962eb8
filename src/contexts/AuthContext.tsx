import React, { createContext, useContext } from 'react'
import { useAuth as useAuthHook, UseAuthReturn } from '../hooks/useAuth'

const AuthContext = createContext<UseAuthReturn | undefined>(undefined)

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuthHook()

  // Debug logging
  console.log('AuthProvider render - loading:', auth.loading, 'user:', auth.user?.email);

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = (): UseAuthReturn => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

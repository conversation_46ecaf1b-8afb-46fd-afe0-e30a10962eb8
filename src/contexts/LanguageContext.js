import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

const LanguageContext = createContext({});

export const LANGUAGES = {
  AR: {
    code: 'ar',
    name: 'العربية',
    dir: 'rtl',
    icon: '🇲🇦'
  },
  FR: {
    code: 'fr',
    name: 'Français',
    dir: 'ltr',
    icon: '🇫🇷'
  }
};

export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(
    localStorage.getItem('language') || LANGUAGES.AR.code
  );

  const changeLanguage = useCallback((langCode) => {
    const language = Object.values(LANGUAGES).find(lang => lang.code === langCode);
    if (language) {
      setCurrentLanguage(langCode);
      document.documentElement.lang = langCode;
      document.documentElement.dir = language.dir;
      localStorage.setItem('language', langCode);
      i18n.changeLanguage(langCode);
    }
  }, [i18n]);

  useEffect(() => {
    // Initialize language on mount
    const savedLanguage = localStorage.getItem('language') || LANGUAGES.AR.code;
    setCurrentLanguage(savedLanguage);
    document.documentElement.lang = savedLanguage;
    document.documentElement.dir = LANGUAGES[savedLanguage.toUpperCase()].dir;
    i18n.changeLanguage(savedLanguage);
  }, [i18n]);

  const value = {
    currentLanguage,
    changeLanguage,
    languages: LANGUAGES
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}; 
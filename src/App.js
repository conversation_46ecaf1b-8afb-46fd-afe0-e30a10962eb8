import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './i18n';
import './styles/rtl.css';
import './App.css';
import './styles/container.css';
import { useTranslation } from 'react-i18next';

// Providers
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import ErrorBoundary from './components/ErrorBoundary';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import LanguageSwitcher from './components/language/LanguageSwitcher';
import BottomNav from './components/navigation/BottomNav';

// Pages
const Home = React.lazy(() => import('./pages/Home'));
const Record = React.lazy(() => import('./pages/Record'));
const TopJokes = React.lazy(() => import('./pages/TopJokes'));
const RecentJokes = React.lazy(() => import('./pages/RecentJokes'));
const Profile = React.lazy(() => import('./pages/Profile'));
const Search = React.lazy(() => import('./pages/Search'));
const Challenges = React.lazy(() => import('./pages/Challenges'));
const Auth = React.lazy(() => import('./pages/Auth'));
const VerifyEmail = React.lazy(() => import('./pages/VerifyEmail'));
const Achievements = React.lazy(() => import('./pages/Achievements'));
const TestPage = React.lazy(() => import('./pages/TestPage'));
const SimpleHome = React.lazy(() => import('./pages/SimpleHome'));
const SimpleProfile = React.lazy(() => import('./pages/SimpleProfile'));
const ConnectionMonitor = React.lazy(() => import('./components/debug/ConnectionMonitor'));
const Favorites = React.lazy(() => import('./pages/Favorites'));
const LeaderboardPage = React.lazy(() => import('./pages/Leaderboard'));
const Onboarding = React.lazy(() => import('./pages/Onboarding'));
const Admin = React.lazy(() => import('./pages/Admin'));

// Loading Component
const Loading = () => {
  const { t } = useTranslation();
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mb-4"></div>
        <p className="text-gray-600">{t('common.loading')}</p>
      </div>
    </div>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <LanguageProvider>
        <AuthProvider>
          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <Header />
            <LanguageSwitcher />
            <Suspense fallback={<Loading />}>
              <Routes>
                <Route path="/" element={<SimpleHome />} />
                <Route path="/record" element={<Record />} />
                <Route path="/top" element={<TopJokes />} />
                <Route path="/recent" element={<RecentJokes />} />
                <Route path="/profile/:id" element={<SimpleProfile />} />
                <Route path="/profile" element={<SimpleProfile />} />
                <Route path="/search" element={<Search />} />
                <Route path="/challenges" element={<Challenges />} />
                <Route path="/auth" element={<Auth />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
                <Route path="/achievements" element={<Achievements />} />
                <Route path="/favorites" element={<Favorites />} />
                <Route path="/leaderboard" element={<LeaderboardPage />} />
                <Route path="/onboarding" element={<Onboarding />} />
                <Route path="/admin" element={<Admin />} />
                <Route path="/test" element={<TestPage />} />
                <Route path="/simple" element={<SimpleHome />} />
              </Routes>
            </Suspense>
            <Footer />
            <BottomNav />

            {/* Connection Monitor for debugging */}
            <Suspense fallback={null}>
              <ConnectionMonitor />
            </Suspense>
          </Router>
        </AuthProvider>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

export default App;

import { useState, useEffect, useCallback } from 'react';
import { Jo<PERSON>, JokeFilt<PERSON> } from '../types';

// Simple state management without external dependencies
interface AppState {
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Jokes State
  jokes: Joke[];
  currentFilters: JokeFilters;
  selectedJoke: Joke | null;
  
  // User Preferences
  preferences: {
    autoPlay: boolean;
    volume: number;
    theme: 'light' | 'dark' | 'auto';
    language: 'ar' | 'fr';
  };
  
  // Cache State
  lastFetchTime: number;
  cacheVersion: number;
}

interface AppActions {
  // UI Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Jokes Actions
  setJokes: (jokes: Joke[]) => void;
  addJoke: (joke: Joke) => void;
  updateJoke: (id: string, updates: Partial<Joke>) => void;
  removeJoke: (id: string) => void;
  setCurrentFilters: (filters: JokeFilters) => void;
  setSelectedJoke: (joke: Joke | null) => void;
  
  // Preferences Actions
  updatePreferences: (updates: Partial<AppState['preferences']>) => void;
  
  // Cache Actions
  invalidateCache: () => void;
  updateCacheVersion: () => void;
}

// Initial state
const initialState: AppState = {
  isLoading: false,
  error: null,
  jokes: [],
  currentFilters: {},
  selectedJoke: null,
  preferences: {
    autoPlay: false,
    volume: 1,
    theme: 'auto',
    language: 'ar',
  },
  lastFetchTime: 0,
  cacheVersion: 1,
};

// State management class
class AppStore {
  private state: AppState = { ...initialState };
  private listeners: Set<() => void> = new Set();

  // Subscribe to state changes
  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Get current state
  getState(): AppState {
    return this.state;
  }

  // Update state and notify listeners
  private setState(updates: Partial<AppState>) {
    this.state = { ...this.state, ...updates };
    this.listeners.forEach(listener => listener());
  }

  // Actions
  setLoading = (loading: boolean) => {
    this.setState({ isLoading: loading });
  };

  setError = (error: string | null) => {
    this.setState({ error });
  };

  setJokes = (jokes: Joke[]) => {
    this.setState({ 
      jokes,
      lastFetchTime: Date.now()
    });
  };

  addJoke = (joke: Joke) => {
    this.setState({
      jokes: [joke, ...this.state.jokes]
    });
  };

  updateJoke = (id: string, updates: Partial<Joke>) => {
    this.setState({
      jokes: this.state.jokes.map(joke =>
        joke.id === id ? { ...joke, ...updates } : joke
      )
    });
  };

  removeJoke = (id: string) => {
    this.setState({
      jokes: this.state.jokes.filter(joke => joke.id !== id)
    });
  };

  setCurrentFilters = (filters: JokeFilters) => {
    this.setState({ currentFilters: filters });
  };

  setSelectedJoke = (joke: Joke | null) => {
    this.setState({ selectedJoke: joke });
  };

  updatePreferences = (updates: Partial<AppState['preferences']>) => {
    const newPreferences = { ...this.state.preferences, ...updates };
    this.setState({ preferences: newPreferences });
    
    // Persist to localStorage
    try {
      localStorage.setItem('noukta_preferences', JSON.stringify(newPreferences));
    } catch (error) {
      console.warn('Failed to save preferences to localStorage:', error);
    }
  };

  invalidateCache = () => {
    this.setState({
      lastFetchTime: 0,
      cacheVersion: this.state.cacheVersion + 1
    });
  };

  updateCacheVersion = () => {
    this.setState({
      cacheVersion: this.state.cacheVersion + 1
    });
  };

  // Initialize store with persisted data
  initialize = () => {
    try {
      const savedPreferences = localStorage.getItem('noukta_preferences');
      if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        this.setState({ preferences: { ...this.state.preferences, ...preferences } });
      }
    } catch (error) {
      console.warn('Failed to load preferences from localStorage:', error);
    }
  };

  // Reset store to initial state
  reset = () => {
    this.state = { ...initialState };
    this.listeners.forEach(listener => listener());
  };
}

// Create singleton store instance
const appStore = new AppStore();

// Initialize store
appStore.initialize();

// React hook to use the store
export const useAppStore = <T>(selector?: (state: AppState) => T): T extends undefined ? AppState & AppActions : T => {
  const [state, setState] = useState(() => 
    selector ? selector(appStore.getState()) : appStore.getState()
  );

  useEffect(() => {
    const unsubscribe = appStore.subscribe(() => {
      const newState = selector ? selector(appStore.getState()) : appStore.getState();
      setState(newState);
    });

    return unsubscribe;
  }, [selector]);

  // Return state with actions
  const stateWithActions = {
    ...appStore.getState(),
    setLoading: appStore.setLoading,
    setError: appStore.setError,
    setJokes: appStore.setJokes,
    addJoke: appStore.addJoke,
    updateJoke: appStore.updateJoke,
    removeJoke: appStore.removeJoke,
    setCurrentFilters: appStore.setCurrentFilters,
    setSelectedJoke: appStore.setSelectedJoke,
    updatePreferences: appStore.updatePreferences,
    invalidateCache: appStore.invalidateCache,
    updateCacheVersion: appStore.updateCacheVersion,
  };

  return selector ? state : stateWithActions as any;
};

// Specific selectors for common use cases
export const useJokes = () => useAppStore(state => state.jokes);
export const useCurrentFilters = () => useAppStore(state => state.currentFilters);
export const useSelectedJoke = () => useAppStore(state => state.selectedJoke);
export const usePreferences = () => useAppStore(state => state.preferences);
export const useLoadingState = () => useAppStore(state => ({ 
  isLoading: state.isLoading, 
  error: state.error 
}));

// Actions-only hook
export const useAppActions = () => {
  return {
    setLoading: appStore.setLoading,
    setError: appStore.setError,
    setJokes: appStore.setJokes,
    addJoke: appStore.addJoke,
    updateJoke: appStore.updateJoke,
    removeJoke: appStore.removeJoke,
    setCurrentFilters: appStore.setCurrentFilters,
    setSelectedJoke: appStore.setSelectedJoke,
    updatePreferences: appStore.updatePreferences,
    invalidateCache: appStore.invalidateCache,
    updateCacheVersion: appStore.updateCacheVersion,
  };
};

export default appStore;

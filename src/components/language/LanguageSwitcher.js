import React from 'react';
import { useLanguage, LANGUAGES } from '../../contexts/LanguageContext';
import { motion, AnimatePresence } from 'framer-motion';

const LanguageSwitcher = () => {
  const { currentLanguage, changeLanguage } = useLanguage();

  const handleLanguageChange = (langCode) => {
    changeLanguage(langCode);
  };

  return (
    <div className="fixed bottom-20 right-4 z-50">
      <div className="bg-white rounded-full shadow-lg p-2 flex flex-col gap-2">
        <AnimatePresence>
          {Object.values(LANGUAGES).map((language) => (
            <motion.button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`w-10 h-10 rounded-full flex items-center justify-center text-lg
                ${currentLanguage === language.code
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                } transition-colors duration-200`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              {language.icon}
            </motion.button>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default LanguageSwitcher; 
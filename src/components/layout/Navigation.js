import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, Search, User, Mic } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../contexts/LanguageContext';

const Navigation = () => {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguage();

  const navItems = [
    { to: '/', Icon: Home, label: t('navigation.main.home') },
    { to: '/search', Icon: Search, label: t('navigation.main.search') },
    { to: '/record', Icon: Mic, label: t('navigation.main.record') },
    { to: '/profile', Icon: User, label: t('navigation.main.profile') }
  ];

  return (
    <nav className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex justify-between h-16">
          {navItems.map(({ to, Icon, label }) => (
            <NavItem
              key={to}
              to={to}
              Icon={Icon}
              label={label}
              currentLanguage={currentLanguage}
            />
          ))}
        </div>
      </div>
    </nav>
  );
};

const NavItem = ({ to, Icon, label, isActive, currentLanguage }) => {
  const isRecordButton = to === '/record';
  
  return (
    <NavLink
      to={to}
      className={({ isActive }) =>
        `flex items-center px-3 py-2 text-sm font-medium ${
          isActive ? 'text-primary-600' : 'text-gray-600 hover:text-primary-500'
        } ${currentLanguage === 'fr' ? 'font-french' : 'font-arabic'}`
      }
    >
      <Icon className="h-5 w-5 ltr:mr-1.5 rtl:ml-1.5" />
      <span>{label}</span>
    </NavLink>
  );
};

export default Navigation;
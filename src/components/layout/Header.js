import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Mic, User } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const Header = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { currentLanguage } = useLanguage();

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <Link 
            to="/" 
            className="flex items-center space-x-2 rtl:space-x-reverse"
          >
            <span className={`text-2xl font-bold text-primary-600 ${
              currentLanguage === 'fr' ? 'font-french' : 'font-arabic'
            }`}>
              {currentLanguage === 'fr' ? 'Noukta' : 'نكتة'}
            </span>
          </Link>

          {/* Navigation Actions */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <Link
              to="/record"
              className={`
                inline-flex items-center px-4 py-2 border border-transparent 
                rounded-md shadow-sm text-sm font-medium text-white 
                bg-primary-600 hover:bg-primary-700 
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
                ${currentLanguage === 'fr' ? 'font-french' : 'font-arabic'}
              `}
            >
              <Mic className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
              {t('navigation.main.record')}
            </Link>

            {user ? (
              <Link
                to={`/profile/${user.id}`}
                className={`
                  inline-flex items-center px-3 py-2 border border-gray-300 
                  rounded-md text-sm font-medium text-gray-700 
                  bg-white hover:bg-gray-50 
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
                  ${currentLanguage === 'fr' ? 'font-french' : 'font-arabic'}
                `}
              >
                <User className="h-4 w-4 ltr:mr-2 rtl:ml-2" />
                {t('profile.myProfile')}
              </Link>
            ) : (
              <Link
                to="/auth"
                className={`
                  inline-flex items-center px-3 py-2 border border-gray-300 
                  rounded-md text-sm font-medium text-gray-700 
                  bg-white hover:bg-gray-50 
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
                  ${currentLanguage === 'fr' ? 'font-french' : 'font-arabic'}
                `}
              >
                {t('auth.login')}
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../contexts/LanguageContext';

const Footer = () => {
  const { t } = useTranslation();
  const { currentLanguage } = useLanguage();
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`bg-gray-800 text-white mt-auto ${
      currentLanguage === 'fr' ? 'font-french' : 'font-arabic'
    }`}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-sm text-center md:text-start">
            {t('footer.copyright', { year: currentYear })}
          </p>
          <div className="flex flex-wrap justify-center md:justify-end gap-4 md:gap-8">
            <button type="button" className="hover:underline">{t('footer.links.terms')}</button>
            <button type="button" className="hover:underline">{t('footer.links.privacy')}</button>
            <button type="button" className="hover:underline">{t('footer.links.contact')}</button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
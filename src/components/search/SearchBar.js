import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import debounce from 'lodash/debounce';
import { Search } from 'lucide-react';

const SearchBar = ({ onSearch }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const { t } = useTranslation();

    const debouncedSearch = useCallback(
        debounce((term) => {
            onSearch(term);
        }, 300),
        [onSearch]
    );

    const handleSearch = (e) => {
        const term = e.target.value;
        setSearchTerm(term);
        debouncedSearch(term);
    };

    return (
        <div className="relative w-full max-w-xl mx-auto">
            <div className="relative">
                <input
                    type="text"
                    value={searchTerm}
                    onChange={handleSearch}
                    placeholder={t('search.placeholder')}
                    className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            </div>
        </div>
    );
};

export default SearchBar; 
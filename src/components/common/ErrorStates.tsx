import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Refresh<PERSON>w,
  Wifi,
  Server,
  Shield,
  Clock,
  Bug,
  Home
} from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

interface ErrorDisplayProps {
  type?: 'network' | 'server' | 'auth' | 'timeout' | 'generic' | 'not-found';
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoHome?: () => void;
  showDetails?: boolean;
  error?: Error;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  type = 'generic',
  title,
  message,
  onRetry,
  onGoHome,
  showDetails = false,
  error
}) => {
  const { t } = useTranslation();

  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: <Wifi className="w-16 h-16 text-red-500" />,
          title: t('errors.network.title'),
          message: t('errors.network.message'),
          color: 'red'
        };
      case 'server':
        return {
          icon: <Server className="w-16 h-16 text-orange-500" />,
          title: t('errors.server.title'),
          message: t('errors.server.message'),
          color: 'orange'
        };
      case 'auth':
        return {
          icon: <Shield className="w-16 h-16 text-blue-500" />,
          title: t('errors.auth.title'),
          message: t('errors.auth.message'),
          color: 'blue'
        };
      case 'timeout':
        return {
          icon: <Clock className="w-16 h-16 text-yellow-500" />,
          title: t('errors.timeout.title'),
          message: t('errors.timeout.message'),
          color: 'yellow'
        };
      case 'not-found':
        return {
          icon: <AlertTriangle className="w-16 h-16 text-gray-500" />,
          title: t('errors.notFound.title'),
          message: t('errors.notFound.message'),
          color: 'gray'
        };
      default:
        return {
          icon: <Bug className="w-16 h-16 text-red-500" />,
          title: t('errors.generic.title'),
          message: t('errors.generic.message'),
          color: 'red'
        };
    }
  };

  const config = getErrorConfig();

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center p-8">
      <div className="mb-6">
        {config.icon}
      </div>
      
      <h2 className="text-2xl font-bold text-gray-900 mb-3">
        {title || config.title}
      </h2>
      
      <p className="text-gray-600 max-w-md mb-6">
        {message || config.message}
      </p>

      {/* Error Details (Development) */}
      {showDetails && error && (
        <details className="mb-6 w-full max-w-2xl">
          <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
            Show technical details
          </summary>
          <div className="mt-2 p-4 bg-gray-100 rounded-lg text-left text-sm font-mono text-gray-700 overflow-auto">
            <div className="mb-2">
              <strong>Error:</strong> {error.message}
            </div>
            {error.stack && (
              <div>
                <strong>Stack:</strong>
                <pre className="whitespace-pre-wrap text-xs mt-1">
                  {error.stack}
                </pre>
              </div>
            )}
          </div>
        </details>
      )}

      {/* Actions */}
      <div className="flex flex-col sm:flex-row gap-3">
        {onRetry && (
          <button
            onClick={onRetry}
            className={`flex items-center gap-2 px-6 py-3 bg-${config.color}-600 text-white rounded-lg hover:bg-${config.color}-700 transition-colors`}
          >
            <RefreshCw className="w-4 h-4" />
            {t('common.retry')}
          </button>
        )}
        
        {onGoHome && (
          <button
            onClick={onGoHome}
            className="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <Home className="w-4 h-4" />
            {t('navigation.main.home')}
          </button>
        )}
        
        {type === 'auth' && (
          <a
            href="/auth"
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Shield className="w-4 h-4" />
            {t('auth.signIn')}
          </a>
        )}
      </div>
    </div>
  );
};

interface InlineErrorProps {
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  type?: 'error' | 'warning' | 'info';
}

export const InlineError: React.FC<InlineErrorProps> = ({
  message,
  onRetry,
  onDismiss,
  type = 'error'
}) => {
  const getConfig = () => {
    switch (type) {
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600'
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600'
        };
      default:
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600'
        };
    }
  };

  const config = getConfig();

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-lg p-4`}>
      <div className="flex items-start gap-3">
        <AlertTriangle className={`w-5 h-5 ${config.iconColor} flex-shrink-0 mt-0.5`} />
        <div className="flex-1">
          <p className={`${config.textColor} text-sm`}>{message}</p>
        </div>
        <div className="flex gap-2">
          {onRetry && (
            <button
              onClick={onRetry}
              className={`${config.textColor} hover:opacity-75 text-sm font-medium`}
            >
              Retry
            </button>
          )}
          {onDismiss && (
            <button
              onClick={onDismiss}
              className={`${config.textColor} hover:opacity-75 text-sm`}
            >
              ×
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<any> },
  ErrorBoundaryState
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Log to error reporting service
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack
          }
        }
      });
    }

    this.setState({
      hasError: true,
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent) {
        return (
          <FallbackComponent
            error={this.state.error}
            onRetry={this.handleRetry}
            onGoHome={this.handleGoHome}
          />
        );
      }

      return (
        <ErrorDisplay
          type="generic"
          title="Application Error"
          message="Something went wrong in the application. Please try refreshing the page."
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
          showDetails={process.env.NODE_ENV === 'development'}
          error={this.state.error}
        />
      );
    }

    return this.props.children;
  }
}

interface NetworkErrorProps {
  onRetry: () => void;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({ onRetry }) => (
  <ErrorDisplay
    type="network"
    onRetry={onRetry}
  />
);

interface ServerErrorProps {
  onRetry: () => void;
}

export const ServerError: React.FC<ServerErrorProps> = ({ onRetry }) => (
  <ErrorDisplay
    type="server"
    onRetry={onRetry}
  />
);

export const AuthError: React.FC = () => (
  <ErrorDisplay type="auth" />
);

interface NotFoundErrorProps {
  onGoHome?: () => void;
  type?: 'page' | 'joke' | 'user' | 'generic';
}

export const NotFoundError: React.FC<NotFoundErrorProps> = ({ 
  onGoHome, 
  type = 'generic' 
}) => {
  const getContent = () => {
    switch (type) {
      case 'page':
        return {
          title: 'Page Not Found',
          message: 'The page you\'re looking for doesn\'t exist.'
        };
      case 'joke':
        return {
          title: 'Joke Not Found',
          message: 'This joke may have been deleted or doesn\'t exist.'
        };
      case 'user':
        return {
          title: 'User Not Found',
          message: 'This user profile doesn\'t exist or has been deactivated.'
        };
      default:
        return {
          title: 'Content Not Found',
          message: 'The content you\'re looking for doesn\'t exist.'
        };
    }
  };

  const content = getContent();

  return (
    <ErrorDisplay
      type="not-found"
      title={content.title}
      message={content.message}
      onGoHome={onGoHome || (() => window.location.href = '/')}
    />
  );
};

// Hook for handling async errors
export const useErrorHandler = () => {
  const [error, setError] = React.useState<string | null>(null);

  const handleError = React.useCallback((err: any) => {
    console.error('Handled error:', err);
    
    if (err?.message) {
      setError(err.message);
    } else if (typeof err === 'string') {
      setError(err);
    } else {
      setError('An unexpected error occurred');
    }

    // Log to error reporting
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(err);
    }
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
};

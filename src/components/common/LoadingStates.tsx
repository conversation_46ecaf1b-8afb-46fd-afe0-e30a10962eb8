import React from 'react';
import { Loader2, Music, Users, MessageCircle, Trophy } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
};

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
}

export const Skeleton: React.FC<SkeletonProps> = ({ 
  className = '', 
  variant = 'rectangular' 
}) => {
  const baseClasses = 'animate-pulse bg-gray-200';
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded'
  };

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} />
  );
};

export const JokeCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
      {/* Header */}
      <div className="flex items-center gap-3 mb-4">
        <Skeleton variant="circular" className="w-10 h-10" />
        <div className="flex-1">
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-3 w-16" />
        </div>
      </div>

      {/* Title */}
      <Skeleton className="h-6 w-3/4 mb-4" />

      {/* Audio Player */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <div className="flex items-center gap-4">
          <Skeleton variant="circular" className="w-12 h-12" />
          <div className="flex-1">
            <Skeleton className="h-2 w-full mb-2" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-4">
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  );
};

export const ProfileSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Skeleton variant="circular" className="w-20 h-20" />
        <div className="flex-1">
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-48 mb-3" />
          <div className="flex gap-4">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="text-center">
            <Skeleton className="h-8 w-12 mx-auto mb-2" />
            <Skeleton className="h-4 w-16 mx-auto" />
          </div>
        ))}
      </div>

      {/* Bio */}
      <Skeleton className="h-4 w-full mb-2" />
      <Skeleton className="h-4 w-3/4" />
    </div>
  );
};

export const CommentSkeleton: React.FC = () => {
  return (
    <div className="flex gap-3 animate-pulse">
      <Skeleton variant="circular" className="w-8 h-8 flex-shrink-0" />
      <div className="flex-1">
        <div className="bg-gray-50 rounded-lg p-3">
          <Skeleton className="h-4 w-20 mb-2" />
          <Skeleton className="h-4 w-full mb-1" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        <div className="flex gap-4 mt-2">
          <Skeleton className="h-3 w-12" />
          <Skeleton className="h-3 w-12" />
        </div>
      </div>
    </div>
  );
};

export const LeaderboardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <Skeleton className="h-6 w-32" />
      </div>
      <div className="divide-y divide-gray-200">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="px-6 py-4 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Skeleton variant="circular" className="w-6 h-6" />
                <Skeleton variant="circular" className="w-10 h-10" />
                <div>
                  <Skeleton className="h-4 w-24 mb-2" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <div className="text-right">
                <Skeleton className="h-6 w-16 mb-1" />
                <Skeleton className="h-3 w-12" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface LoadingPageProps {
  type?: 'jokes' | 'profile' | 'leaderboard' | 'comments';
  message?: string;
}

export const LoadingPage: React.FC<LoadingPageProps> = ({ 
  type = 'jokes', 
  message 
}) => {
  const getIcon = () => {
    switch (type) {
      case 'profile':
        return <Users className="w-12 h-12 text-blue-500" />;
      case 'leaderboard':
        return <Trophy className="w-12 h-12 text-yellow-500" />;
      case 'comments':
        return <MessageCircle className="w-12 h-12 text-green-500" />;
      default:
        return <Music className="w-12 h-12 text-purple-500" />;
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'profile':
        return 'Loading profile...';
      case 'leaderboard':
        return 'Loading leaderboard...';
      case 'comments':
        return 'Loading comments...';
      default:
        return 'Loading jokes...';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
      <div className="relative mb-6">
        {getIcon()}
        <div className="absolute -bottom-1 -right-1">
          <LoadingSpinner size="sm" className="text-gray-400" />
        </div>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {message || getDefaultMessage()}
      </h3>
      <p className="text-gray-600 max-w-md">
        Please wait while we fetch the latest content for you.
      </p>
    </div>
  );
};

interface EmptyStateProps {
  type?: 'jokes' | 'profile' | 'leaderboard' | 'comments' | 'search';
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  type = 'jokes',
  title,
  description,
  actionLabel,
  onAction
}) => {
  const getIcon = () => {
    switch (type) {
      case 'profile':
        return <Users className="w-16 h-16 text-gray-400" />;
      case 'leaderboard':
        return <Trophy className="w-16 h-16 text-gray-400" />;
      case 'comments':
        return <MessageCircle className="w-16 h-16 text-gray-400" />;
      case 'search':
        return <Music className="w-16 h-16 text-gray-400" />;
      default:
        return <Music className="w-16 h-16 text-gray-400" />;
    }
  };

  const getDefaultContent = () => {
    switch (type) {
      case 'profile':
        return {
          title: 'No jokes yet',
          description: 'This user hasn\'t posted any jokes yet. Check back later!',
          actionLabel: 'Explore other profiles'
        };
      case 'leaderboard':
        return {
          title: 'No rankings yet',
          description: 'Be the first to start posting jokes and climb the leaderboard!',
          actionLabel: 'Post your first joke'
        };
      case 'comments':
        return {
          title: 'No comments yet',
          description: 'Be the first to comment on this joke!',
          actionLabel: 'Add a comment'
        };
      case 'search':
        return {
          title: 'No results found',
          description: 'Try adjusting your search terms or browse all jokes.',
          actionLabel: 'Clear search'
        };
      default:
        return {
          title: 'No jokes yet',
          description: 'Be the first to share a joke with the community!',
          actionLabel: 'Record your first joke'
        };
    }
  };

  const defaultContent = getDefaultContent();

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] text-center p-8">
      <div className="mb-6">
        {getIcon()}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-3">
        {title || defaultContent.title}
      </h3>
      <p className="text-gray-600 max-w-md mb-6">
        {description || defaultContent.description}
      </p>
      {(onAction || actionLabel) && (
        <button
          onClick={onAction}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          {actionLabel || defaultContent.actionLabel}
        </button>
      )}
    </div>
  );
};

export const InlineLoader: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => {
  return (
    <div className="flex items-center justify-center gap-3 py-8">
      <LoadingSpinner size="sm" className="text-blue-600" />
      <span className="text-gray-600">{message}</span>
    </div>
  );
};

export const ButtonLoader: React.FC<{ size?: 'sm' | 'md' }> = ({ size = 'sm' }) => {
  return <LoadingSpinner size={size} className="text-current" />;
};

import React, { useState } from 'react';
import { Share2, Check, X } from 'lucide-react';
import {
  WhatsappShareButton,
  TwitterShareButton,
  FacebookShareButton,
  TelegramShareButton,
  WhatsappIcon,
  TwitterIcon,
  FacebookIcon,
  TelegramIcon
} from 'react-share';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../contexts/LanguageContext';

const ShareButton = ({ url, title, onClose }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const { t } = useTranslation();
  const { currentLanguage } = useLanguage();

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-gray-500 hover:text-primary-600 rounded-full hover:bg-gray-100 transition-colors duration-200"
        aria-label={t('sharing.shareJoke')}
      >
        <Share2 className="h-5 w-5" />
      </button>

      {isOpen && (
        <div className={`
          absolute bottom-full right-0 mb-2 bg-white rounded-lg shadow-lg p-4 w-72
          ${currentLanguage === 'fr' ? 'font-french' : 'font-arabic'}
        `}>
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium text-gray-900">
              {t('sharing.shareJoke')}
            </h3>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              aria-label={t('common.close')}
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="grid grid-cols-4 gap-4 mb-4">
            <WhatsappShareButton url={url} title={title}>
              <WhatsappIcon size={32} round />
            </WhatsappShareButton>

            <TwitterShareButton url={url} title={title}>
              <TwitterIcon size={32} round />
            </TwitterShareButton>

            <FacebookShareButton url={url} quote={title}>
              <FacebookIcon size={32} round />
            </FacebookShareButton>

            <TelegramShareButton url={url} title={title}>
              <TelegramIcon size={32} round />
            </TelegramShareButton>
          </div>

          <div className="flex items-center space-x-2 rtl:space-x-reverse bg-gray-50 rounded-lg p-2">
            <input
              type="text"
              value={url}
              readOnly
              className="flex-1 bg-transparent text-sm text-gray-600 outline-none"
              dir="ltr"
            />
            <button
              onClick={handleCopyLink}
              className={`
                px-3 py-1 text-sm font-medium rounded-md bg-white shadow-sm 
                hover:bg-gray-50 transition-colors duration-200
                flex items-center gap-2
              `}
            >
              {copied ? (
                <Check className="h-4 w-4 text-green-500" />
              ) : (
                t('sharing.copyLink')
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ShareButton;
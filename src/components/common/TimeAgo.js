import React from 'react';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import { ar, fr } from 'date-fns/locale';

const locales = {
  ar,
  fr
};

const TimeAgo = ({ date }) => {
  const { i18n } = useTranslation();
  const locale = locales[i18n.language] || undefined;

  return (
    <time
      dateTime={date}
      className="text-sm text-gray-500"
      title={new Date(date).toLocaleString(i18n.language)}
    >
      {formatDistanceToNow(new Date(date), {
        addSuffix: true,
        locale
      })}
    </time>
  );
};

export default TimeAgo; 
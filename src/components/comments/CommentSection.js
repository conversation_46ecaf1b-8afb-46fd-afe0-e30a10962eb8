import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MessageCircle, Send, MoreVertical, Edit2, Trash2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { socialService } from '../../lib/services/social-service';
import TimeAgo from '../common/TimeAgo';

const CommentSection = ({ jokeId }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingComment, setEditingComment] = useState(null);

  useEffect(() => {
    loadComments();
  }, [jokeId]);

  const loadComments = async () => {
    try {
      const data = await socialService.getComments(jokeId);
      setComments(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    try {
      const comment = await socialService.addComment(jokeId, newComment.trim());
      setComments([...comments, comment]);
      setNewComment('');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleEdit = async (commentId, content) => {
    try {
      const updated = await socialService.updateComment(commentId, content);
      setComments(comments.map(c => c.id === commentId ? updated : c));
      setEditingComment(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDelete = async (commentId) => {
    if (!window.confirm(t('comments.deleteConfirm'))) return;

    try {
      await socialService.deleteComment(commentId);
      setComments(comments.filter(c => c.id !== commentId));
    } catch (err) {
      setError(err.message);
    }
  };

  const Comment = ({ comment }) => {
    const [showActions, setShowActions] = useState(false);
    const [editContent, setEditContent] = useState(comment.content);
    const isEditing = editingComment === comment.id;
    const isOwner = user?.id === comment.user_id;

    return (
      <div className="flex space-x-3 rtl:space-x-reverse p-4 hover:bg-gray-50 rounded-lg">
        <img
          src={comment.user?.avatar_url || '/default-avatar.png'}
          alt={comment.user?.username}
          className="w-8 h-8 rounded-full"
        />
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <span className="font-medium">{comment.user?.username}</span>
              <TimeAgo date={comment.created_at} />
              {comment.is_edited && (
                <span className="text-xs text-gray-500">({t('comments.edited')})</span>
              )}
            </div>
            {isOwner && (
              <div className="relative">
                <button
                  onClick={() => setShowActions(!showActions)}
                  className="p-1 hover:bg-gray-100 rounded-full"
                >
                  <MoreVertical className="w-4 h-4" />
                </button>
                {showActions && (
                  <div className="absolute right-0 mt-1 py-1 w-32 bg-white rounded-md shadow-lg z-10">
                    <button
                      onClick={() => {
                        setEditingComment(comment.id);
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Edit2 className="w-4 h-4 mr-2" />
                      {t('common.edit')}
                    </button>
                    <button
                      onClick={() => handleDelete(comment.id)}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      {t('common.delete')}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
          {isEditing ? (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleEdit(comment.id, editContent);
              }}
              className="mt-1"
            >
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                rows="2"
              />
              <div className="flex justify-end space-x-2 mt-2">
                <button
                  type="button"
                  onClick={() => setEditingComment(null)}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="submit"
                  className="px-3 py-1 text-sm bg-primary-600 text-white rounded-md hover:bg-primary-700"
                >
                  {t('common.save')}
                </button>
              </div>
            </form>
          ) : (
            <p className="mt-1 text-gray-800">{comment.content}</p>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-20 bg-gray-100 rounded-lg"></div>
        <div className="h-20 bg-gray-100 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 rtl:space-x-reverse">
        <MessageCircle className="w-5 h-5 text-gray-600" />
        <h3 className="text-lg font-medium">{t('comments.title')}</h3>
        <span className="text-gray-500">({comments.length})</span>
      </div>

      {error && (
        <div className="text-red-500 text-sm">
          {t('errors.generic')}: {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex space-x-2 rtl:space-x-reverse">
        <input
          type="text"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder={t('comments.placeholder')}
          className="flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
        <button
          type="submit"
          disabled={!newComment.trim()}
          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Send className="w-5 h-5" />
        </button>
      </form>

      <div className="space-y-2">
        {comments.map((comment) => (
          <Comment key={comment.id} comment={comment} />
        ))}
      </div>

      {comments.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          {t('comments.empty')}
        </div>
      )}
    </div>
  );
};

export default CommentSection; 
import React, { useState, useEffect } from 'react';
import { MessageCircle, Send, MoreVertical, Edit, Trash2, Reply } from 'lucide-react';
import { commentsService, Comment } from '../../services/commentsService';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import TimeAgo from '../common/TimeAgo';

interface CommentsSectionProps {
  jokeId: string;
  initialCount?: number;
  onCommentCountChange?: (count: number) => void;
}

interface CommentItemProps {
  comment: Comment;
  onReply: (parentId: string) => void;
  onEdit: (comment: Comment) => void;
  onDelete: (commentId: string) => void;
  level?: number;
}

const CommentItem: React.FC<CommentItemProps> = ({ 
  comment, 
  onReply, 
  onEdit, 
  onDelete, 
  level = 0 
}) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [showOptions, setShowOptions] = useState(false);
  const isOwner = user?.id === comment.user_id;
  const maxLevel = 3; // Maximum nesting level

  return (
    <div className={`${level > 0 ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>
      <div className="flex gap-3 group">
        {/* Avatar */}
        <img
          src={comment.user?.avatar_url || '/default-avatar.svg'}
          alt={comment.user?.username || 'User'}
          className="w-8 h-8 rounded-full flex-shrink-0"
        />

        {/* Comment Content */}
        <div className="flex-1 min-w-0">
          <div className="bg-gray-50 rounded-lg px-3 py-2">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-sm text-gray-900">
                {comment.user?.username || 'Unknown User'}
              </span>
              <TimeAgo date={comment.created_at} />
              {comment.is_edited && (
                <span className="text-xs text-gray-400">(edited)</span>
              )}
            </div>
            <p className="text-sm text-gray-700 whitespace-pre-wrap">
              {comment.content}
            </p>
          </div>

          {/* Comment Actions */}
          <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
            {level < maxLevel && (
              <button
                onClick={() => onReply(comment.id)}
                className="hover:text-blue-600 transition-colors"
              >
                Reply
              </button>
            )}
            
            {isOwner && (
              <div className="relative">
                <button
                  onClick={() => setShowOptions(!showOptions)}
                  className="hover:text-gray-700 transition-colors"
                >
                  <MoreVertical className="w-3 h-3" />
                </button>
                
                {showOptions && (
                  <div className="absolute top-full left-0 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10">
                    <button
                      onClick={() => {
                        onEdit(comment);
                        setShowOptions(false);
                      }}
                      className="w-full px-3 py-1 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Edit className="w-3 h-3" />
                      Edit
                    </button>
                    <button
                      onClick={() => {
                        onDelete(comment.id);
                        setShowOptions(false);
                      }}
                      className="w-full px-3 py-1 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
                    >
                      <Trash2 className="w-3 h-3" />
                      Delete
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3 space-y-3">
              {comment.replies.map((reply) => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  level={level + 1}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const CommentsSection: React.FC<CommentsSectionProps> = ({
  jokeId,
  initialCount = 0,
  onCommentCountChange
}) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<Comment | null>(null);
  const [editContent, setEditContent] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(initialCount);
  const [hasMore, setHasMore] = useState(false);

  // Load comments
  const loadComments = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data, error: commentsError } = await commentsService.getComments(jokeId, {
        limit: 20,
        includeReplies: true
      });

      if (commentsError) {
        throw commentsError;
      }

      if (data) {
        setComments(data.comments);
        setTotalCount(data.total_count);
        setHasMore(data.has_more);
        onCommentCountChange?.(data.total_count);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  };

  // Submit comment
  const handleSubmitComment = async () => {
    if (!newComment.trim() || !user) return;

    setSubmitting(true);
    setError(null);

    try {
      const { data, error: submitError } = await commentsService.createComment({
        joke_id: jokeId,
        content: newComment.trim(),
        parent_id: replyingTo || undefined
      });

      if (submitError) {
        throw submitError;
      }

      setNewComment('');
      setReplyingTo(null);
      await loadComments(); // Reload to get updated structure
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post comment');
    } finally {
      setSubmitting(false);
    }
  };

  // Edit comment
  const handleEditComment = async () => {
    if (!editContent.trim() || !editingComment) return;

    setSubmitting(true);
    setError(null);

    try {
      const { error: editError } = await commentsService.updateComment(
        editingComment.id,
        { content: editContent.trim() }
      );

      if (editError) {
        throw editError;
      }

      setEditingComment(null);
      setEditContent('');
      await loadComments();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update comment');
    } finally {
      setSubmitting(false);
    }
  };

  // Delete comment
  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) return;

    try {
      const { error: deleteError } = await commentsService.deleteComment(commentId);

      if (deleteError) {
        throw deleteError;
      }

      await loadComments();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete comment');
    }
  };

  // Start editing
  const startEditing = (comment: Comment) => {
    setEditingComment(comment);
    setEditContent(comment.content);
  };

  // Load comments on mount
  useEffect(() => {
    loadComments();
  }, [jokeId]);

  // Real-time updates
  useEffect(() => {
    const subscription = commentsService.subscribeToComments(jokeId, () => {
      loadComments(); // Reload comments when changes occur
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [jokeId]);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center gap-2">
        <MessageCircle className="w-5 h-5 text-gray-600" />
        <span className="font-medium text-gray-900">
          Comments ({totalCount})
        </span>
      </div>

      {/* Comment Input */}
      {user ? (
        <div className="space-y-3">
          {replyingTo && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Reply className="w-4 h-4" />
              <span>Replying to comment</span>
              <button
                onClick={() => setReplyingTo(null)}
                className="text-blue-600 hover:text-blue-700"
              >
                Cancel
              </button>
            </div>
          )}

          <div className="flex gap-3">
            <img
              src={user.user_metadata?.avatar_url || '/default-avatar.svg'}
              alt="Your avatar"
              className="w-8 h-8 rounded-full flex-shrink-0"
            />
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder={replyingTo ? "Write a reply..." : "Write a comment..."}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                rows={3}
                maxLength={500}
              />
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">
                  {newComment.length}/500
                </span>
                <button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || submitting}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                  {submitting ? 'Posting...' : 'Post'}
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-4 text-gray-600">
          <a href="/auth" className="text-blue-600 hover:text-blue-700">
            Sign in to comment
          </a>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
          {error}
        </div>
      )}

      {/* Edit Modal */}
      {editingComment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4">Edit Comment</h3>
            <textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows={4}
              maxLength={500}
            />
            <div className="flex items-center justify-between mt-4">
              <span className="text-xs text-gray-500">
                {editContent.length}/500
              </span>
              <div className="flex gap-2">
                <button
                  onClick={() => setEditingComment(null)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleEditComment}
                  disabled={!editContent.trim() || submitting}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {submitting ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Comments List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading comments...</p>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8 text-gray-600">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p>No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onReply={setReplyingTo}
              onEdit={startEditing}
              onDelete={handleDeleteComment}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default CommentsSection;

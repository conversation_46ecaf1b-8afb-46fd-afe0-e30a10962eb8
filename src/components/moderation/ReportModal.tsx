import React, { useState } from 'react';
import { <PERSON>, <PERSON>, AlertTriangle } from 'lucide-react';
import { reportService, ReportType, ReportReason } from '../../services/reportService';
import { useTranslation } from 'react-i18next';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string;
  itemType: ReportType;
  reportedUserId?: string;
  itemTitle?: string;
}

const getReportReasons = (t: any): { value: ReportReason; label: string; description: string }[] => [
  {
    value: 'inappropriate_content',
    label: t('report.reasons.inappropriate_content.label'),
    description: t('report.reasons.inappropriate_content.description')
  },
  {
    value: 'spam',
    label: t('report.reasons.spam.label'),
    description: t('report.reasons.spam.description')
  },
  {
    value: 'harassment',
    label: t('report.reasons.harassment.label'),
    description: t('report.reasons.harassment.description')
  },
  {
    value: 'hate_speech',
    label: t('report.reasons.hate_speech.label'),
    description: t('report.reasons.hate_speech.description')
  },
  {
    value: 'violence',
    label: t('report.reasons.violence.label'),
    description: t('report.reasons.violence.description')
  },
  {
    value: 'sexual_content',
    label: t('report.reasons.sexual_content.label'),
    description: t('report.reasons.sexual_content.description')
  },
  {
    value: 'copyright',
    label: t('report.reasons.copyright.label'),
    description: t('report.reasons.copyright.description')
  },
  {
    value: 'misinformation',
    label: t('report.reasons.misinformation.label'),
    description: t('report.reasons.misinformation.description')
  },
  {
    value: 'other',
    label: t('report.reasons.other.label'),
    description: t('report.reasons.other.description')
  }
];

const ReportModal: React.FC<ReportModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemType,
  reportedUserId,
  itemTitle
}) => {
  const { t } = useTranslation();
  const [selectedReason, setSelectedReason] = useState<ReportReason | ''>('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedReason) {
      setError(t('report.errors.selectReason'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { error: reportError } = await reportService.createReport({
        reported_item_id: itemId,
        reported_user_id: reportedUserId,
        report_type: itemType,
        reason: selectedReason,
        description: description.trim() || undefined
      });

      if (reportError) {
        throw new Error(reportError.message || t('report.errors.submitFailed'));
      }

      setSubmitted(true);
      
      // Auto-close after 2 seconds
      setTimeout(() => {
        onClose();
        resetForm();
      }, 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : t('report.errors.submitFailed'));
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedReason('');
    setDescription('');
    setSubmitted(false);
    setError(null);
    setLoading(false);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Flag className="w-5 h-5 text-red-500" />
            <h2 className="text-lg font-semibold">{t('report.title', { type: itemType })}</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {submitted ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Flag className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('report.success.title')}
              </h3>
              <p className="text-gray-600">
                {t('report.success.message')}
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Item Info */}
              {itemTitle && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-600 mb-1">{t('report.reporting')}:</p>
                  <p className="font-medium text-gray-900 truncate">{itemTitle}</p>
                </div>
              )}

              {/* Reason Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('report.question', { type: itemType })}
                </label>
                <div className="space-y-2">
                  {getReportReasons(t).map((reason) => (
                    <label
                      key={reason.value}
                      className={`flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedReason === reason.value
                          ? 'border-red-500 bg-red-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="reason"
                        value={reason.value}
                        checked={selectedReason === reason.value}
                        onChange={(e) => setSelectedReason(e.target.value as ReportReason)}
                        className="mt-1 text-red-600 focus:ring-red-500"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{reason.label}</div>
                        <div className="text-sm text-gray-600">{reason.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Additional Details */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional details (optional)
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Provide any additional context that might help us understand the issue..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
                  maxLength={500}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {description.length}/500 characters
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertTriangle className="w-4 h-4 text-red-600 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleClose}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading || !selectedReason}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? t('report.submitting') : t('report.submit')}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportModal;

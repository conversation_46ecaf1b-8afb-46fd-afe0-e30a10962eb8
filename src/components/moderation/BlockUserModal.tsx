import React, { useState } from 'react';
import { X, UserX, AlertTriangle } from 'lucide-react';
import { reportService } from '../../services/reportService';
import { useTranslation } from 'react-i18next';

interface BlockUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  username: string;
  onBlockSuccess?: () => void;
}

const BlockUserModal: React.FC<BlockUserModalProps> = ({
  isOpen,
  onClose,
  userId,
  username,
  onBlockSuccess
}) => {
  const { t } = useTranslation();
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleBlock = async () => {
    setLoading(true);
    setError(null);

    try {
      const { error: blockError } = await reportService.blockUser(
        userId,
        reason.trim() || undefined
      );

      if (blockError) {
        throw new Error(blockError.message || 'Failed to block user');
      }

      onBlockSuccess?.();
      onClose();
      resetForm();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to block user');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setReason('');
    setError(null);
    setLoading(false);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <UserX className="w-5 h-5 text-red-500" />
            <h2 className="text-lg font-semibold">Block User</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-700 mb-4">
              Are you sure you want to block <span className="font-semibold">@{username}</span>?
            </p>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">When you block this user:</p>
                  <ul className="list-disc list-inside space-y-1 text-yellow-700">
                    <li>You won't see their jokes or comments</li>
                    <li>They won't be able to follow you</li>
                    <li>They won't see your content in their feed</li>
                    <li>You can unblock them anytime from your settings</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for blocking (optional)
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Why are you blocking this user? This helps us improve the platform..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
                maxLength={200}
              />
              <div className="text-xs text-gray-500 mt-1">
                {reason.length}/200 characters
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
              <AlertTriangle className="w-4 h-4 text-red-600 flex-shrink-0" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={handleBlock}
              disabled={loading}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Blocking...' : 'Block User'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlockUserModal;

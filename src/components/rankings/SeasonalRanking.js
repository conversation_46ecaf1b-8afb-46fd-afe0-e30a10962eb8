const RANKS = {
  BRONZE: {
    threshold: 0,
    icon: '🥉',
    color: 'from-amber-700 to-amber-900',
    perks: ['Basic joke effects']
  },
  SILVER: {
    threshold: 1000,
    icon: '🥈',
    color: 'from-gray-300 to-gray-400',
    perks: ['Premium effects', 'Custom badge']
  },
  GOLD: {
    threshold: 5000,
    icon: '🥇',
    color: 'from-yellow-400 to-yellow-600',
    perks: ['All effects', 'Priority featuring']
  },
  DIAMOND: {
    threshold: 25000,
    icon: '💎',
    color: 'from-blue-400 to-blue-600',
    perks: ['Custom effects', 'Challenge creation']
  }
};

// Rank Display Component
const RankDisplay = ({ userPoints, currentRank }) => {
  const nextRank = Object.entries(RANKS).find(([_, data]) => 
    data.threshold > userPoints
  );

  return (
    <div className="bg-white rounded-xl shadow-lg p-4">
      <div className="flex items-center space-x-4">
        <div className={`
          w-20 h-20 rounded-full 
          bg-gradient-to-r ${RANKS[currentRank].color}
          flex items-center justify-center text-3xl
        `}>
          {RANKS[currentRank].icon}
        </div>
        
        <div>
          <h2 className="font-bold text-xl">{currentRank}</h2>
          {nextRank && (
            <div className="mt-2">
              <div className="text-sm text-gray-600">Next Rank: {nextRank[0]}</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full"
                  style={{ 
                    width: `${(userPoints / nextRank[1].threshold) * 100}%`
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 
import React, { useState, useEffect } from 'react';
import { rankingService } from '../../lib/supabase/rankings';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { Trophy } from 'lucide-react';

const Leaderboard = () => {
  const { t } = useTranslation();
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoading(true);
        const data = await rankingService.getLeaderboard();
        setLeaderboard(data);
      } catch (err) {
        setError('Failed to fetch leaderboard. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, []);

  if (loading) {
    return <div className="text-center p-10">{t('common.loading')}</div>;
  }

  if (error) {
    return <div className="text-center p-10 text-red-500">{error}</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold text-center mb-6 flex items-center justify-center">
        <Trophy className="w-8 h-8 mr-2 text-yellow-500" />
        Seasonal Leaderboard
      </h1>
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <ul className="divide-y divide-gray-200">
          {leaderboard.map((entry, index) => (
            <li key={entry.id} className={`flex items-center p-4 ${entry.user_id === user?.id ? 'bg-purple-100' : ''}`}>
              <div className="w-12 text-center text-lg font-bold text-gray-500">{index + 1}</div>
              <div className="flex-shrink-0 mr-4">
                <img 
                  className="w-12 h-12 rounded-full object-cover"
                  src={entry.user?.avatar_url || '/path/to/default-avatar.png'} 
                  alt={entry.user?.username}
                />
              </div>
              <div className="flex-grow">
                <div className="font-medium text-lg text-gray-800">{entry.user?.username || 'Anonymous'}</div>
                <div className="text-sm text-gray-500">{entry.rank} - {entry.points} points</div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Leaderboard;

import React from 'react';
import { TrendingUp, Award, Users, CheckCircle } from 'lucide-react';

const ProfileBadge = ({ type }) => {
  const badges = {
    trending: <TrendingUp className="text-orange-500" size={16} />,
    topLikes: <Award className="text-purple-500" size={16} />,
    topFollows: <Users className="text-blue-500" size={16} />,
    verified: <CheckCircle className="text-green-500" size={16} />
  };
  return badges[type];
};

export default ProfileBadge; 
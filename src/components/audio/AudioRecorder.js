import React, { useEffect, useState } from 'react';
import { Mic, Square, Loader, RotateCcw, Wand2 } from 'lucide-react';
import useAudioRecorder from '../../hooks/useAudioRecorder';
import useVoiceEffect from '../../hooks/useVoiceEffect';

const AudioRecorder = ({ onRecordingComplete }) => {
  const {
    isRecording,
    audioURL,
    duration,
    error,
    startRecording: startBaseRecording,
    stopRecording: stopBaseRecording,
    resetRecording
  } = useAudioRecorder();

  const { applyEffect, cleanupEffect, availableEffects } = useVoiceEffect();
  const [selectedEffect, setSelectedEffect] = useState('normal');
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewURL, setPreviewURL] = useState('');

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const modifiedStream = await applyEffect(stream, selectedEffect);
      await startBaseRecording(modifiedStream);
    } catch (err) {
      console.error('Error starting recording:', err);
    }
  };

  const stopRecording = async () => {
    await stopBaseRecording();
    cleanupEffect();
  };

  const handleEffectChange = async (effect) => {
    setSelectedEffect(effect);
    if (isRecording) {
      await stopRecording();
      await startRecording();
    }
  };

  const handleSave = async () => {
    setIsProcessing(true);
    if (onRecordingComplete) {
      await onRecordingComplete(audioURL);
    }
    setIsProcessing(false);
  };

  useEffect(() => {
    return () => {
      if (previewURL) {
        URL.revokeObjectURL(previewURL);
      }
      cleanupEffect();
    };
  }, [previewURL, cleanupEffect]);

  return (
    <div className="bg-white p-6 rounded-xl shadow-md space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Record Your Joke</h2>
        {error && (
          <p className="text-red-500 text-sm">{error}</p>
        )}
      </div>

      <div className="flex items-center justify-center space-x-4">
        <select
          value={selectedEffect}
          onChange={(e) => handleEffectChange(e.target.value)}
          className="px-4 py-2 border rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          disabled={isRecording}
        >
          {availableEffects.map(effect => (
            <option key={effect} value={effect}>
              {effect.charAt(0).toUpperCase() + effect.slice(1)} Voice
            </option>
          ))}
        </select>

        {!isRecording && !audioURL && (
          <button
            onClick={startRecording}
            className="flex items-center space-x-2 bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <Mic className="h-5 w-5" />
            <span>Start Recording</span>
          </button>
        )}

        {isRecording && (
          <button
            onClick={stopRecording}
            className="flex items-center space-x-2 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
          >
            <Square className="h-5 w-5" />
            <span>Stop Recording</span>
          </button>
        )}
      </div>

      {audioURL && !isRecording && (
        <div className="space-y-4">
          <audio src={audioURL} controls className="w-full" />
          <div className="flex justify-center space-x-2">
            <button
              onClick={handleSave}
              disabled={isProcessing}
              className="flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isProcessing ? (
                <Loader className="h-5 w-5 animate-spin" />
              ) : (
                'Save Recording'
              )}
            </button>
            <button
              onClick={resetRecording}
              className="flex items-center space-x-2 bg-gray-200 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors"
            >
              <RotateCcw className="h-5 w-5" />
              <span>Record Again</span>
            </button>
          </div>
        </div>
      )}

      {isRecording && (
        <div className="text-center">
          <div className="text-2xl font-mono text-indigo-600">
            {formatDuration(duration)}
          </div>
          <div className="text-sm text-gray-500">Recording in progress...</div>
          <div className="flex items-center justify-center mt-2 text-indigo-600">
            <Wand2 className="h-5 w-5 animate-pulse mr-2" />
            <span>Using {selectedEffect} effect</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioRecorder;
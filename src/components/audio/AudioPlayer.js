import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, ThumbsUp, Star } from 'lucide-react';

const AudioPlayer = ({ 
  audioUrl, 
  title, 
  author = 'Anonymous',
  rating = 0,
  upvotes = 0,
  onRate,
  onUpvote,
  createdAt 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const audioRef = useRef(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      audio.addEventListener('loadedmetadata', () => {
        setDuration(audio.duration);
      });
      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime);
      });
      audio.addEventListener('ended', () => {
        setIsPlaying(false);
        setCurrentTime(0);
      });

      return () => {
        audio.removeEventListener('loadedmetadata', () => {});
        audio.removeEventListener('timeupdate', () => {});
        audio.removeEventListener('ended', () => {});
      };
    }
  }, []);

  const togglePlay = () => {
    if (audioRef.current.paused) {
      audioRef.current.play();
      setIsPlaying(true);
    } else {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    audioRef.current.muted = !audioRef.current.muted;
    setIsMuted(!isMuted);
  };

  const handleSeek = (e) => {
    const time = (e.target.value / 100) * duration;
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-4 space-y-4">
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500">By {author}</p>
        </div>
        <div className="text-sm text-gray-500">
          {createdAt && formatDate(createdAt)}
        </div>
      </div>

      <div className="space-y-2">
        <audio ref={audioRef} src={audioUrl} preload="metadata" />
        
        <div className="flex items-center space-x-4">
          <button
            onClick={togglePlay}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            {isPlaying ? (
              <Pause className="h-6 w-6 text-gray-700" />
            ) : (
              <Play className="h-6 w-6 text-gray-700" />
            )}
          </button>

          <div className="flex-1">
            <input
              type="range"
              min="0"
              max="100"
              value={(currentTime / duration) * 100 || 0}
              onChange={handleSeek}
              className="w-full h-2 rounded-lg appearance-none cursor-pointer bg-gray-200"
            />
          </div>

          <button
            onClick={toggleMute}
            className="p-2 rounded-full hover:bg-gray-100"
          >
            {isMuted ? (
              <VolumeX className="h-5 w-5 text-gray-700" />
            ) : (
              <Volume2 className="h-5 w-5 text-gray-700" />
            )}
          </button>

          <div className="text-sm text-gray-500 w-20 text-right">
            {formatTime(currentTime)} / {formatTime(duration)}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between pt-2 border-t">
        <div className="flex items-center space-x-4">
          <button
            onClick={onUpvote}
            className="flex items-center space-x-1 text-gray-500 hover:text-indigo-600"
          >
            <ThumbsUp className="h-5 w-5" />
            <span>{upvotes}</span>
          </button>
          
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => onRate && onRate(star)}
                className={`${
                  star <= rating ? 'text-yellow-400' : 'text-gray-300'
                } hover:text-yellow-400`}
              >
                <Star className="h-5 w-5 fill-current" />
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
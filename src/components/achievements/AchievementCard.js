const AchievementCard = ({ achievement }) => {
  return (
    <div className="bg-gray-50 rounded-lg p-6 w-full">
      <div className="text-4xl mb-4">{achievement.icon}</div>
      <h3 className="font-bold text-lg mb-2">{achievement.title}</h3>
      <p className="text-gray-600 mb-3">{achievement.description}</p>
      {achievement.unlocked_at ? (
        <div className="text-green-500 font-medium">
          Unlocked! 🎉
        </div>
      ) : (
        <div className="text-gray-500">
          Progress: {achievement.progress}/{achievement.requirement}
        </div>
      )}
    </div>
  );
}; 
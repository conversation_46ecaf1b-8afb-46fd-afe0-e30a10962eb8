// 1. Achievement System with Visual Badges
const ACHIEVEMENTS = {
  CREATOR: {
    ROOKIE: {
      id: 'rookie',
      title: 'Rookie Joker',
      description: 'Published your first 10 jokes',
      icon: '🎭',
      requirement: 10,
      xpReward: 100,
      badgeColor: 'from-blue-400 to-blue-600'
    },
    STORYTELLER: {
      id: 'storyteller',
      title: 'Master Storyteller',
      description: 'Published 50 quality jokes',
      icon: '📚',
      requirement: 50,
      xpReward: 500,
      badgeColor: 'from-purple-400 to-purple-600'
    },
    LEGEND: {
      id: 'legend',
      title: 'Comedy Legend',
      description: 'Published 1000 jokes',
      icon: '👑',
      requirement: 1000,
      xpReward: 10000,
      badgeColor: 'from-yellow-400 to-yellow-600'
    }
  },
  VIRAL: {
    TRENDSETTER: {
      id: 'trendsetter',
      title: 'Trendsetter',
      description: 'Had 3 jokes trending simultaneously',
      icon: '🌟',
      requirement: 3,
      xpReward: 1000,
      badgeColor: 'from-red-400 to-red-600'
    }
  }
};

// Achievement Badge Component
const AchievementBadge = ({ achievement, progress }) => {
  const isUnlocked = progress >= achievement.requirement;
  
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      className="relative group"
    >
      <div className={`
        w-16 h-16 rounded-full flex items-center justify-center
        bg-gradient-to-r ${achievement.badgeColor}
        ${isUnlocked ? 'opacity-100' : 'opacity-40'}
        shadow-lg
      `}>
        <span className="text-2xl">{achievement.icon}</span>
        {!isUnlocked && (
          <div className="absolute bottom-0 right-0 bg-gray-800 rounded-full px-2 py-1 text-xs text-white">
            {progress}/{achievement.requirement}
          </div>
        )}
      </div>
      
      {/* Tooltip */}
      <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 
                    opacity-0 group-hover:opacity-100 transition-opacity
                    bg-white rounded-lg shadow-xl p-3 w-48 z-50">
        <h3 className="font-bold text-sm">{achievement.title}</h3>
        <p className="text-xs text-gray-600">{achievement.description}</p>
        <div className="text-xs text-purple-600 mt-1">+{achievement.xpReward} XP</div>
      </div>
    </motion.div>
  );
}; 
import React, { useState, useEffect } from 'react';
import { TrendingUp, <PERSON>, Clock, BarChart2 } from 'lucide-react';
import { supabase } from '../../lib/supabase';

const ReactionStats = ({ userReactions = [], customReactions = [] }) => {
  const [activeTab, setActiveTab] = useState('trending');
  const [reactionStats, setReactionStats] = useState([]);
  const [loading, setLoading] = useState(true);

  // Emoji reactions list
  const EMOJI_REACTIONS = ['😂', '🤣', '😅', '😆', '👏', '💯', '🔥', '❤️'];

  useEffect(() => {
    const fetchReactionStats = async () => {
      try {
        // Get reaction counts from database
        const { data: reactions, error } = await supabase
          .from('emoji_reactions')
          .select('emoji, created_at')
          .order('created_at', { ascending: false });

        if (error) throw error;

        // Calculate statistics for each emoji
        const stats = EMOJI_REACTIONS.map(emoji => {
          const emojiReactions = reactions?.filter(r => r.emoji === emoji) || [];
          const count = emojiReactions.length;

          // Calculate trend (reactions in last 24h vs previous 24h)
          const now = new Date();
          const last24h = emojiReactions.filter(r =>
            new Date(r.created_at) > new Date(now.getTime() - 24 * 60 * 60 * 1000)
          ).length;
          const previous24h = emojiReactions.filter(r => {
            const reactionDate = new Date(r.created_at);
            return reactionDate > new Date(now.getTime() - 48 * 60 * 60 * 1000) &&
                   reactionDate <= new Date(now.getTime() - 24 * 60 * 60 * 1000);
          }).length;

          const trend = last24h - previous24h;

          return {
            id: emoji,
            emoji,
            name: `${emoji} Reaction`,
            count,
            trend,
            isIncreasing: trend > 0
          };
        }).sort((a, b) => b.count - a.count);

        setReactionStats(stats);
      } catch (error) {
        console.error('Error fetching reaction stats:', error);
        // Fallback to empty stats
        setReactionStats(EMOJI_REACTIONS.map(emoji => ({
          id: emoji,
          emoji,
          name: `${emoji} Reaction`,
          count: 0,
          trend: 0,
          isIncreasing: false
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchReactionStats();
  }, []);

  // Get top reactions
  const topReactions = reactionStats.slice(0, 3);
  
  // Get trending reactions (positive trend)
  const trendingReactions = reactionStats
    .filter(r => r.trend > 0)
    .sort((a, b) => b.trend - a.trend)
    .slice(0, 3);

  // Get recent reactions
  const recentReactions = [...userReactions]
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    .slice(0, 3);

  const tabs = [
    { id: 'trending', icon: TrendingUp, label: 'الأكثر تفاعلاً' },
    { id: 'top', icon: Star, label: 'الأفضل' },
    { id: 'recent', icon: Clock, label: 'الأخيرة' }
  ];

  const renderReactionList = (reactions) => (
    <div className="space-y-3">
      {reactions.map((reaction, index) => (
        <div
          key={reaction.id}
          className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm transition-all duration-300 hover:scale-102 hover:shadow-md"
          style={{
            animation: `slideIn 0.3s ease-out ${index * 0.1}s both`
          }}
        >
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 flex items-center justify-center rounded-full bg-emerald-100">
              <span className="text-xl">{reaction.icon}</span>
            </div>
            <div>
              <p className="font-medium text-gray-900">{reaction.label}</p>
              <p className="text-sm text-gray-500">
                {reaction.count} تفاعل
              </p>
            </div>
          </div>
          {reaction.trend !== undefined && (
            <div className={`flex items-center gap-1 text-sm ${
              reaction.isIncreasing ? 'text-emerald-600' : 'text-red-500'
            }`}>
              <span>{Math.abs(reaction.trend)}%</span>
              <div className={`transform ${reaction.isIncreasing ? 'rotate-0' : 'rotate-180'}`}>
                ↑
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <div className="bg-gray-50 rounded-xl p-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          إحصائيات التفاعل
        </h3>
        <BarChart2 className="h-5 w-5 text-gray-400" />
      </div>

      {/* Tabs */}
      <div className="flex gap-2 mb-6">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center gap-2 px-4 py-2 rounded-full text-sm transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-emerald-600 text-white shadow-md scale-105'
                : 'bg-white text-gray-600 hover:bg-gray-100'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="relative overflow-hidden">
        <div
          className="transition-all duration-300"
          style={{
            opacity: activeTab === 'trending' ? 1 : 0,
            transform: `translateX(${activeTab === 'trending' ? 0 : 100}%)`,
            position: activeTab === 'trending' ? 'relative' : 'absolute',
            width: '100%'
          }}
        >
          {renderReactionList(trendingReactions)}
        </div>
        <div
          className="transition-all duration-300"
          style={{
            opacity: activeTab === 'top' ? 1 : 0,
            transform: `translateX(${activeTab === 'top' ? 0 : 100}%)`,
            position: activeTab === 'top' ? 'relative' : 'absolute',
            width: '100%'
          }}
        >
          {renderReactionList(topReactions)}
        </div>
        <div
          className="transition-all duration-300"
          style={{
            opacity: activeTab === 'recent' ? 1 : 0,
            transform: `translateX(${activeTab === 'recent' ? 0 : 100}%)`,
            position: activeTab === 'recent' ? 'relative' : 'absolute',
            width: '100%'
          }}
        >
          {renderReactionList(recentReactions)}
        </div>
      </div>

      {/* Custom Reactions Summary */}
      {customReactions.length > 0 && (
        <div className="mt-6 p-4 bg-white rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            ردود فعلك الخاصة
          </h4>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{customReactions.length} رد فعل مسجل</span>
            <span>{customReactions.reduce((acc, r) => acc + (r.plays || 0), 0)} تشغيل</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReactionStats;

// Add these styles to your global CSS
const styles = `
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
`;
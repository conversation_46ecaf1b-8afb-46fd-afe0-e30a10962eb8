import React, { useState, useRef, useEffect } from 'react';
import { Play, Pause, Plus, Mic, X } from 'lucide-react';
import { supabase } from '../../lib/supabase';

const VoiceReactions = ({ jokeId, onReactionAdd }) => {
  const [isPlaying, setIsPlaying] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [customReactionUrl, setCustomReactionUrl] = useState(null);
  const [reactionCounts, setReactionCounts] = useState({});
  const [recentlyPlayed, setRecentlyPlayed] = useState(new Set());
  const [reactions, setReactions] = useState([]);
  const [loading, setLoading] = useState(true);

  // Default emoji reactions
  const EMOJI_REACTIONS = ['😂', '🤣', '😅', '😆', '👏', '💯', '🔥', '❤️'];
  
  const audioRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);

  // Load reactions data
  useEffect(() => {
    const loadReactions = async () => {
      try {
        if (jokeId) {
          // Get reaction counts for this joke
          const { data: jokeReactions, error } = await supabase
            .from('emoji_reactions')
            .select('emoji')
            .eq('joke_id', jokeId);

          if (error) throw error;

          // Count reactions by emoji
          const counts = {};
          jokeReactions?.forEach(reaction => {
            counts[reaction.emoji] = (counts[reaction.emoji] || 0) + 1;
          });

          setReactionCounts(counts);

          // Set up reactions list with counts
          const reactionsWithCounts = EMOJI_REACTIONS.map(emoji => ({
            id: emoji,
            emoji,
            name: `${emoji} Reaction`,
            count: counts[emoji] || 0,
            audioUrl: null // Voice reactions would need audio files
          }));

          setReactions(reactionsWithCounts);
        }
      } catch (error) {
        console.error('Error loading reactions:', error);
        // Fallback to default reactions
        setReactions(EMOJI_REACTIONS.map(emoji => ({
          id: emoji,
          emoji,
          name: `${emoji} Reaction`,
          count: 0,
          audioUrl: null
        })));
      } finally {
        setLoading(false);
      }
    };

    loadReactions();
  }, [jokeId]);

  useEffect(() => {
    // Cleanup function for audio
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const playReaction = (reactionId) => {
    if (isPlaying === reactionId) {
      audioRef.current?.pause();
      setIsPlaying(null);
    } else {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      const reaction = reactions.find(r => r.id === reactionId);
      audioRef.current = new Audio(reaction.audioUrl);
      audioRef.current.onended = () => {
        setIsPlaying(null);
        // Add to recently played and increment count
        if (!recentlyPlayed.has(reactionId)) {
          setRecentlyPlayed(prev => new Set([...prev, reactionId]));
          setReactionCounts(prev => ({
            ...prev,
            [reactionId]: (prev[reactionId] || 0) + 1
          }));
        }
      };
      audioRef.current.play();
      setIsPlaying(reactionId);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm' });
        const url = URL.createObjectURL(blob);
        setCustomReactionUrl(url);
        if (onReactionAdd) {
          onReactionAdd({
            id: 'custom_' + Date.now(),
            audioUrl: url,
            type: 'custom',
            jokeId
          });
        }
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting reaction recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const cancelRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      setCustomReactionUrl(null);
    }
  };

  return (
    <div className="space-y-4" dir="rtl">
      {/* Quick Reactions */}
      <div className="flex flex-wrap items-center gap-2">
        {reactions.map(reaction => (
          <button
            key={reaction.id}
            onClick={() => playReaction(reaction.id)}
            className={`group relative flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-300 
              ${isPlaying === reaction.id 
                ? 'bg-emerald-100 text-emerald-700 scale-105' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              } ${recentlyPlayed.has(reaction.id) ? 'ring-2 ring-emerald-500/30' : ''}`}
          >
            <span className={`text-xl transition-transform duration-300 ${
              isPlaying === reaction.id ? 'animate-bounce' : 'group-hover:scale-110'
            }`}>
              {reaction.icon}
            </span>
            <span className="text-sm font-medium">{reaction.label}</span>
            {isPlaying === reaction.id ? (
              <Pause className="h-4 w-4 animate-pulse" />
            ) : (
              <Play className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            )}
            {/* Reaction Count */}
            <span className="absolute -top-2 -right-2 min-w-[20px] h-5 rounded-full bg-emerald-500 text-white text-xs flex items-center justify-center px-1">
              {reactionCounts[reaction.id]}
            </span>
          </button>
        ))}
      </div>

      {/* Custom Reaction Recording */}
      <div className="flex items-center gap-2">
        {isRecording ? (
          <>
            <div className="flex-1 bg-red-50 rounded-full px-4 py-2 text-red-600 text-sm animate-pulse">
              جاري التسجيل...
            </div>
            <button
              onClick={stopRecording}
              className="p-2 rounded-full bg-emerald-100 text-emerald-700 hover:bg-emerald-200 transition-colors duration-200"
            >
              <Pause className="h-5 w-5" />
            </button>
            <button
              onClick={cancelRecording}
              className="p-2 rounded-full bg-red-100 text-red-700 hover:bg-red-200 transition-colors duration-200"
            >
              <X className="h-5 w-5" />
            </button>
          </>
        ) : (
          <button
            onClick={startRecording}
            className="flex items-center gap-2 px-4 py-2 rounded-full bg-emerald-100 text-emerald-700 hover:bg-emerald-200 transition-all duration-200 hover:scale-105"
          >
            <Mic className="h-4 w-4" />
            <span className="text-sm font-medium">سجل رد فعلك</span>
          </button>
        )}
      </div>

      {/* Custom Reaction Preview */}
      {customReactionUrl && !isRecording && (
        <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-50">
          <div className="flex-1">
            <audio
              src={customReactionUrl}
              controls
              className="w-full h-8 [&::-webkit-media-controls-panel]:!bg-transparent"
            />
          </div>
          <button
            onClick={() => setCustomReactionUrl(null)}
            className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500 transition-colors duration-200"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default VoiceReactions;
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Users } from 'lucide-react';
import { ReactionCounts } from '../../types';

interface ReactionsDisplayProps {
  reactions: ReactionCounts;
  userReactions: string[];
  likesCount: number;
  isLiked: boolean;
  onToggleReaction: (emoji: string) => void;
  onToggleLike: () => void;
  showDetails?: boolean;
  compact?: boolean;
}

const EMOJI_LIST = ['😂', '🤣', '😅', '😆', '👏', '💯', '🔥', '❤️'];

const ReactionsDisplay: React.FC<ReactionsDisplayProps> = ({
  reactions,
  userReactions,
  likesCount,
  isLiked,
  onToggleReaction,
  onToggleLike,
  showDetails = false,
  compact = false
}) => {
  const [showReactionPicker, setShowReactionPicker] = useState(false);

  // Get top reactions (with counts > 0)
  const topReactions = EMOJI_LIST
    .map(emoji => ({ emoji, count: reactions[emoji] || 0 }))
    .filter(r => r.count > 0)
    .sort((a, b) => b.count - a.count)
    .slice(0, compact ? 3 : 5);

  const totalReactions = Object.values(reactions).reduce((sum, count) => sum + count, 0);
  const totalEngagement = likesCount + totalReactions;

  if (compact) {
    return (
      <div className="flex items-center gap-2 text-sm text-gray-600">
        {/* Like count */}
        {likesCount > 0 && (
          <div className="flex items-center gap-1">
            <Heart className="w-4 h-4 text-red-500" />
            <span>{likesCount}</span>
          </div>
        )}

        {/* Top reactions */}
        {topReactions.length > 0 && (
          <div className="flex items-center gap-1">
            {topReactions.map(({ emoji, count }) => (
              <div key={emoji} className="flex items-center gap-0.5">
                <span className="text-base">{emoji}</span>
                <span className="text-xs">{count}</span>
              </div>
            ))}
          </div>
        )}

        {/* Total engagement */}
        {totalEngagement > 0 && (
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Users className="w-3 h-3" />
            <span>{totalEngagement}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Reactions Summary */}
      {(topReactions.length > 0 || likesCount > 0) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Like button and count */}
            <button
              onClick={onToggleLike}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-full transition-all ${
                isLiked
                  ? 'bg-red-50 text-red-600 border border-red-200'
                  : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
              }`}
            >
              <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
              <span className="text-sm font-medium">{likesCount}</span>
            </button>

            {/* Top reactions */}
            {topReactions.length > 0 && (
              <div className="flex items-center gap-2">
                {topReactions.map(({ emoji, count }) => {
                  const hasReacted = userReactions.includes(emoji);
                  return (
                    <button
                      key={emoji}
                      onClick={() => onToggleReaction(emoji)}
                      className={`flex items-center gap-1.5 px-2.5 py-1.5 rounded-full transition-all ${
                        hasReacted
                          ? 'bg-blue-50 text-blue-600 border border-blue-200 scale-105'
                          : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                      }`}
                    >
                      <span className="text-base">{emoji}</span>
                      <span className="text-sm font-medium">{count}</span>
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          {/* Reaction picker toggle */}
          <button
            onClick={() => setShowReactionPicker(!showReactionPicker)}
            className="flex items-center gap-1 px-3 py-1.5 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-full transition-all"
          >
            <span>😊</span>
            <span>Add reaction</span>
          </button>
        </div>
      )}

      {/* Reaction Picker */}
      <AnimatePresence>
        {showReactionPicker && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -10 }}
            className="flex items-center gap-2 p-3 bg-white border border-gray-200 rounded-lg shadow-lg"
          >
            {EMOJI_LIST.map((emoji) => {
              const count = reactions[emoji] || 0;
              const hasReacted = userReactions.includes(emoji);
              
              return (
                <motion.button
                  key={emoji}
                  onClick={() => {
                    onToggleReaction(emoji);
                    setShowReactionPicker(false);
                  }}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  className={`relative flex items-center justify-center w-10 h-10 rounded-full transition-all ${
                    hasReacted
                      ? 'bg-blue-100 ring-2 ring-blue-300'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <span className="text-xl">{emoji}</span>
                  {count > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gray-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {count > 99 ? '99+' : count}
                    </span>
                  )}
                </motion.button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Detailed breakdown (when showDetails is true) */}
      {showDetails && totalReactions > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Reactions ({totalReactions})</h4>
          <div className="grid grid-cols-4 gap-2">
            {EMOJI_LIST.map((emoji) => {
              const count = reactions[emoji] || 0;
              const hasReacted = userReactions.includes(emoji);
              
              if (count === 0) return null;
              
              return (
                <button
                  key={emoji}
                  onClick={() => onToggleReaction(emoji)}
                  className={`flex items-center gap-2 p-2 rounded-lg transition-all ${
                    hasReacted
                      ? 'bg-blue-50 border border-blue-200'
                      : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <span className="text-lg">{emoji}</span>
                  <span className="text-sm font-medium text-gray-700">{count}</span>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Engagement summary */}
      {totalEngagement > 0 && showDetails && (
        <div className="flex items-center gap-4 pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Heart className="w-4 h-4" />
            <span>{likesCount} likes</span>
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <span>😊</span>
            <span>{totalReactions} reactions</span>
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Users className="w-4 h-4" />
            <span>{totalEngagement} total</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReactionsDisplay;

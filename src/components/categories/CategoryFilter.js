import React from 'react';
import { useTranslation } from 'react-i18next';

const categories = [
  { id: 'all', icon: '🎭' },
  { id: 'general', icon: '😄' },
  { id: 'situational', icon: '🎬' },
  { id: 'wordplay', icon: '🔤' },
  { id: 'dark', icon: '🌚' },
  { id: 'family', icon: '👨‍👩‍👧‍👦' },
  { id: 'children', icon: '🧒' },
  { id: 'cultural', icon: '🎨' },
  { id: 'sports', icon: '⚽' },
  { id: 'political', icon: '🗳️' },
  { id: 'social', icon: '🤝' }
];

const CategoryFilter = ({ selectedCategory, onCategoryChange }) => {
  const { t } = useTranslation();
  
  return (
    <div className="relative">
      <div className="overflow-x-auto pb-4 hide-scrollbar">
        <div className="flex space-x-2 px-4 sm:px-0">
          {categories.map(({ id, icon }) => (
            <button
              key={id}
              onClick={() => onCategoryChange(id)}
              className={`
                flex items-center space-x-2 whitespace-nowrap px-4 py-2 rounded-full
                transition-all duration-200 ease-in-out
                ${selectedCategory === id 
                  ? 'bg-purple-600 text-white shadow-lg scale-105' 
                  : 'bg-white text-gray-700 hover:bg-purple-50 shadow-sm'
                }
              `}
            >
              <span className="text-lg">{icon}</span>
              <span className="text-sm font-medium">
                {t(`categories.${id}`)}
              </span>
            </button>
          ))}
        </div>
      </div>
      
      {/* Fade edges for better scroll indication */}
      <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-gray-50 to-transparent pointer-events-none" />
      <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-gray-50 to-transparent pointer-events-none" />
    </div>
  );
};

export default CategoryFilter; 
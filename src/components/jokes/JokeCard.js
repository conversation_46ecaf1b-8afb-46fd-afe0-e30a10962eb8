import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useTranslations } from '../../hooks/useTranslations';
import { useAuth } from '../../contexts/AuthContext';
import { useReactions } from '../../hooks/useReactions';
import { useAsyncOperation } from '../../hooks/useAsyncOperation';
import { Play, Pause, Bookmark, Share2, Flag, Loader, MoreVertical, UserX } from 'lucide-react';

import TimeAgo from '../common/TimeAgo';
import ReportModal from '../moderation/ReportModal';
import BlockUserModal from '../moderation/BlockUserModal';
import ProfileBadge from '../profile/ProfileBadge';
import FollowButton from '../social/FollowButton';

import defaultAvatar from '../../assets/default-avatar.svg';
import { motion, AnimatePresence } from 'framer-motion';

const EMOJI_LIST = ['😂', '🤣', '😅', '😆', '👏', '💯', '🔥', '❤️'];

const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds) || !isFinite(seconds)) {
    return '0:00';
  }
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const MAX_RETRY_ATTEMPTS = 3;

const JokeCard = React.memo(React.forwardRef(({ joke, onLike, currentChallenge }, ref) => {
  const { t, loading: translationsLoading, currentTranslations } = useTranslations();
  const { user } = useAuth();
  const [isLiked, setIsLiked] = useState(joke.is_liked);
  // eslint-disable-next-line no-unused-vars
  const [likesCount, setLikesCount] = useState(joke.likes_count || 0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [retryTimeout, setRetryTimeout] = useState(null);
  const audioRef = useRef(null);
  const progressBarRef = useRef(null);
  const debugRef = useRef({ 
    audioChecked: false,
    translationsLogged: false 
  });
  const [isDragging, setIsDragging] = useState(false);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const dragStartX = useRef(null);
  const dragStartTime = useRef(null);
  const lastVolume = useRef(1);
  const [hasBeenPlayed, setHasBeenPlayed] = useState(false);
  const [particles, setParticles] = useState([]);
  const particleId = useRef(0);
  const lastClickTime = useRef({});

  // Moderation states
  const [showReportModal, setShowReportModal] = useState(false);
  const [showBlockModal, setShowBlockModal] = useState(false);
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  // Use the new reactions hook
  const {
    reactions,
    userReactions,
    // eslint-disable-next-line no-unused-vars
    loading: reactionsLoading,
    error: reactionError,
    toggleReaction
  } = useReactions(joke.id);

  // Async operations for likes and other actions
  // eslint-disable-next-line no-unused-vars
  const { execute: executeLike } = useAsyncOperation();

  // Cleanup function for retry timeout
  useEffect(() => {
    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [retryTimeout]);



  // Add diagnostic check on mount
  useEffect(() => {
    if (joke.audio_url && !debugRef.current.audioChecked) {
      // console.group('🎵 JokeCard Audio Diagnostics');
      // checkBrowserSupport();
      // testAudioPlayback(joke.audio_url);
      // console.groupEnd();
      debugRef.current.audioChecked = true;
    }
  }, [joke.audio_url]);

  // Add translations debugging only once on mount
  useEffect(() => {
    if (!debugRef.current.translationsLogged && !translationsLoading) {
      console.group('🌐 JokeCard Translations Debug');
      console.log('Current translations:', currentTranslations);
      console.log('Translation loading:', translationsLoading);
      console.groupEnd();
      debugRef.current.translationsLogged = true;
    }
  }, [translationsLoading, currentTranslations]);

  // Update audio initialization with proper cleanup
  useEffect(() => {
    let mounted = true;

    const initAudio = async () => {
      if (!audioRef.current || !joke.audio_url) return;

      try {
        // Reset error state
        setError(null);
        
        // Configure audio element
        audioRef.current.preload = 'metadata';
        audioRef.current.src = joke.audio_url;
        
        // Force a reload
        await audioRef.current.load();
      } catch (err) {
        console.error('Audio initialization error:', err);
        if (mounted) {
          setError('Error initializing audio');
        }
      }
    };

    initAudio();

    return () => {
      mounted = false;
      if (audioRef.current) {
        const currentAudioRef = audioRef.current;
        currentAudioRef.pause();
        currentAudioRef.src = '';
        currentAudioRef.load();
      }
    };
  }, [joke.id, joke.audio_url]);

  // Reactions are now handled by the useReactions hook

  // Update play/pause handler
  const handlePlayPause = async () => {
    try {
      if (!audioRef.current) return;

      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        setIsLoading(true);
        try {
          await audioRef.current.play();
          setIsPlaying(true);
          setHasBeenPlayed(true);
        } catch (err) {
          console.error('Play error:', err);
          if (err.name === 'NotAllowedError') {
            setError(t('errors.audio.permission'));
          } else if (err.name === 'NotSupportedError') {
            setError(t('errors.audio.format'));
          } else {
            setError(t('errors.audio.network'));
          }
        } finally {
          setIsLoading(false);
        }
      }
    } catch (err) {
      console.error('PlayPause error:', err);
    }
  };

  const handleTimeUpdate = () => {
    if (!audioRef.current) return;
    setCurrentTime(audioRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    if (!audioRef.current) return;
    setDuration(audioRef.current.duration);
  };

  const handleError = (e) => {
    console.error('Audio error:', e);
    const error = e.target.error;
    
    if (error) {
      let errorMessage;
      switch (error.code) {
        case error.MEDIA_ERR_ABORTED:
          errorMessage = 'Audio playback was aborted';
          break;
        case error.MEDIA_ERR_NETWORK:
          errorMessage = 'Network error occurred during audio playback';
          break;
        case error.MEDIA_ERR_DECODE:
          errorMessage = 'Audio decoding failed';
          break;
        case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'Audio format is not supported';
          break;
        default:
          errorMessage = 'An error occurred during audio playback';
      }
      setError(errorMessage);
    }
    
    setIsPlaying(false);
    setIsLoading(false);
    setIsBuffering(false);
  };

  const handleProgressBarDrag = useCallback((event) => {
    if (!isDragging || !audioRef.current || !progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const width = rect.width;
    const x = Math.max(0, Math.min(event.clientX - rect.left, width));
    const percentage = x / width;
    const newTime = percentage * duration;

    // Update time display but don't seek yet
    setCurrentTime(newTime);
  }, [isDragging, duration]);

  const handleProgressBarMouseUp = useCallback((event) => {
    if (!isDragging || !audioRef.current || !progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const width = rect.width;
    const x = Math.max(0, Math.min(event.clientX - rect.left, width));
    const percentage = x / width;
    const newTime = percentage * duration;

    // Actually seek the audio
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
    setIsDragging(false);

    // Remove window event listeners
    window.removeEventListener('mousemove', handleProgressBarDrag);
    window.removeEventListener('mouseup', handleProgressBarMouseUp);
  }, [isDragging, duration, handleProgressBarDrag]);







  const handleLike = async () => {
    if (!user) {
      window.location.href = '/auth';
      return;
    }

    try {
      // Optimistic update
      const newLikedState = !isLiked;
      setIsLiked(newLikedState);
      setLikesCount(prev => newLikedState ? prev + 1 : prev - 1);

      // Call the jokes service to toggle like in database
      const { jokesService } = await import('../../services/jokes');
      const result = await jokesService.toggleLike(joke.id, user.id);

      if (result.error) {
        throw new Error(result.error.message);
      }

      // Call onLike prop if provided (for parent component updates)
      if (onLike) {
        await onLike(joke.id, newLikedState);
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      // Revert optimistic update on error
      setIsLiked(isLiked);
      setLikesCount(prev => isLiked ? prev + 1 : prev - 1);
    }
  };

  // Share handler with multiple options
  const handleShare = async () => {
    const shareText = `Check out this joke: ${joke.title}`;
    const shareUrl = window.location.href;

    try {
      if (navigator.share) {
        await navigator.share({
          title: joke.title,
          text: shareText,
          url: shareUrl
        });
      } else {
        // Show share options modal
        setShowShareModal(true);
      }
    } catch (error) {
      console.error('Error sharing:', error);
      // Fallback to share modal
      setShowShareModal(true);
    }
  };

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${joke.title}\n\n${window.location.href}`);
    window.open(`https://wa.me/?text=${text}`, '_blank');
    setShowShareModal(false);
  };

  const shareToFacebook = () => {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
    setShowShareModal(false);
  };

  const shareToTwitter = () => {
    const text = encodeURIComponent(joke.title);
    const url = encodeURIComponent(window.location.href);
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank');
    setShowShareModal(false);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      console.log('Link copied to clipboard!');
      setShowShareModal(false);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  // Moderation handlers
  const handleReport = () => {
    if (!user) {
      window.location.href = '/auth';
      return;
    }
    setShowOptionsMenu(false);
    setShowReportModal(true);
  };

  const handleBlockUser = () => {
    if (!user) {
      window.location.href = '/auth';
      return;
    }
    setShowOptionsMenu(false);
    setShowBlockModal(true);
  };

  const handleBlockSuccess = () => {
    // Optionally hide the joke card or show a message
    console.log('User blocked successfully');
  };

  const handleReaction = async (emoji, event) => {
    if (!user) {
      return;
    }

    // Prevent event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Debounce rapid clicks - prevent multiple reactions within 500ms
    const now = Date.now();
    const lastClickKey = `${joke.id}-${emoji}`;
    if (lastClickTime.current[lastClickKey] && now - lastClickTime.current[lastClickKey] < 500) {
      console.log('Debouncing rapid click for:', emoji);
      return;
    }
    lastClickTime.current[lastClickKey] = now;

    console.log('Clicked emoji:', emoji, 'Event:', event); // Debug log

    // Add particle effect
    const newParticle = {
      id: particleId.current++,
      emoji,
      x: event?.clientX || 0,
      y: event?.clientY || 0
    };
    setParticles(prev => [...prev, newParticle]);

    // Use the new toggleReaction hook which handles optimistic updates
    await toggleReaction(emoji);
  };

  const removeParticle = (id) => {
    setParticles(prev => prev.filter(p => p.id !== id));
  };

  // Update audio element when volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  useEffect(() => {
    // Cleanup audio on unmount
    return () => {
      if (audioRef.current) {
        const currentAudioRef = audioRef.current;
        currentAudioRef.pause();
        currentAudioRef.src = '';
        currentAudioRef.load();
      }
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [retryTimeout]);

  // Add preload check
  useEffect(() => {
    if (audioRef.current && joke.audio_url) {
      audioRef.current.preload = 'metadata';
      audioRef.current.load();
    }
  }, [joke.audio_url]);

  // Close options menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showOptionsMenu && !event.target.closest('.options-menu')) {
        setShowOptionsMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showOptionsMenu]);

  return (
    <div ref={ref} className="bg-white rounded-xl shadow-[0_2px_8px_rgba(0,0,0,0.1)] hover:shadow-[0_4px_12px_rgba(0,0,0,0.15)] 
                             transition-shadow duration-300 ease-in-out
                             p-3 sm:p-6 w-full sm:w-[95%] sm:max-w-xl mx-auto mb-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <Link
          to={`/profile/${joke.user_id || joke.profiles?.id}`}
          className="flex items-center space-x-2 sm:space-x-3 rtl:space-x-reverse group flex-1 min-w-0"
        >
          <div className="relative flex-shrink-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full overflow-hidden bg-gray-100">
              {joke.profiles?.avatar_url ? (
                <img
                  src={joke.profiles.avatar_url}
                  alt={joke.profiles?.username || t('user.anonymous')}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = defaultAvatar;
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-purple-100 text-purple-600">
                  <span className="text-lg font-medium">
                    {(joke.profiles?.username || t('user.anonymous')).charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="absolute -top-1 -right-1 flex space-x-0.5 rtl:space-x-reverse">
              {joke.profiles?.isTopLikes && <ProfileBadge type="topLikes" />}
              {joke.profiles?.isTopFollows && <ProfileBadge type="topFollows" />}
              {joke.profiles?.isVerified && <ProfileBadge type="verified" />}
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2 rtl:space-x-reverse flex-wrap">
              <span className="text-sm text-gray-500 group-hover:text-purple-600 transition-colors truncate">
                {joke.profiles?.username || t('user.anonymous')}
              </span>
              <span className="text-xs px-2 py-0.5 bg-purple-50 text-purple-700 rounded-full whitespace-nowrap">
                {joke.category}
              </span>
            </div>
            <div className="text-xs text-gray-500 mt-0.5">
              <TimeAgo date={joke.created_at} />
            </div>
          </div>
        </Link>
        <div className="flex-shrink-0 ms-2 sm:ms-4">
          <FollowButton
            userId={joke.user_id}
            initialIsFollowing={joke.profiles?.isFollowing}
          />
        </div>
      </div>

      {/* Title */}
      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 sm:mb-6 line-clamp-2 px-1">{joke.title}</h3>

      {/* Player */}
      <div className="relative flex flex-col items-center my-6 sm:my-8">
        {/* Time Display */}
        <div className="w-full flex justify-between items-center text-xs text-gray-500 mb-4">
          <span>{formatTime(currentTime)}</span>
          <span>{isBuffering ? t('jokeBox.buffering') : formatTime(duration)}</span>
        </div>

        {/* Controls */}
        <div className="relative flex justify-center items-center w-full">
          {/* Like Button */}
          <button
            onClick={handleLike}
            className="absolute -left-1 sm:left-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full 
                     bg-white shadow-[0_2px_6px_rgba(0,0,0,0.12)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.15)]
                     flex items-center justify-center text-purple-600 
                     hover:bg-purple-50 transition-all duration-300 z-30"
          >
            <Bookmark className={isLiked ? 'fill-purple-600' : ''} size={20} />
          </button>

          {/* Play Button Container */}
          <div className="relative">
            {/* Play Button */}
            <button
              onClick={handlePlayPause}
              disabled={isLoading}
              className="relative w-24 h-24 sm:w-28 sm:h-28 bg-purple-600 hover:bg-purple-700 
                       text-white rounded-full flex items-center justify-center 
                       shadow-[0_4px_12px_rgba(147,51,234,0.3)] hover:shadow-[0_6px_16px_rgba(147,51,234,0.4)]
                       transition-all duration-300 z-40
                       disabled:opacity-75 disabled:cursor-not-allowed
                       group"
            >
              {isLoading ? (
                <Loader className="w-8 h-8 animate-spin" />
              ) : isPlaying ? (
                <Pause size={32} />
              ) : (
                <Play size={32} />
              )}
            </button>

            {/* Emoji Reactions */}
            <div className="absolute inset-0 pointer-events-none">
              {EMOJI_LIST.map((emoji, index) => {
                const angle = (index / EMOJI_LIST.length) * Math.PI * 2;
                const baseRadius = window.innerWidth < 640 ? 70 : 90;
                const radius = hasBeenPlayed 
                  ? userReactions.includes(emoji) 
                    ? baseRadius - 15  // Closer when reacted
                    : baseRadius + 15  // Further when played but not reacted
                  : baseRadius;        // Default position
                const x = Math.cos(angle) * radius;
                const y = Math.sin(angle) * radius;
                const count = reactions[emoji] || 0;
                const hasReacted = userReactions.includes(emoji);
                
                return (
                  <div
                    key={emoji}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: `calc(50% + ${x}px)`,
                      top: `calc(50% + ${y}px)`,
                      transition: 'all 0.3s ease-out'
                    }}
                  >
                    <motion.button
                      onClick={(event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('Emoji button clicked:', emoji);
                        handleReaction(emoji, event);
                      }}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full 
                               bg-white shadow-[0_2px_4px_rgba(0,0,0,0.08)] hover:shadow-[0_3px_6px_rgba(0,0,0,0.12)]
                               flex items-center justify-center text-base sm:text-xl cursor-pointer
                               hover:bg-purple-50 transition-all duration-300
                               pointer-events-auto z-20
                               ${hasReacted ? 'bg-purple-50 scale-110' : ''}
                               ${hasBeenPlayed ? 'opacity-90' : 'opacity-100'}`}
                      disabled={!user}
                      title={!user ? t('errors.auth.required') : ''}
                    >
                      {emoji}
                      <AnimatePresence>
                        {count > 0 && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                            className={`absolute -top-2 -right-2 min-w-[20px] h-5 
                                     bg-purple-600 text-white text-xs rounded-full 
                                     flex items-center justify-center px-1
                                     ${hasReacted ? 'opacity-100' : 'opacity-75'}`}
                          >
                            {count}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </motion.button>
                  </div>
                );
              })}

              {/* Emoji Particles */}
              <AnimatePresence>
                {particles.map(particle => (
                  <motion.div
                    key={particle.id}
                    initial={{ 
                      x: particle.x,
                      y: particle.y,
                      scale: 1,
                      opacity: 1 
                    }}
                    animate={{ 
                      y: particle.y - 100,
                      scale: 1.5,
                      opacity: 0 
                    }}
                    exit={{ opacity: 0 }}
                    transition={{ 
                      duration: 0.7,
                      ease: "easeOut" 
                    }}
                    onAnimationComplete={() => removeParticle(particle.id)}
                    className="fixed pointer-events-none text-xl z-50"
                  >
                    {particle.emoji}
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </div>

          {/* Share Button */}
          <button
            onClick={handleShare}
            className="absolute -right-1 sm:right-0 -top-12 sm:-top-14 w-10 h-10 sm:w-12 sm:h-12 rounded-full
                     bg-white shadow-[0_2px_6px_rgba(0,0,0,0.12)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.15)]
                     flex items-center justify-center text-blue-600
                     hover:bg-blue-50 transition-all duration-300 z-30"
          >
            <Share2 size={20} />
          </button>

          {/* Options Menu - positioned at bottom right */}
          <div className="relative options-menu">
            <button
              onClick={() => setShowOptionsMenu(!showOptionsMenu)}
              className="absolute -right-1 sm:right-0 bottom-4 sm:bottom-6 w-10 h-10 sm:w-12 sm:h-12 rounded-full
                       bg-white shadow-[0_2px_6px_rgba(0,0,0,0.12)] hover:shadow-[0_4px_8px_rgba(0,0,0,0.15)]
                       flex items-center justify-center text-gray-600
                       hover:bg-gray-50 transition-all duration-300 z-30"
            >
              <MoreVertical size={20} />
            </button>

            {/* Options Dropdown */}
            {showOptionsMenu && (
              <div className="absolute right-0 top-12 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[160px] z-40">
                <button
                  onClick={handleReport}
                  className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                >
                  <Flag size={16} />
                  {t('common.report')}
                </button>
                {joke.user_id !== user?.id && (
                  <button
                    onClick={handleBlockUser}
                    className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                  >
                    <UserX size={16} />
                    {t('profile.block')}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-4 text-sm text-red-600 text-center bg-red-50 px-4 py-2 rounded-lg">
            {error}
            <button 
              onClick={() => setError(null)}
              className="ml-2 text-red-700 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        )}

        {/* Reaction Error Message */}
        {reactionError && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 
                         bg-red-100 text-red-600 text-sm px-3 py-1 rounded-lg shadow-sm">
            {reactionError}
          </div>
        )}
      </div>

      {/* Moderation Modals */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        itemId={joke.id}
        itemType="joke"
        reportedUserId={joke.user_id}
        itemTitle={joke.title}
      />

      <BlockUserModal
        isOpen={showBlockModal}
        onClose={() => setShowBlockModal(false)}
        userId={joke.user_id}
        username={joke.profiles?.username || 'Unknown User'}
        onBlockSuccess={handleBlockSuccess}
      />

      {/* Share Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">{t('common.share')}</h3>
            <div className="space-y-3">
              <button
                onClick={shareToWhatsApp}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  W
                </div>
                WhatsApp
              </button>
              <button
                onClick={shareToFacebook}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  f
                </div>
                Facebook
              </button>
              <button
                onClick={shareToTwitter}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  T
                </div>
                Twitter
              </button>
              <button
                onClick={copyToClipboard}
                className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  📋
                </div>
                {t('common.copyLink')}
              </button>
            </div>
            <button
              onClick={() => setShowShareModal(false)}
              className="w-full mt-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              {t('common.cancel')}
            </button>
          </div>
        </div>
      )}

      <audio
        ref={audioRef}
        preload="metadata"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
        onError={handleError}
        className="hidden"
      >
        <source src={joke.audio_url} type="audio/mpeg" />
        <source src={joke.audio_url} type="audio/mp4" />
        <source src={joke.audio_url} type="audio/webm" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
}));

// Add display name for debugging
JokeCard.displayName = 'JokeCard';

export default JokeCard;
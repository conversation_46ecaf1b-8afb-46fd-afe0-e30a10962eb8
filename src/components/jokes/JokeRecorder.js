import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Mic, Square, Loader } from 'lucide-react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';

const categories = [
  { id: 'general', icon: '😄' },
  { id: 'situational', icon: '🎬' },
  { id: 'wordplay', icon: '🔤' },
  { id: 'dark', icon: '🌚' },
  { id: 'family', icon: '👨‍👩‍👧‍👦' },
  { id: 'children', icon: '🧒' },
  { id: 'cultural', icon: '🎨' },
  { id: 'sports', icon: '⚽' },
  { id: 'political', icon: '🗳️' },
  { id: 'social', icon: '🤝' }
];

const JokeRecorder = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState(null);
  const [title, setTitle] = useState('');
  const [category, setCategory] = useState('general');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const mediaRecorder = useRef(null);
  const audioChunks = useRef([]);
  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimer = useRef(null);
  const MAX_RECORDING_TIME = 180; // 3 minutes in seconds

  useEffect(() => {
    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorder.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      audioChunks.current = [];
      
      mediaRecorder.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.current.push(event.data);
        }
      };

      mediaRecorder.current.start(100);
      setIsRecording(true);
      setError(null);
      setAudioBlob(null);

      // Start recording timer
      setRecordingTime(0);
      recordingTimer.current = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= MAX_RECORDING_TIME) {
            stopRecording();
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    } catch (err) {
      console.error('Error starting recording:', err);
      setError(t('errors.microphone.access'));
    }
  };

  const stopRecording = () => {
    if (mediaRecorder.current && isRecording) {
      mediaRecorder.current.stop();
      mediaRecorder.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      // Create the blob with proper MIME type
      const blob = new Blob(audioChunks.current, { 
        type: 'audio/webm;codecs=opus' 
      });
      setAudioBlob(blob);
      audioChunks.current = [];
      
      clearInterval(recordingTimer.current);
      setRecordingTime(0);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!user) {
      navigate('/auth', { state: { from: '/record' } });
      return;
    }

    if (!audioBlob || !title.trim()) {
      setError(t('errors.form.required'));
      return;
    }

    try {
      setUploading(true);
      setError(null);

      // Create a unique filename with user ID and timestamp
      const fileName = `${user.id}/${Date.now()}.webm`;

      // Upload audio file with correct MIME type
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('jokes')
        .upload(fileName, audioBlob, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'audio/webm;codecs=opus'
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('jokes')
        .getPublicUrl(fileName);

      // Create joke record
      const { error: insertError } = await supabase
        .from('jokes')
        .insert({
          title: title.trim(),
          audio_url: publicUrl,
          category,
          created_at: new Date().toISOString()
        });

      if (insertError) throw insertError;

      // Success! Redirect to home
      navigate('/');
    } catch (err) {
      console.error('Error uploading:', err);
      setError(t('errors.upload.failed'));
    } finally {
      setUploading(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title Input */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700">
            {t('recorder.title')} *
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            placeholder={t('recorder.titlePlaceholder')}
            disabled={uploading}
          />
        </div>

        {/* Category Selection */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700">
            {t('recorder.category')} *
          </label>
          <div className="mt-1 grid grid-cols-2 sm:grid-cols-3 gap-2">
            {categories.map(({ id, icon }) => (
              <button
                key={id}
                type="button"
                onClick={() => setCategory(id)}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-lg
                  transition-all duration-200 ease-in-out
                  ${category === id 
                    ? 'bg-purple-600 text-white shadow-lg scale-105' 
                    : 'bg-white text-gray-700 hover:bg-purple-50 shadow-sm border border-gray-200'
                  }
                `}
              >
                <span className="text-lg">{icon}</span>
                <span className="text-sm font-medium">
                  {t(`categories.${id}`)}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Recording Controls */}
        <div className="text-center space-y-4">
          {/* Timer Display */}
          {isRecording && (
            <div className="text-lg font-mono text-purple-600">
              {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}
            </div>
          )}

          {/* Record/Stop Button */}
          <div className="flex justify-center">
            <button
              type="button"
              onClick={isRecording ? stopRecording : startRecording}
              disabled={uploading}
              className={`
                p-6 rounded-full shadow-lg transition-all duration-300
                ${isRecording
                  ? 'bg-red-600 hover:bg-red-700 animate-pulse'
                  : 'bg-purple-600 hover:bg-purple-700'
                }
                text-white
                disabled:opacity-50 disabled:cursor-not-allowed
              `}
            >
              {isRecording ? (
                <Square className="h-8 w-8" />
              ) : (
                <Mic className="h-8 w-8" />
              )}
            </button>
          </div>

          {/* Audio Preview */}
          {audioBlob && (
            <audio
              src={URL.createObjectURL(audioBlob)}
              controls
              className="w-full"
            />
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded">
            {error}
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!audioBlob || !title.trim() || uploading}
            className={`
              px-6 py-2 rounded-lg text-white
              transition-all duration-200
              ${!audioBlob || !title.trim() || uploading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-purple-600 hover:bg-purple-700'
              }
            `}
          >
            {uploading ? (
              <div className="flex items-center space-x-2">
                <Loader className="animate-spin h-5 w-5" />
                <span>{t('recorder.uploading')}</span>
              </div>
            ) : (
              t('recorder.submit')
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default JokeRecorder; 
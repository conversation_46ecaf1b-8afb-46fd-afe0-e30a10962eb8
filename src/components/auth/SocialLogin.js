import React from 'react';

const SocialLogin = ({ onLogin }) => {
  const socialProviders = [
    {
      id: 'google',
      name: 'Google',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12.545,12.151L12.545,12.151c0,1.054,0.855,1.909,1.909,1.909h3.536c-0.367,1.722-1.505,3.18-3.091,3.91 c-1.031,0.473-2.215,0.636-3.344,0.462c-1.129-0.174-2.167-0.68-2.973-1.486c-0.806-0.806-1.312-1.844-1.486-2.973 C6.87,12.844,7.033,11.66,7.506,10.629c0.73-1.586,2.188-2.724,3.91-3.091V11.1C11.416,11.465,11.891,11.808,12.545,12.151"
          />
          <path
            fill="currentColor"
            d="M11.416,7.538v3.562c-0.654-0.343-1.129-0.686-1.129-1.051V7.538c0-0.365,0.475-0.708,1.129-1.051V4.447 c-1.722,0.367-3.18,1.505-3.91,3.091C6.87,8.569,6.707,9.753,6.881,10.882c0.174,1.129,0.68,2.167,1.486,2.973 c0.806,0.806,1.844,1.312,2.973,1.486c1.129,0.174,2.313,0.011,3.344-0.462c1.586-0.73,2.724-2.188,3.091-3.91h-3.536 c-1.054,0-1.909-0.855-1.909-1.909c0-0.343,0.475-0.686,1.129-1.051V4.447C11.891,4.829,11.416,5.172,11.416,7.538"
          />
        </svg>
      ),
      bgColor: 'bg-white',
      textColor: 'text-gray-700',
      hoverBg: 'hover:bg-gray-50',
      borderColor: 'border-gray-300'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
          />
        </svg>
      ),
      bgColor: 'bg-[#1877F2]',
      textColor: 'text-white',
      hoverBg: 'hover:bg-[#0c60d3]',
      borderColor: 'border-transparent'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.53 4.08M13 7.13C12.8 5.24 14.34 3.72 16.13 3.5c.21 2.06-1.76 3.61-3.13 3.63z"
          />
        </svg>
      ),
      bgColor: 'bg-black',
      textColor: 'text-white',
      hoverBg: 'hover:bg-gray-900',
      borderColor: 'border-transparent'
    }
  ];

  const handleSocialLogin = (providerId) => {
    // TODO: Implement social login with Supabase
    console.log(`Logging in with ${providerId}`);
    if (onLogin) {
      onLogin(providerId);
    }
  };

  return (
    <div className="space-y-3">
      {socialProviders.map(provider => (
        <button
          key={provider.id}
          onClick={() => handleSocialLogin(provider.id)}
          className={`w-full flex items-center justify-center gap-3 px-4 py-2.5 border ${provider.borderColor} rounded-xl font-medium ${provider.bgColor} ${provider.textColor} ${provider.hoverBg} transition-colors duration-200 group`}
        >
          <span className="transform transition-transform duration-200 group-hover:scale-110">
            {provider.icon}
          </span>
          <span>متابعة مع {provider.name}</span>
        </button>
      ))}

      {/* Divider */}
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-4 bg-white text-gray-500">أو</span>
        </div>
      </div>
    </div>
  );
};

export default SocialLogin;
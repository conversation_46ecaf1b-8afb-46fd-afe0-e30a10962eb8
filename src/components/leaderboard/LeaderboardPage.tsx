import React, { useState, useEffect, useCallback } from 'react';
import { Trophy, Medal, Crown, TrendingUp, Calendar, Users, Zap } from 'lucide-react';
import { leaderboardService, LeaderboardEntry, LeaderboardFilters } from '../../services/leaderboardService';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';

const LeaderboardPage: React.FC = () => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [userRank, setUserRank] = useState<LeaderboardEntry | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LeaderboardFilters>({
    timeframe: 'weekly',
    category: 'overall',
    limit: 50
  });

  // Load leaderboard data
  const loadLeaderboard = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: leaderboardData, error: leaderboardError } = await leaderboardService.getLeaderboard(filters);

      if (leaderboardError) {
        throw leaderboardError;
      }

      setLeaderboard(leaderboardData || []);

      // Load user's rank if authenticated
      if (user) {
        const { data: userRankData } = await leaderboardService.getUserRank(user.id, filters.category);
        setUserRank(userRankData);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : t('leaderboard.error'));
    } finally {
      setLoading(false);
    }
  }, [filters, user, t]);

  useEffect(() => {
    loadLeaderboard();
  }, [filters, user, loadLeaderboard]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Medal className="w-6 h-6 text-amber-600" />;
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankBgColor = (rank: number, isCurrentUser: boolean) => {
    if (isCurrentUser) return 'bg-blue-50 border-blue-200';
    if (rank === 1) return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200';
    if (rank === 2) return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200';
    if (rank === 3) return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200';
    return 'bg-white border-gray-200';
  };

  const formatScore = (score: number) => {
    if (score >= 1000000) return `${(score / 1000000).toFixed(1)}M`;
    if (score >= 1000) return `${(score / 1000).toFixed(1)}K`;
    return score.toString();
  };

  const timeframeOptions = [
    { value: 'daily', label: t('leaderboard.timeframes.daily'), icon: Calendar },
    { value: 'weekly', label: t('leaderboard.timeframes.weekly'), icon: Calendar },
    { value: 'monthly', label: t('leaderboard.timeframes.monthly'), icon: Calendar },
    { value: 'all-time', label: t('leaderboard.timeframes.all-time'), icon: Trophy }
  ];

  const categoryOptions = [
    { value: 'overall', label: t('leaderboard.categories.overall'), icon: Trophy },
    { value: 'engagement', label: t('leaderboard.categories.engagement'), icon: Users },
    { value: 'consistency', label: t('leaderboard.categories.consistency'), icon: Calendar },
    { value: 'trending', label: t('leaderboard.categories.trending'), icon: TrendingUp }
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Trophy className="w-8 h-8 text-yellow-500" />
          <h1 className="text-3xl font-bold text-gray-900">{t('leaderboard.title')}</h1>
        </div>
        <p className="text-gray-600">{t('leaderboard.subtitle')}</p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Timeframe Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">{t('leaderboard.timePeriod')}</label>
            <div className="grid grid-cols-2 gap-2">
              {timeframeOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => setFilters(prev => ({ ...prev, timeframe: option.value as any }))}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                      filters.timeframe === option.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {option.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">{t('leaderboard.category')}</label>
            <div className="grid grid-cols-2 gap-2">
              {categoryOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.value}
                    onClick={() => setFilters(prev => ({ ...prev, category: option.value as any }))}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                      filters.category === option.value
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {option.label}
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* User's Rank Card */}
      {userRank && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                {getRankIcon(userRank.rank)}
                <div>
                  <h3 className="font-semibold text-gray-900">Your Rank</h3>
                  <p className="text-sm text-gray-600">#{userRank.rank} out of thousands</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">{formatScore(userRank.score)}</div>
                <div className="text-sm text-gray-600">points</div>
              </div>
            </div>
            <div className="flex gap-2">
              {userRank.badges.slice(0, 3).map((badge, index) => (
                <span key={index} className="text-lg" title={badge}>
                  {badge.split(' ')[0]}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('leaderboard.loading')}</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
          <p className="text-red-700">{error}</p>
          <button
            onClick={loadLeaderboard}
            className="mt-2 text-red-600 hover:text-red-700 font-medium"
          >
            {t('leaderboard.tryAgain')}
          </button>
        </div>
      )}

      {/* Leaderboard */}
      {!loading && !error && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              {categoryOptions.find(c => c.value === filters.category)?.label} {t('leaderboard.rankings')}
            </h2>
            <p className="text-sm text-gray-600">
              {timeframeOptions.find(t => t.value === filters.timeframe)?.label}
            </p>
          </div>

          <div className="divide-y divide-gray-200">
            {leaderboard.length === 0 ? (
              <div className="text-center py-12">
                <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('leaderboard.noRankings')}</h3>
                <p className="text-gray-600">{t('leaderboard.noRankingsDesc')}</p>
              </div>
            ) : (
              leaderboard.map((entry) => (
                <div
                  key={entry.user_id}
                  className={`px-6 py-4 transition-all hover:bg-gray-50 ${getRankBgColor(entry.rank, entry.is_current_user || false)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      {/* Rank */}
                      <div className="flex-shrink-0">
                        {getRankIcon(entry.rank)}
                      </div>

                      {/* User Info */}
                      <div className="flex items-center gap-3">
                        <img
                          src={entry.avatar_url || '/default-avatar.svg'}
                          alt={entry.username}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <div className="font-medium text-gray-900">
                            {entry.username}
                            {entry.is_current_user && (
                              <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                You
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-600">
                            {entry.total_jokes} jokes • {entry.total_followers} followers
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-6">
                      {/* Badges */}
                      <div className="hidden md:flex gap-1">
                        {entry.badges.slice(0, 3).map((badge, index) => (
                          <span key={index} className="text-lg" title={badge}>
                            {badge.split(' ')[0]}
                          </span>
                        ))}
                      </div>

                      {/* Stats */}
                      <div className="text-right">
                        <div className="text-xl font-bold text-gray-900">
                          {formatScore(entry.score)}
                        </div>
                        <div className="text-sm text-gray-600">{t('leaderboard.points')}</div>
                      </div>

                      {/* Engagement indicator */}
                      <div className="hidden lg:block">
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Zap className="w-4 h-4" />
                          <span>{entry.total_likes + entry.total_reactions + entry.total_comments}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Call to Action */}
      {!user && (
        <div className="text-center mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('leaderboard.joinCompetition')}</h3>
          <p className="text-gray-600 mb-4">{t('leaderboard.joinDesc')}</p>
          <a
            href="/auth"
            className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Trophy className="w-5 h-5" />
            {t('leaderboard.signUp')}
          </a>
        </div>
      )}
    </div>
  );
};

export default LeaderboardPage;

// 7. Community Recognition System
const RECOGNITION_CATEGORIES = {
  WEEKLY_SPOTLIGHT: {
    title: 'Creator of the Week',
    icon: '🌟',
    criteria: 'Most engaging content this week'
  },
  HALL_OF_FAME: {
    title: 'Hall of Fame',
    icon: '👑',
    criteria: 'All-time greatest creators'
  },
  RISING_STAR: {
    title: 'Rising Star',
    icon: '⭐',
    criteria: 'Fastest growing new creator'
  }
};

// Spotlight Creator Component
const SpotlightCreator = ({ creator, category }) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className="bg-white rounded-xl shadow-lg p-6 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute top-0 right-0 w-40 h-40 opacity-5">
        <Pattern />
      </div>
      
      {/* Category Badge */}
      <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-500 to-purple-600
                    text-white px-3 py-1 rounded-full text-sm font-medium">
        {RECOGNITION_CATEGORIES[category].icon} {RECOGNITION_CATEGORIES[category].title}
      </div>
      
      {/* Creator Info */}
      <div className="flex items-center space-x-4">
        <img 
          src={creator.avatar} 
          className="w-16 h-16 rounded-full ring-4 ring-purple-100"
        />
        <div>
          <h3 className="font-bold text-lg">{creator.username}</h3>
          <p className="text-sm text-gray-600">{creator.tagline}</p>
        </div>
      </div>
      
      {/* Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 text-center">
        <Stat label="Jokes" value={creator.jokeCount} />
        <Stat label="Followers" value={creator.followerCount} />
        <Stat label="Trending" value={creator.trendingCount} />
      </div>
      
      {/* Featured Joke Preview */}
      {creator.featuredJoke && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium mb-2">Featured Joke</div>
          <div className="text-gray-600">{creator.featuredJoke.title}</div>
        </div>
      )}
    </motion.div>
  );
}; 
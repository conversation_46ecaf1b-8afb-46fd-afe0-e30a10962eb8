import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';

const ConnectionMonitor = () => {
  const [connectionInfo, setConnectionInfo] = useState({
    channels: 0,
    status: 'unknown',
    lastUpdate: new Date().toLocaleTimeString()
  });

  useEffect(() => {
    const updateConnectionInfo = () => {
      try {
        const channels = supabase.getChannels();
        const status = supabase.realtime.isConnected() ? 'connected' : 'disconnected';
        
        setConnectionInfo({
          channels: channels.length,
          status,
          lastUpdate: new Date().toLocaleTimeString()
        });

        // Log warning if too many channels
        if (channels.length > 5) {
          console.warn('🚨 High channel count:', channels.length);
        }
      } catch (error) {
        console.error('Connection monitor error:', error);
      }
    };

    // Update immediately
    updateConnectionInfo();

    // Update every 5 seconds
    const interval = setInterval(updateConnectionInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-75 text-white text-xs p-2 rounded z-50">
      <div>🔌 Status: {connectionInfo.status}</div>
      <div>📡 Channels: {connectionInfo.channels}</div>
      <div>🕐 Updated: {connectionInfo.lastUpdate}</div>
      {connectionInfo.channels > 5 && (
        <div className="text-red-400">⚠️ High channel count!</div>
      )}
    </div>
  );
};

export default ConnectionMonitor;

import React, { useState } from 'react';
import { supabase } from '../../lib/supabase';
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface ConnectionStatus {
  status: 'idle' | 'testing' | 'success' | 'error';
  message: string;
  details?: any;
}

const ConnectionTest: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    status: 'idle',
    message: 'Click to test connection'
  });

  const testConnection = async () => {
    setConnectionStatus({ status: 'testing', message: 'Testing connection...' });

    try {
      // Test 1: Basic connection
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);

      if (error) {
        setConnectionStatus({
          status: 'error',
          message: `Database connection failed: ${error.message}`,
          details: error
        });
        return;
      }

      // Test 2: Auth service
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        setConnectionStatus({
          status: 'error',
          message: `Auth service failed: ${authError.message}`,
          details: authError
        });
        return;
      }

      setConnectionStatus({
        status: 'success',
        message: 'All services connected successfully!',
        details: {
          database: 'Connected',
          auth: 'Connected',
          url: process.env.REACT_APP_SUPABASE_URL,
          session: authData.session ? 'Active' : 'No session'
        }
      });

    } catch (err) {
      setConnectionStatus({
        status: 'error',
        message: `Connection test failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
        details: err
      });
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus.status) {
      case 'testing':
        return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus.status) {
      case 'testing':
        return 'border-blue-200 bg-blue-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Supabase Connection Test
      </h3>
      
      <div className={`p-4 rounded-lg border-2 ${getStatusColor()} mb-4`}>
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <span className="text-sm font-medium text-gray-900">
            {connectionStatus.message}
          </span>
        </div>
      </div>

      <button
        onClick={testConnection}
        disabled={connectionStatus.status === 'testing'}
        className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        {connectionStatus.status === 'testing' ? 'Testing...' : 'Test Connection'}
      </button>

      {connectionStatus.details && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Details:</h4>
          <pre className="text-xs text-gray-600 overflow-auto">
            {JSON.stringify(connectionStatus.details, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p><strong>Supabase URL:</strong> {process.env.REACT_APP_SUPABASE_URL}</p>
        <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
      </div>
    </div>
  );
};

export default ConnectionTest;

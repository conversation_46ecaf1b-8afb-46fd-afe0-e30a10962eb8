import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { Home, User, Trophy, Heart, Mic } from 'lucide-react';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

const BottomNav = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { currentLanguage } = useLanguage();

  const navItems = [
    { to: '/', Icon: Home, label: t('navigation.bottom.home') },
    { to: '/leaderboard', Icon: Trophy, label: t('navigation.bottom.leaderboard') },
    { to: '/record', Icon: Mic, label: t('navigation.bottom.record') },
    { to: '/favorites', Icon: Heart, label: t('navigation.bottom.favorites') },
    { to: '/profile', Icon: User, label: t('navigation.bottom.profile') }
  ];

  // If RTL, reverse the items array to maintain visual order
  const displayItems = currentLanguage === 'ar' ? [...navItems].reverse() : navItems;

  return (
    <nav className="fixed inset-x-0 bottom-0 z-50">
      {/* Safe area spacer for iOS */}
      <div className="h-safe-bottom bg-white" />
      
      {/* Main navigation */}
      <div className="bg-white border-t border-gray-200">
        <div className="w-full px-6">
          <div className="flex justify-between items-center w-full">
            {displayItems.map((item) => (
              <NavItem 
                key={item.to} 
                {...item} 
                isActive={location.pathname === item.to}
                currentLanguage={currentLanguage}
              />
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

const NavItem = ({ to, Icon, label, isActive, currentLanguage }) => {
  const isRecordButton = to === '/record';
  
  return (
    <Link
      to={to}
      className={`flex flex-col items-center justify-center h-14 relative ${
        isActive
          ? 'text-primary-600'
          : 'text-gray-600 hover:text-primary-500'
      }`}
    >
      {isRecordButton ? (
        <div className="absolute -top-5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full p-3 shadow-lg transform transition-transform duration-200 hover:scale-110">
          <Icon className="h-6 w-6 text-white" />
        </div>
      ) : (
        <Icon className="h-6 w-6" />
      )}
      <span className={`text-xs ${isRecordButton ? 'mt-6' : 'mt-1'} ${
        currentLanguage === 'fr' ? 'font-french' : 'font-arabic'
      }`}>
        {label}
      </span>
    </Link>
  );
};

export default BottomNav; 
import { socialService } from '../../lib/services/social-service';
import React, { useState } from 'react';
import { UserPlus, UserCheck } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const FollowButton = ({ userId, initialIsFollowing = false }) => {
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);
  const { user } = useAuth();

  // Don't show follow button if user is trying to follow themselves
  if (!user || user.id === userId) {
    return null;
  }

  const handleFollow = async () => {
    if (!userId) return;
    
    try {
      setIsFollowing(!isFollowing);
      if (isFollowing) {
        await socialService.unfollowUser(userId);
      } else {
        await socialService.followUser(userId);
      }
    } catch (error) {
      setIsFollowing(isFollowing);
      console.error('Error toggling follow:', error);
    }
  };

  return (
    <button
      onClick={handleFollow}
      className="inline-flex items-center justify-center p-2 rounded-full
                 bg-teal-600 hover:bg-teal-700 text-white 
                 transition-colors duration-200"
      aria-label={isFollowing ? 'Unfollow' : 'Follow'}
    >
      {isFollowing ? (
        <UserCheck size={18} />
      ) : (
        <UserPlus size={18} />
      )}
    </button>
  );
};

export default FollowButton; 
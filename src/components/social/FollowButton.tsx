import React, { useState, useEffect } from 'react';
import { UserPlus, UserMinus, Users } from 'lucide-react';
import { followingService } from '../../services/followingService';
import { useAuth } from '../../contexts/AuthContext';

interface FollowButtonProps {
  userId: string;
  username?: string;
  initialIsFollowing?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline';
  showCount?: boolean;
  className?: string;
}

const FollowButton: React.FC<FollowButtonProps> = ({
  userId,
  username,
  initialIsFollowing = false,
  onFollowChange,
  size = 'md',
  variant = 'primary',
  showCount = false,
  className = ''
}) => {
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = useState(initialIsFollowing);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [followersCount, setFollowersCount] = useState<number | null>(null);

  // Don't show follow button for own profile
  if (user?.id === userId) {
    return null;
  }

  // Check initial follow status
  useEffect(() => {
    const checkFollowStatus = async () => {
      if (!user) return;

      try {
        const { data: isFollowingData } = await followingService.isFollowing(userId);
        setIsFollowing(isFollowingData);

        if (showCount) {
          const { data: stats } = await followingService.getFollowStats(userId);
          if (stats) {
            setFollowersCount(stats.followers_count);
          }
        }
      } catch (err) {
        console.error('Error checking follow status:', err);
      }
    };

    checkFollowStatus();
  }, [userId, user, showCount]);

  const handleToggleFollow = async () => {
    if (!user) {
      // Redirect to auth
      window.location.href = '/auth';
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (isFollowing) {
        const { error: unfollowError } = await followingService.unfollowUser(userId);
        if (unfollowError) {
          throw unfollowError;
        }
        setIsFollowing(false);
        setFollowersCount(prev => prev !== null ? prev - 1 : null);
        onFollowChange?.(false);
      } else {
        const { error: followError } = await followingService.followUser(userId);
        if (followError) {
          throw followError;
        }
        setIsFollowing(true);
        setFollowersCount(prev => prev !== null ? prev + 1 : null);
        onFollowChange?.(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update follow status');
      console.error('Follow/unfollow error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // Variant classes
  const getVariantClasses = () => {
    if (isFollowing) {
      return 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-red-50 hover:text-red-600 hover:border-red-300';
    }

    switch (variant) {
      case 'primary':
        return 'bg-blue-600 text-white border border-blue-600 hover:bg-blue-700';
      case 'secondary':
        return 'bg-gray-600 text-white border border-gray-600 hover:bg-gray-700';
      case 'outline':
        return 'bg-white text-blue-600 border border-blue-600 hover:bg-blue-50';
      default:
        return 'bg-blue-600 text-white border border-blue-600 hover:bg-blue-700';
    }
  };

  const buttonText = isFollowing ? 'Following' : 'Follow';
  const hoverText = isFollowing ? 'Unfollow' : 'Follow';
  const Icon = isFollowing ? UserMinus : UserPlus;

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleToggleFollow}
        disabled={loading}
        className={`
          inline-flex items-center gap-2 rounded-lg font-medium transition-all duration-200
          disabled:opacity-50 disabled:cursor-not-allowed
          group
          ${sizeClasses[size]}
          ${getVariantClasses()}
          ${className}
        `}
        title={`${hoverText} ${username ? `@${username}` : 'user'}`}
      >
        <Icon className={`${size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'}`} />
        
        {loading ? (
          <span>...</span>
        ) : (
          <>
            <span className="group-hover:hidden">
              {buttonText}
            </span>
            <span className="hidden group-hover:inline">
              {hoverText}
            </span>
          </>
        )}
      </button>

      {/* Followers count */}
      {showCount && followersCount !== null && (
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>{followersCount}</span>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute top-full left-0 mt-1 p-2 bg-red-100 text-red-700 text-xs rounded shadow-lg z-10 whitespace-nowrap">
          {error}
        </div>
      )}
    </div>
  );
};

export default FollowButton;

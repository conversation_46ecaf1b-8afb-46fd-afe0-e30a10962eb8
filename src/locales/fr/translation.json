{"common": {"appName": "<PERSON><PERSON><PERSON>", "loading": "Chargement...", "error": "Une erreur est survenue", "retry": "<PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "share": "Partager", "copyLink": "Copier le lien", "success": "Su<PERSON>ès", "report": "Signaler", "settings": "Paramètres", "time": {"infinity": "Infini"}, "dismiss": "<PERSON><PERSON><PERSON>", "noContent": "Aucun contenu à afficher", "loadMore": "Charger plus", "reportUser": "Signaler l'utilisateur", "searching": "Recherche en cours..."}, "errors": {"generic": {"title": "Une erreur s'est produite", "message": "Une erreur inattendue s'est produite. Veuillez réessayer."}, "network": {"title": "Problème de connexion", "message": "Veuillez vérifier votre connexion internet et réessayer."}, "server": {"title": "<PERSON><PERSON><PERSON> du <PERSON>", "message": "Nos serveurs rencontrent des problèmes. Veuillez réessayer dans un moment."}, "auth": {"title": "Authentification requise", "message": "Veuillez vous connecter pour accéder à ce contenu."}, "timeout": {"title": "<PERSON><PERSON><PERSON>'attente d<PERSON><PERSON>", "message": "La requête a pris trop de temps. Veuillez réessayer."}, "notFound": {"title": "Contenu non trouvé", "message": "Le contenu que vous recherchez n'existe pas ou a été supprimé."}}, "jokes": {"noTopJokesFound": "Aucune blague populaire trouvée."}, "recorder": {"title": "Enregistrer une nouvelle blague", "jokeTitle": "<PERSON><PERSON><PERSON> de la blague", "titlePlaceholder": "Donnez un titre accrocheur à votre blague...", "required": "Champ obligatoire", "enterTitleFirst": "Entrez d'abord un titre", "categories": "Catégories", "effects": {"normal": "Normal", "deep": "Voix grave", "high": "Voix aiguë", "robot": "Robot", "echo": "<PERSON><PERSON>"}, "privateJoke": "Blague privée", "privateJokeDesc": "Seules les personnes avec le lien pourront l'écouter", "upload": "Publier la blague", "uploading": "Publication en cours...", "reRecord": "Réenregistrer", "titleRequired": "Veuillez entrer un titre", "stopRecording": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "startRecording": "Commencer l'enregistrement", "loginToContinue": "Connectez-vous pour continuer"}, "search": {"placeholder": "Rechercher une blague...", "noResults": "Aucun résultat trouvé."}, "navigation": {"main": {"home": "Accueil", "record": "Enregistrer", "search": "<PERSON><PERSON><PERSON>", "profile": "Profil"}, "bottom": {"home": "Accueil", "favorites": "<PERSON><PERSON><PERSON>", "record": "Enregistrer", "achievements": "Réalisations", "profile": "Profil", "leaderboard": "Classement"}, "sections": {"topJokes": "<PERSON><PERSON><PERSON> blagues", "recentJokes": "<PERSON><PERSON>ues récentes", "challenges": "<PERSON><PERSON><PERSON><PERSON>"}}, "home": {"title": "Bienvenue sur Noukta", "subtitle": "Découvrez et partagez les meilleures blagues en darija marocaine", "noJokes": "<PERSON><PERSON><PERSON> blague trouvée"}, "challenges": {"title": "<PERSON><PERSON><PERSON><PERSON>", "createNew": "<PERSON><PERSON>er un nouveau défi", "endsIn": "Se termine le", "participants": "participant", "participate": "Participer au défi", "noChallenges": "Aucun défi pour le moment", "form": {"title": "<PERSON><PERSON><PERSON> du d<PERSON>fi", "description": "Description du défi", "endDate": "Date de fin", "rules": "Règles du défi", "prize": "Prix", "maxParticipants": "Nombre maximum de participants", "creating": "Création en cours...", "create": "<PERSON><PERSON><PERSON> le <PERSON>"}}, "categories": {"general": "Général", "situational": "Situationnel", "wordplay": "Jeux de mots", "dark": "Humour noir", "family": "<PERSON><PERSON><PERSON>", "children": "<PERSON><PERSON><PERSON>", "cultural": "Culturel", "sports": "Sports", "political": "Politique", "social": "Social", "friends": "<PERSON><PERSON>", "tech": "Technologie", "animals": "<PERSON><PERSON><PERSON>", "travel": "Voyage", "cooking": "<PERSON><PERSON><PERSON><PERSON>", "work": "Travail", "comedy": "Comédie", "school": "École"}, "auth": {"login": "Connexion", "register": "S'inscrire", "logout": "Déconnexion", "required": "Connexion requise", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "forgotPassword": "Mot de passe oublié ?", "or": "ou", "continueWith": "Continuer avec", "signIn": "Se connecter", "signOut": "Se déconnecter", "signUp": "S'inscrire", "testConnection": "Tester la connexion à la base de données", "verificationLinkSent": "Veuillez vérifier votre e-mail pour le lien de vérification!", "dbConnectionFailed": "Échec de la connexion à la base de données: ", "dbConnectionSuccess": "Connexion à la base de données réussie!"}, "user": {"anonymous": "Utilisateur anonyme"}, "profile": {"myProfile": "Mon profil", "editProfile": "Modifier le profil", "settings": "Paramètres", "followers": "Abonnés", "following": "Abonnements", "jokes": "<PERSON><PERSON><PERSON>", "loginRequired": "Veuillez vous connecter pour voir le profil", "notFound": "Profil introuvable", "incompleteProfileMessage": "Votre profil est incomplet. Ajoutez un avatar et une biographie pour que votre profil se démarque!", "completeProfile": "Compléter le profil"}, "social": {"likes": "<PERSON>'aime", "comments": "Commentaires", "followers": "Abonnés", "following": "Abonnements", "joinedDaysAgo": "Membre depuis {{count}} jours", "unfollow": "Ne plus suivre", "follow": "Suivre", "unblock": "Débloquer", "block": "Bloquer"}, "report": {"title": "Signaler {{type}}", "question": "Pourquoi signalez-vous ce {{type}} ?", "reporting": "Signalement", "submit": "Envoyer le signalement", "submitting": "Envoi en cours...", "success": {"title": "Signalement envoyé", "message": "Merci de nous aider à maintenir notre communauté sûre. Nous examinerons votre signalement sous peu."}, "errors": {"selectReason": "Veuillez sélectionner une raison pour le signalement", "submitFailed": "Échec de l'envoi du signalement"}, "reasons": {"inappropriate_content": {"label": "Contenu inapproprié", "description": "Contenu offensant ou inapproprié"}, "spam": {"label": "Spam", "description": "Contenu répétitif ou promotionnel"}, "harassment": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Intimidation ou ciblage d'individus"}, "hate_speech": {"label": "Discours de haine", "description": "Contenu promouvant la haine ou la discrimination"}, "violence": {"label": "Violence", "description": "Contenu promouvant ou dépeignant la violence"}, "sexual_content": {"label": "Contenu sexuel", "description": "<PERSON><PERSON><PERSON> sexuel inapproprié"}, "copyright": {"label": "Violation de droits d'auteur", "description": "Utilisation non autorisée de matériel protégé"}, "misinformation": {"label": "Désinformation", "description": "Informations fausses ou trompeuses"}, "other": {"label": "<PERSON><PERSON>", "description": "Autre raison non listée ci-dessus"}}}, "achievements": {"title": "Réalisations", "unlocked": "Débloqué!", "JOKES_CREATED": {"title": "C<PERSON><PERSON>ur de b<PERSON>ues", "description": "<PERSON><PERSON><PERSON> {{count}} blagues"}, "LIKES_RECEIVED": {"title": "Aimé par la foule", "description": "<PERSON><PERSON><PERSON> {{count}} likes"}, "REACTIONS_GIVEN": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> à {{count}} blagues"}, "DAYS_STREAK": {"title": "Assidu", "description": "Enregistrez une blague pendant {{count}} jours consécutifs"}}, "rankings": {"seasonRankings": "Classements de la saison", "currentRank": "Rang actuel", "seasonPoints": "Points de la saison", "season": "<PERSON><PERSON>"}, "leaderboard": {"title": "Classement", "subtitle": "Concourez avec les comédiens les plus drôles de la plateforme", "timePeriod": "Période", "category": "<PERSON><PERSON><PERSON><PERSON>", "timeframes": {"daily": "<PERSON><PERSON><PERSON>'hui", "weekly": "<PERSON><PERSON> se<PERSON>", "monthly": "<PERSON> mois", "all-time": "Tout le temps"}, "categories": {"overall": "Général", "engagement": "Engagement", "consistency": "Régularité", "trending": "Tendance"}, "loading": "Chargement du classement...", "error": "Échec du chargement du classement", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "noRankings": "Aucun classement pour le moment", "noRankingsDesc": "Soyez le premier à publier des blagues et à gravir le classement !", "points": "points", "joinCompetition": "Rejoignez la compétition !", "joinDesc": "Inscrivez-vous pour commencer à publier des blagues et gravir le classement", "signUp": "S'inscrire", "rankings": "Classements"}, "favorites": {"title": "<PERSON><PERSON>", "subtitle": "Blagues que vous avez aimées", "empty": "<PERSON><PERSON>ne blague favorite pour le moment"}, "footer": {"copyright": "© {{year}} Noukta - Tous droits réservés", "links": {"terms": "Conditions d'utilisation", "privacy": "Politique de confidentialité", "contact": "Contactez-nous"}}, "onboarding": {"welcomeTitle": "Bienvenue sur Noukta!", "welcomeText": "Noukta est votre plateforme pour partager et écouter des blagues audio en darija marocaine.", "next": "Suivant", "featureTitle": "Fonctionnalités clés", "feature1": "Enregistrez vos propres blagues avec des effets vocaux amusants.", "feature2": "Interagissez avec les blagues par des emojis et des commentaires.", "feature3": "Suivez vos amis et découvrez de nouvelles blagues.", "complete": "Commencer maintenant"}}
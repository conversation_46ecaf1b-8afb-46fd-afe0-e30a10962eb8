import { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';

export function useTranslations() {
  const { t: staticTranslate, i18n } = useTranslation();
  const [dbTranslations, setDbTranslations] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const debugRef = useRef({
    logged: false,
    translations: new Set()
  });

  useEffect(() => {
    // Temporarily disable database translations to prevent infinite loops
    // The translations table doesn't exist in the current database setup
    setLoading(false);
    setError(null);
    setDbTranslations({});

    // TODO: Re-enable when translations table is properly set up
    // const loadDbTranslations = async () => {
    //   setLoading(true);
    //   setError(null);
    //
    //   try {
    //     const { data, error } = await supabase
    //       .from('translations')
    //       .select('*')
    //       .eq('language', i18n.language);
    //
    //     if (error) {
    //       console.error('Error loading translations:', error);
    //       return; // Don't set error state to prevent loops
    //     }
    //
    //     if (data && data.length > 0) {
    //       const translations = data.reduce((acc, { key, value }) => {
    //         acc[key] = value;
    //         return acc;
    //       }, {});
    //
    //       setDbTranslations(translations);
    //     }
    //   } catch (err) {
    //     console.error('Failed to load translations:', err);
    //   } finally {
    //     setLoading(false);
    //   }
    // };
    //
    // loadDbTranslations();
  }, [i18n.language]);

  const translate = (key, options) => {
    // First try static translations
    const staticResult = staticTranslate(key, options);
    
    // If it returns the key itself, it means no static translation was found
    if (staticResult === key && dbTranslations[key]) {
      if (!debugRef.current.translations.has(key)) {
        debugRef.current.translations.add(key);
        console.log(`🔤 DB translation for "${key}":`, dbTranslations[key]);
      }
      return dbTranslations[key];
    }
    
    if (staticResult === key) {
      if (!debugRef.current.translations.has(key)) {
        debugRef.current.translations.add(key);
        console.warn(`❌ Missing translation for "${key}"`);
      }
    }
    
    return staticResult;
  };

  return { 
    t: translate, 
    loading, 
    error,
    i18n,
    currentTranslations: {
      static: i18n.getDataByLanguage(i18n.language),
      database: dbTranslations
    }
  };
} 
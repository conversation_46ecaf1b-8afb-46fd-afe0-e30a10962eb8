import { useEffect, useState, useCallback } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';

export interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface AuthActions {
  signIn: (provider: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
  signInWithPassword: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string, options?: any) => Promise<{ data: any; error: any }>;
  clearError: () => void;
}

export interface UseAuthReturn extends AuthState, AuthActions {}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Helper to ensure a profile exists for the user
  const ensureProfile = useCallback(async (user: User) => {
    if (!user) return;

    try {
      console.log('Checking profile for user:', user.id);

      // Check if profile exists without timeout to prevent blocking
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('id, username')
        .eq('id', user.id)
        .maybeSingle();

      if (!profile && !error) {
        // Profile doesn't exist, create it
        console.log('Creating profile for user:', user.id);

        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            username: user.user_metadata?.username || user.email?.split('@')[0] || `user_${user.id.slice(0, 8)}`,
            avatar_url: user.user_metadata?.avatar_url || null,
            bio: '',
            onboarded: false
          });

        if (insertError) {
          console.error('Error creating profile:', insertError);
          throw insertError;
        }

        console.log('Profile created successfully for user:', user.id);
      } else if (error) {
        console.error('Error checking profile:', error);
        throw error;
      } else {
        console.log('Profile already exists for user:', user.id);
      }
    } catch (err) {
      console.error('Error ensuring profile:', err);
      // Don't throw - let the app continue even if profile creation fails
    }
  }, []);

  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
          return;
        }

        const currentUser = session?.user ?? null;
        
        if (mounted) {
          setUser(currentUser);
          setLoading(false);
          
          if (currentUser) {
            await ensureProfile(currentUser);

            // Auto-complete onboarding for existing users to fix blank page issue
            if (!currentUser.user_metadata?.onboarded) {
              console.log('Auto-completing onboarding for existing user');
              try {
                await supabase.auth.updateUser({
                  data: { onboarded: true }
                });
                console.log('User onboarding completed automatically');
              } catch (error) {
                console.error('Failed to complete onboarding:', error);
              }
            }
          }
        }
      } catch (err) {
        console.error('Error in getInitialSession:', err);
        if (mounted) {
          setError('Failed to initialize authentication');
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: string, session: Session | null) => {
        if (!mounted) return;

        const changedUser = session?.user ?? null;
        console.log('Auth state changed. Event:', event, 'User:', changedUser);

        setUser(changedUser);
        setError(null);
        setLoading(false); // Always set loading to false after auth state change
        console.log('Auth loading set to false after event:', event);

        if (changedUser) {
          try {
            // Ensure profile exists without timeout to prevent blocking
            await ensureProfile(changedUser);
          } catch (error) {
            console.error('Profile creation failed:', error);
            // Continue anyway - don't block the app
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [ensureProfile]);

  const signIn = useCallback(async (provider: string) => {
    setError(null);
    setLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider as any,
      });
      
      if (error) {
        setError(error.message);
      }
      
      return { data, error };
    } catch (err) {
      const errorMessage = 'Failed to sign in';
      setError(errorMessage);
      return { data: null, error: { message: errorMessage } };
    } finally {
      setLoading(false);
    }
  }, []);

  const signInWithPassword = useCallback(async (email: string, password: string) => {
    setError(null);
    setLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        setError(error.message);
      }
      
      return { data, error };
    } catch (err) {
      const errorMessage = 'Failed to sign in with password';
      setError(errorMessage);
      return { data: null, error: { message: errorMessage } };
    } finally {
      setLoading(false);
    }
  }, []);

  const signUp = useCallback(async (email: string, password: string, options?: any) => {
    setError(null);
    setLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: email.split('@')[0],
            onboarded: false,
            ...options?.data
          },
          ...options
        }
      });
      
      if (error) {
        setError(error.message);
      }
      
      return { data, error };
    } catch (err) {
      const errorMessage = 'Failed to sign up';
      setError(errorMessage);
      return { data: null, error: { message: errorMessage } };
    } finally {
      setLoading(false);
    }
  }, []);

  const signOut = useCallback(async () => {
    setError(null);
    
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        setError(error.message);
      }
      
      return { error };
    } catch (err) {
      const errorMessage = 'Failed to sign out';
      setError(errorMessage);
      return { error: { message: errorMessage } };
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    user,
    loading,
    error,
    signIn,
    signInWithPassword,
    signUp,
    signOut,
    clearError,
  };
};

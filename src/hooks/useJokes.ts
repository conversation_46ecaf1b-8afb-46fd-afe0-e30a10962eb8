import { useState, useEffect, useCallback } from 'react';
import { useAsyncOperation } from './useAsyncOperation';
import { jokesService } from '../services/jokes';
import { Joke, JokeFilters } from '../types';

export interface UseJokesOptions {
  filters?: JokeFilters;
  autoFetch?: boolean;
  realtime?: boolean;
}

export interface UseJokesReturn {
  jokes: Joke[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  totalCount: number;
  fetchJokes: (reset?: boolean) => Promise<void>;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  updateJoke: (jokeId: string, updates: Partial<Joke>) => void;
  removeJoke: (jokeId: string) => void;
  reset: () => void;
}



export const useJokes = (options: UseJokesOptions = {}): UseJokesReturn => {
  const { filters = {}, autoFetch = true, realtime = false } = options;
  
  const [jokes, setJokes] = useState<Joke[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  
  const { execute, loading, error, reset: resetAsync } = useAsyncOperation<{
    jokes: Joke[];
    count: number;
  }>();

  const {
    limit = 20,
    sortBy = 'created_at',
    sortOrder = 'desc',
    ...otherFilters
  } = filters;

  const fetchJokes = useCallback(async (reset = false) => {
    const currentPage = reset ? 0 : page;
    const offset = currentPage * limit;

    const result = await execute(async () => {
      const response = await jokesService.getJokes({
        ...otherFilters,
        sortBy,
        sortOrder,
        limit,
        offset,
      });

      if (response.error) throw response.error;

      return {
        jokes: response.data || [],
        count: response.count || 0,
      };
    }, {
      retryCount: 2,
      retryDelay: 1000,
    });

    if (result) {
      const { jokes: newJokes, count } = result;

      if (reset) {
        setJokes(newJokes);
        setPage(1);
      } else {
        setJokes(prev => [...prev, ...newJokes]);
        setPage(prev => prev + 1);
      }

      setHasMore(newJokes.length === limit);
      setTotalCount(count);
    }
  }, [execute, otherFilters, sortBy, sortOrder, page, limit]);

  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await fetchJokes(false);
    }
  }, [fetchJokes, loading, hasMore]);

  const refresh = useCallback(async () => {
    setPage(0);
    await fetchJokes(true);
  }, [fetchJokes]);

  // Optimistic updates for better UX
  const updateJoke = useCallback((jokeId: string, updates: Partial<Joke>) => {
    setJokes(prev => prev.map(joke => 
      joke.id === jokeId ? { ...joke, ...updates } : joke
    ));
  }, []);

  const removeJoke = useCallback((jokeId: string) => {
    setJokes(prev => prev.filter(joke => joke.id !== jokeId));
    setTotalCount(prev => Math.max(0, prev - 1));
  }, []);

  const reset = useCallback(() => {
    setJokes([]);
    setHasMore(true);
    setTotalCount(0);
    setPage(0);
    resetAsync();
  }, [resetAsync]);

  // Auto-fetch on mount and filter changes
  useEffect(() => {
    if (autoFetch) {
      reset();
      fetchJokes(true);
    }
  }, [
    otherFilters.category,
    otherFilters.search,
    otherFilters.userId,
    sortBy,
    sortOrder,
    autoFetch,
    fetchJokes,
    reset
  ]);

  // Real-time subscriptions - TEMPORARILY DISABLED
  useEffect(() => {
    // DISABLED: Real-time subscriptions causing connection exhaustion
    // TODO: Re-enable with proper connection pooling
    return;

    if (!realtime) return;

    const subscription = jokesService.subscribeToJokes((payload) => {
      if (payload.eventType === 'INSERT') {
        const newJoke = payload.new as Joke;
        setJokes(prev => [newJoke, ...prev]);
        setTotalCount(prev => prev + 1);
      } else if (payload.eventType === 'UPDATE') {
        const updatedJoke = payload.new as Joke;
        updateJoke(updatedJoke.id, updatedJoke);
      } else if (payload.eventType === 'DELETE') {
        const deletedJoke = payload.old as Joke;
        removeJoke(deletedJoke.id);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [realtime, updateJoke, removeJoke]);

  return {
    jokes,
    loading,
    error,
    hasMore,
    totalCount,
    fetchJokes,
    loadMore,
    refresh,
    updateJoke,
    removeJoke,
    reset,
  };
};

// Specialized hook for user's own jokes
export const useUserJokes = (userId?: string) => {
  return useJokes({
    filters: { userId, sortBy: 'created_at', sortOrder: 'desc' },
    autoFetch: !!userId,
  });
};

// Specialized hook for top jokes
export const useTopJokes = (limit = 20) => {
  return useJokes({
    filters: { sortBy: 'likes_count', sortOrder: 'desc', limit },
    autoFetch: true,
  });
};

// Specialized hook for recent jokes
export const useRecentJokes = (limit = 20) => {
  return useJokes({
    filters: { sortBy: 'created_at', sortOrder: 'desc', limit },
    autoFetch: true,
    realtime: true,
  });
};

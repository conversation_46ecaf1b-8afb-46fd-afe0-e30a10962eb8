import { useState, useEffect, useCallback } from 'react';
import { useOptimisticOperation } from './useAsyncOperation';
import { reactionsService } from '../services/reactions';
import { ReactionCounts } from '../types';

export interface UseReactionsReturn {
  reactions: ReactionCounts;
  userReactions: string[];
  loading: boolean;
  error: string | null;
  toggleReaction: (emoji: string) => Promise<void>;
  addReaction: (emoji: string) => Promise<void>;
  removeReaction: (emoji: string) => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
}

export const useReactions = (jokeId: string): UseReactionsReturn => {
  const [reactions, setReactions] = useState<ReactionCounts>({});
  const [userReactions, setUserReactions] = useState<string[]>([]);
  
  const {
    execute,
    executeWithOptimisticUpdate,
    loading,
    error,
    reset: resetAsync
  } = useOptimisticOperation<{
    reactions: ReactionCounts;
    userReactions: string[];
  }>({
    reactions: {},
    userReactions: []
  });

  // Load initial reactions
  const loadReactions = useCallback(async () => {
    if (!jokeId) return;

    await execute(async () => {
      const { counts, userReactions: userReactionsList } = await reactionsService.getReactionsData(jokeId);

      return {
        reactions: counts,
        userReactions: userReactionsList
      };
    });
  }, [jokeId, execute]);

  // Toggle reaction with optimistic updates
  const toggleReaction = useCallback(async (emoji: string) => {
    if (!jokeId) return;

    const isRemoving = userReactions.includes(emoji);
    
    // Optimistic update
    const optimisticReactions = { ...reactions };
    const optimisticUserReactions = [...userReactions];

    if (isRemoving) {
      optimisticReactions[emoji] = Math.max((optimisticReactions[emoji] || 1) - 1, 0);
      const index = optimisticUserReactions.indexOf(emoji);
      if (index > -1) {
        optimisticUserReactions.splice(index, 1);
      }
    } else {
      optimisticReactions[emoji] = (optimisticReactions[emoji] || 0) + 1;
      optimisticUserReactions.push(emoji);
    }

    const optimisticData = {
      reactions: optimisticReactions,
      userReactions: optimisticUserReactions
    };

    const result = await executeWithOptimisticUpdate(
      async () => {
        await reactionsService.toggleReaction(jokeId, emoji);

        // Fetch updated counts after toggle
        const { counts, userReactions: newUserReactions } = await reactionsService.getReactionsData(jokeId);

        return {
          reactions: counts,
          userReactions: newUserReactions
        };
      },
      optimisticData,
      {
        retryCount: 2,
        retryDelay: 1000,
      }
    );

    if (result) {
      setReactions(result.reactions);
      setUserReactions(result.userReactions);
    }
  }, [jokeId, reactions, userReactions, executeWithOptimisticUpdate]);

  // Add reaction (always adds, doesn't toggle)
  const addReaction = useCallback(async (emoji: string) => {
    if (!jokeId || userReactions.includes(emoji)) return;

    // Optimistic update
    const optimisticReactions = {
      ...reactions,
      [emoji]: (reactions[emoji] || 0) + 1
    };
    const optimisticUserReactions = [...userReactions, emoji];

    const optimisticData = {
      reactions: optimisticReactions,
      userReactions: optimisticUserReactions
    };

    const result = await executeWithOptimisticUpdate(
      async () => {
        await reactionsService.addReaction(jokeId, emoji);

        // Fetch updated counts
        const { counts, userReactions: newUserReactions } = await reactionsService.getReactionsData(jokeId);

        return {
          reactions: counts,
          userReactions: newUserReactions
        };
      },
      optimisticData,
      {
        retryCount: 2,
        retryDelay: 1000,
      }
    );

    if (result) {
      setReactions(result.reactions);
      setUserReactions(result.userReactions);
    }
  }, [jokeId, reactions, userReactions, executeWithOptimisticUpdate]);

  // Remove reaction
  const removeReaction = useCallback(async (emoji: string) => {
    if (!jokeId || !userReactions.includes(emoji)) return;

    // Optimistic update
    const optimisticReactions = {
      ...reactions,
      [emoji]: Math.max((reactions[emoji] || 1) - 1, 0)
    };
    const optimisticUserReactions = userReactions.filter(r => r !== emoji);

    const optimisticData = {
      reactions: optimisticReactions,
      userReactions: optimisticUserReactions
    };

    const result = await executeWithOptimisticUpdate(
      async () => {
        await reactionsService.removeReaction(jokeId, emoji);

        // Fetch updated counts
        const { counts, userReactions: newUserReactions } = await reactionsService.getReactionsData(jokeId);

        return {
          reactions: counts,
          userReactions: newUserReactions
        };
      },
      optimisticData,
      {
        retryCount: 2,
        retryDelay: 1000,
      }
    );

    if (result) {
      setReactions(result.reactions);
      setUserReactions(result.userReactions);
    }
  }, [jokeId, reactions, userReactions, executeWithOptimisticUpdate]);

  const refresh = useCallback(async () => {
    await loadReactions();
  }, [loadReactions]);

  const reset = useCallback(() => {
    setReactions({});
    setUserReactions([]);
    resetAsync();
  }, [resetAsync]);

  // Load reactions on mount and when jokeId changes
  useEffect(() => {
    if (jokeId) {
      loadReactions();
    } else {
      reset();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jokeId]); // Remove loadReactions and reset from dependencies to prevent loops

  // Real-time subscription for reaction changes - TEMPORARILY DISABLED
  useEffect(() => {
    // DISABLED: Real-time subscriptions causing connection exhaustion
    // TODO: Re-enable with proper connection pooling
    return;

    if (!jokeId) return;

    const subscription = reactionsService.subscribeToReactions(jokeId, () => {
      // Refresh reactions when changes occur
      loadReactions();
    });

    return () => {
      subscription.unsubscribe();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jokeId]); // Remove loadReactions from dependencies to prevent loops

  return {
    reactions,
    userReactions,
    loading,
    error,
    toggleReaction,
    addReaction,
    removeReaction,
    refresh,
    reset,
  };
};

import { useState, useCallback, useRef } from 'react';
import * as Sentry from '@sentry/react';

export interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  success: boolean;
}

export interface AsyncOperationOptions {
  retryCount?: number;
  retryDelay?: number;
  reportToSentry?: boolean;
  successMessage?: string;
}

export interface UseAsyncOperationReturn<T> extends AsyncOperationState<T> {
  execute: (operation: () => Promise<T>, options?: AsyncOperationOptions) => Promise<T | null>;
  reset: () => void;
  retry: () => Promise<T | null>;
}

export const useAsyncOperation = <T = any>(): UseAsyncOperationReturn<T> => {
  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  const lastOperationRef = useRef<{
    operation: () => Promise<T>;
    options?: AsyncOperationOptions;
  } | null>(null);

  const execute = useCallback(async (
    operation: () => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    const {
      retryCount = 0,
      retryDelay = 1000,
      reportToSentry = true,
      successMessage
    } = options;

    // Store for retry functionality
    lastOperationRef.current = { operation, options };

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false,
    }));

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        const result = await operation();
        
        setState({
          data: result,
          loading: false,
          error: null,
          success: true,
        });

        if (successMessage) {
          console.log(successMessage);
        }

        return result;
      } catch (error) {
        lastError = error as Error;
        
        // If this is not the last attempt, wait before retrying
        if (attempt < retryCount) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
          continue;
        }

        // Final attempt failed
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
        
        setState({
          data: null,
          loading: false,
          error: errorMessage,
          success: false,
        });

        // Report to Sentry if enabled
        if (reportToSentry) {
          Sentry.captureException(error, {
            tags: {
              operation: 'async_operation',
              attempt: attempt + 1,
              maxAttempts: retryCount + 1,
            },
            extra: {
              errorMessage,
              options,
            },
          });
        }

        console.error('Async operation failed:', error);
        break;
      }
    }

    return null;
  }, []);

  const retry = useCallback(async (): Promise<T | null> => {
    if (!lastOperationRef.current) {
      console.warn('No operation to retry');
      return null;
    }

    const { operation, options } = lastOperationRef.current;
    return execute(operation, options);
  }, [execute]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
    lastOperationRef.current = null;
  }, []);

  return {
    ...state,
    execute,
    retry,
    reset,
  };
};

// Specialized hook for API operations
export const useApiOperation = <T = any>() => {
  const asyncOp = useAsyncOperation<T>();

  const executeApi = useCallback(async (
    apiCall: () => Promise<{ data: T; error: any }>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    return asyncOp.execute(async () => {
      const { data, error } = await apiCall();
      
      if (error) {
        throw new Error(error.message || 'API operation failed');
      }
      
      return data;
    }, options);
  }, [asyncOp]);

  return {
    ...asyncOp,
    executeApi,
  };
};

// Hook for operations with optimistic updates
export const useOptimisticOperation = <T = any>(initialData: T | null = null) => {
  const [optimisticData, setOptimisticData] = useState<T | null>(initialData);
  const asyncOp = useAsyncOperation<T>();

  const executeWithOptimisticUpdate = useCallback(async (
    operation: () => Promise<T>,
    optimisticUpdate: T,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Apply optimistic update immediately
    setOptimisticData(optimisticUpdate);

    const result = await asyncOp.execute(operation, options);

    if (result) {
      // Update with real data on success
      setOptimisticData(result);
    } else {
      // Revert optimistic update on failure
      setOptimisticData(asyncOp.data || initialData);
    }

    return result;
  }, [asyncOp, initialData]);

  const reset = useCallback(() => {
    setOptimisticData(initialData);
    asyncOp.reset();
  }, [asyncOp, initialData]);

  return {
    ...asyncOp,
    optimisticData,
    executeWithOptimisticUpdate,
    reset,
  };
};

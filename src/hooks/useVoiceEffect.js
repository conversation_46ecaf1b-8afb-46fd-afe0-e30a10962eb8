import { useCallback, useRef } from 'react';

const useVoiceEffect = () => {
  const audioContextRef = useRef(null);
  const sourceNodeRef = useRef(null);
  const effectNodeRef = useRef(null);

  const effects = {
    normal: {
      pitch: 1.0,
      detune: 0,
      formant: 0
    },
    subtle1: {
      pitch: 1.05,
      detune: 50,
      formant: 0.2
    },
    subtle2: {
      pitch: 0.95,
      detune: -50,
      formant: -0.2
    },
    neutral: {
      pitch: 1.0,
      detune: 25,
      formant: 0.1,
      blend: true
    }
  };

  const createAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
    return audioContextRef.current;
  }, []);

  const applyEffect = useCallback(async (stream, effectName = 'normal') => {
    const audioContext = createAudioContext();
    
    // Clean up previous nodes
    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect();
    }
    if (effectNodeRef.current) {
      effectNodeRef.current.disconnect();
    }

    // Create source node from stream
    sourceNodeRef.current = audioContext.createMediaStreamSource(stream);
    
    // Get effect settings
    const effect = effects[effectName] || effects.normal;
    
    // Create subtle pitch shift
    const pitchNode = audioContext.createBiquadFilter();
    pitchNode.type = 'highshelf';
    pitchNode.frequency.value = effect.pitch * 1000;
    pitchNode.gain.value = 2;

    // Create formant shift (subtle voice character change)
    const formantNode = audioContext.createBiquadFilter();
    formantNode.type = 'peaking';
    formantNode.Q.value = 1;
    formantNode.frequency.value = 2000 + (effect.formant * 500);
    formantNode.gain.value = effect.formant * 3;

    // Create subtle detune
    const detuneNode = audioContext.createBiquadFilter();
    detuneNode.type = 'lowshelf';
    detuneNode.frequency.value = 500;
    detuneNode.detune.value = effect.detune;

    // Blend with original signal if specified
    if (effect.blend) {
      const gainNode1 = audioContext.createGain();
      const gainNode2 = audioContext.createGain();
      gainNode1.gain.value = 0.7; // Modified signal
      gainNode2.gain.value = 0.3; // Original signal

      // Modified signal path
      sourceNodeRef.current
        .connect(pitchNode)
        .connect(formantNode)
        .connect(detuneNode)
        .connect(gainNode1);

      // Original signal path
      sourceNodeRef.current.connect(gainNode2);

      const merger = audioContext.createChannelMerger(2);
      gainNode1.connect(merger, 0, 0);
      gainNode2.connect(merger, 0, 0);
      
      effectNodeRef.current = merger;
      merger.connect(audioContext.destination);
    } else {
      // Connect nodes in series
      sourceNodeRef.current
        .connect(pitchNode)
        .connect(formantNode)
        .connect(detuneNode);

      effectNodeRef.current = detuneNode;
      detuneNode.connect(audioContext.destination);
    }

    // Return modified stream
    const destination = audioContext.createMediaStreamDestination();
    effectNodeRef.current.connect(destination);
    return destination.stream;
  }, []);

  const cleanupEffect = useCallback(() => {
    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect();
      sourceNodeRef.current = null;
    }
    if (effectNodeRef.current) {
      effectNodeRef.current.disconnect();
      effectNodeRef.current = null;
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
  }, []);

  return {
    applyEffect,
    cleanupEffect,
    availableEffects: Object.keys(effects)
  };
};

export default useVoiceEffect;
import { useState, useRef, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export const useAudioRecorder = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const mediaRecorderRef = useRef(null);
  const chunksRef = useRef([]);
  // eslint-disable-next-line no-unused-vars
  const { user } = useAuth();

  // Apply voice effect to audio buffer
  const applyVoiceEffect = async (audioBuffer, effect) => {
    console.log('Applying voice effect:', effect, 'to audio buffer:', audioBuffer);
    const ctx = new (window.AudioContext || window.webkitAudioContext)();
    const source = ctx.createBufferSource();
    source.buffer = audioBuffer;

    // Create destination for recording
    const destination = ctx.createMediaStreamDestination();
    let effectNode = null;

    switch (effect) {
      case 'deep':
        effectNode = ctx.createBiquadFilter();
        effectNode.type = 'lowshelf';
        effectNode.frequency.value = 100;
        effectNode.gain.value = 15;
        source.connect(effectNode);
        effectNode.connect(destination);
        break;

      case 'high':
        effectNode = ctx.createBiquadFilter();
        effectNode.type = 'highshelf';
        effectNode.frequency.value = 1000;
        effectNode.gain.value = 15;
        source.connect(effectNode);
        effectNode.connect(destination);
        break;

      case 'robot':
        const oscillator = ctx.createOscillator();
        const gain = ctx.createGain();
        oscillator.frequency.value = 440;
        gain.gain.value = 0.5;
        source.connect(gain);
        oscillator.connect(gain);
        gain.connect(destination);
        oscillator.start();
        effectNode = gain; // For cleanup
        break;

      case 'echo':
        const delay = ctx.createDelay(0.5);
        const feedback = ctx.createGain();
        feedback.gain.value = 0.5;
        source.connect(delay);
        delay.connect(feedback);
        feedback.connect(delay);
        delay.connect(destination);
        effectNode = delay; // For cleanup
        break;

      default:
        source.connect(destination);
    }

    return new Promise((resolve) => {
      const chunks = [];
      // Remove duplicate connection
      // source.connect(destination); // This was already done above
      
      const recorder = new MediaRecorder(destination.stream);
      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => resolve(new Blob(chunks, { type: 'audio/webm' }));
      
      source.start(0);
      recorder.start();
      
      setTimeout(() => {
        recorder.stop();
        source.stop();
      }, audioBuffer.duration * 1000);
    });
  };

  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setError(null);
    } catch (err) {
      setError('Error accessing microphone: ' + err.message);
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Stop all tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  }, [isRecording]);

  const uploadAudio = async (title, effect = 'normal', isPrivate = false) => {
    // Check session before upload
    const { data: { session } } = await supabase.auth.getSession();
    console.log('Session before upload:', session);
    if (!session || !session.user) {
      setError('You must be logged in to upload audio. No session found.');
      return null;
    }
    if (!audioUrl) {
      setError('No audio to upload.');
      return null;
    }
    setLoading(true);
    console.log('Uploading audio for user:', session.user.id);
    console.log('Joke data being sent:', { title, effect, isPrivate, userId: session.user.id });
    
    // Additional logging for authentication state
    const { data: { user: authenticatedUser }, error: authError } = await supabase.auth.getUser();
    console.log('Supabase auth.getUser() result before upload:', authenticatedUser);
    if (authError) {
      console.error('Error getting authenticated user before upload:', authError);
    }
    try {
      // Get the original audio blob
      const response = await fetch(audioUrl);
      let blob = await response.blob();

      // Apply voice effect if needed
      if (effect !== 'normal') {
        const arrayBuffer = await blob.arrayBuffer();
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        blob = await applyVoiceEffect(audioBuffer, effect);
      }

      // Generate unique filename
      const filename = `${session.user.id}/${Date.now()}.webm`;
      
      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('jokes')
        .upload(filename, blob);
        
      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('jokes')
        .getPublicUrl(filename);

      // Create joke record in database
      const { data: joke, error: jokeError } = await supabase
        .from('jokes')
        .insert({
          audio_url: publicUrl,
          title,
          voice_effect: effect,
          is_private: isPrivate,
          user_id: session.user.id
        })
        .select()
        .single();

      if (jokeError) throw jokeError;

      return joke;
    } catch (err) {
      setError('Error uploading audio: ' + err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const resetRecording = useCallback(() => {
    setAudioUrl(null);
    setError(null);
    chunksRef.current = [];
  }, []);

  return {
    isRecording,
    audioUrl,
    error,
    loading,
    startRecording,
    stopRecording,
    uploadAudio,
    resetRecording,
  };
};

export default useAudioRecorder;
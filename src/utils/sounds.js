// Create audio context singleton
let audioContext = null;

const getAudioContext = () => {
  if (!audioContext) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  }
  return audioContext;
};

// Sound definitions
const sounds = {
  click: {
    frequency: 2000,
    gain: 0.1,
    attack: 0.001,
    decay: 0.1,
    type: 'sine'
  },
  expand: {
    frequency: 1200,
    gain: 0.1,
    attack: 0.01,
    decay: 0.2,
    type: 'sine'
  },
  collapse: {
    frequency: 800,
    gain: 0.1,
    attack: 0.01,
    decay: 0.2,
    type: 'sine'
  },
  hover: {
    frequency: 2400,
    gain: 0.05,
    attack: 0.001,
    decay: 0.05,
    type: 'sine'
  },
  like: {
    frequency: 1600,
    gain: 0.15,
    attack: 0.001,
    decay: 0.15,
    type: 'sine'
  },
  success: {
    frequency: 1800,
    gain: 0.15,
    attack: 0.001,
    decay: 0.2,
    type: 'sine'
  }
};

// Play a sound with the given parameters
const playSound = ({ frequency, gain: gainValue, attack, decay, type }) => {
  const ctx = getAudioContext();
  
  // Create oscillator
  const oscillator = ctx.createOscillator();
  oscillator.type = type;
  oscillator.frequency.setValueAtTime(frequency, ctx.currentTime);

  // Create gain node for envelope
  const gain = ctx.createGain();
  gain.gain.setValueAtTime(0, ctx.currentTime);
  gain.gain.linearRampToValueAtTime(gainValue, ctx.currentTime + attack);
  gain.gain.linearRampToValueAtTime(0, ctx.currentTime + attack + decay);

  // Connect nodes
  oscillator.connect(gain);
  gain.connect(ctx.destination);

  // Start and stop
  oscillator.start(ctx.currentTime);
  oscillator.stop(ctx.currentTime + attack + decay);
};

// Sound player functions
export const playClickSound = () => playSound(sounds.click);
export const playExpandSound = () => playSound(sounds.expand);
export const playCollapseSound = () => playSound(sounds.collapse);
export const playHoverSound = () => playSound(sounds.hover);
export const playLikeSound = () => playSound(sounds.like);
export const playSuccessSound = () => playSound(sounds.success);

// Initialize audio context on first user interaction
export const initSounds = () => {
  const ctx = getAudioContext();
  if (ctx.state === 'suspended') {
    ctx.resume();
  }
};

// Enable/disable sounds
let soundsEnabled = true;

export const enableSounds = () => {
  soundsEnabled = true;
};

export const disableSounds = () => {
  soundsEnabled = false;
};

export const isSoundsEnabled = () => soundsEnabled;

// Wrapped sound functions that check if sounds are enabled
export const playSoundIfEnabled = (soundFunction) => {
  if (soundsEnabled) {
    soundFunction();
  }
};
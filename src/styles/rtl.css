/* Language-specific styles */
[lang="ar"] {
  font-family: 'Noto Sans Arabic', 'Noto Kufi Arabic', system-ui, -apple-system, sans-serif;
  direction: rtl;
  text-align: right;
}

[lang="fr"] {
  font-family: 'Poppins', 'Inter', system-ui, -apple-system, sans-serif;
  direction: ltr;
  text-align: left;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

/* LTR Support */
[dir="ltr"] {
  text-align: left;
}

/* Direction-aware Layout */
.direction-aware {
  direction: inherit;
}

/* Bidirectional Text Support */
.bidi-override {
  unicode-bidi: bidi-override;
}

/* Numbers always LTR */
.numbers-ltr {
  direction: ltr;
  unicode-bidi: embed;
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid theme('colors.gray.200');
  z-index: 50;
}

/* Mobile Header */
.mobile-header {
  position: sticky;
  top: 0;
  background: white;
  padding-top: max(1rem, env(safe-area-inset-top));
  z-index: 40;
  border-bottom: 1px solid theme('colors.gray.200');
}

/* Language Switcher */
.lang-switcher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
  background: theme('colors.gray.100');
}

/* Spacing and Layout */
[dir="rtl"] .space-x-1> :not([hidden])~ :not([hidden]),
[dir="rtl"] .space-x-2> :not([hidden])~ :not([hidden]),
[dir="rtl"] .space-x-3> :not([hidden])~ :not([hidden]),
[dir="rtl"] .space-x-4> :not([hidden])~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="ltr"] .space-x-1> :not([hidden])~ :not([hidden]),
[dir="ltr"] .space-x-2> :not([hidden])~ :not([hidden]),
[dir="ltr"] .space-x-3> :not([hidden])~ :not([hidden]),
[dir="ltr"] .space-x-4> :not([hidden])~ :not([hidden]) {
  --tw-space-x-reverse: 0;
}

/* Flexbox Modifications */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* Border Radius Adjustments */
[dir="rtl"] .rounded-l {
  border-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

[dir="rtl"] .rounded-r {
  border-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

/* Positioning */
[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}

[dir="rtl"] .right-0 {
  right: auto;
  left: 0;
}

/* Text Input and Form Elements */
[dir="rtl"] input,
[dir="rtl"] textarea {
  text-align: right;
}

[dir="ltr"] input,
[dir="ltr"] textarea {
  text-align: left;
}

/* Numbers and Dates */
[dir="rtl"] input[type="tel"],
[dir="rtl"] input[type="number"],
[dir="rtl"] input[type="date"] {
  direction: ltr;
}

/* Dropdown/Popover Positioning */
[dir="rtl"] .origin-top-right {
  transform-origin: top left;
}

[dir="rtl"] .origin-top-left {
  transform-origin: top right;
}

/* Button and Icon Spacing */
[dir="rtl"] .btn-icon {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="ltr"] .btn-icon {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* List Styling */
[dir="rtl"] ul,
[dir="rtl"] ol {
  padding-left: 0;
  padding-right: 1.25rem;
}

[dir="ltr"] ul,
[dir="ltr"] ol {
  padding-right: 0;
  padding-left: 1.25rem;
}

/* Audio Player Controls */
[dir="rtl"] .audio-controls {
  flex-direction: row-reverse;
}

[dir="rtl"] .audio-progress {
  direction: ltr;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  [dir="rtl"] .mobile-nav {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .mobile-search input {
    padding-right: 2.5rem;
    padding-left: 1rem;
  }

  [dir="ltr"] .mobile-search input {
    padding-left: 2.5rem;
    padding-right: 1rem;
  }

  [dir="rtl"] .mobile-search .search-icon {
    right: 0.75rem;
    left: auto;
  }

  [dir="ltr"] .mobile-search .search-icon {
    left: 0.75rem;
    right: auto;
  }

  .mobile-stack {
    flex-direction: column;
  }

  .mobile-reverse {
    flex-direction: column-reverse;
  }
}

/* Typography Enhancements */
[lang="ar"] {
  letter-spacing: 0;
  word-spacing: 0.05em;
}

[lang="fr"] {
  letter-spacing: 0.01em;
  word-spacing: 0.025em;
}

/* Transitions */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus Rings */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px theme('colors.primary.200');
}
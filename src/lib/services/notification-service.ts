import { supabase } from '../supabase';

export interface Notification {
  id: string;
  user_id: string;
  type: string;
  data: any;
  read: boolean;
  created_at: string;
}

export const notificationService = {
  async create(notification: {
    userId: string;
    type: string;
    data: any;
  }): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: notification.userId,
        type: notification.type,
        data: notification.data,
        read: false
      });

    if (error) throw error;
  },

  async markAsRead(notificationId: string): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) throw error;
  },

  async getUserNotifications(userId: string): Promise<Notification[]> {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }
}; 
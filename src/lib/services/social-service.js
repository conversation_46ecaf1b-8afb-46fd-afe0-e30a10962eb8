import { supabase } from '../supabase';
import { notificationService } from './notification-service';

// Helper function to get profile ID from auth user
const getProfileId = async (authUserId) => {
  const { data: profile } = await supabase
    .from('profiles')
    .select('id')
    .eq('id', authUserId)
    .single();

  return profile?.id || authUserId; // Fallback to auth ID if profile not found
};

export const socialService = {
  // Follow/Unfollow
  async followUser(followingId) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User must be authenticated');

    // Get profile ID for the follower
    const followerProfileId = await getProfileId(user.id);

    const { data, error } = await supabase
      .from('follows')
      .insert({
        follower_id: followerProfileId,
        following_id: followingId
      });

    if (error) throw error;

    try {
      await notificationService.create({
        userId: followingId,
        type: 'NEW_FOLLOWER',
        data: { followerId: user.id }
      });
    } catch (notifError) {
      console.warn('Failed to create notification:', notifError);
    }

    return data;
  },

  async unfollowUser(followingId) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User must be authenticated');

    // Get profile ID for the follower
    const followerProfileId = await getProfileId(user.id);

    const { data, error } = await supabase
      .from('follows')
      .delete()
      .match({
        follower_id: followerProfileId,
        following_id: followingId
      });
    
    if (error) throw error;
    return data;
  },

  // Comments
  async addComment(jokeId, content, parentId = null) {
    const { data, error } = await supabase
      .from('comments')
      .insert({
        joke_id: jokeId,
        user_id: supabase.auth.user().id,
        content,
        parent_id: parentId
      })
      .select(`
        *,
        user:user_id (username, avatar_url)
      `)
      .single();
    
    if (error) throw error;

    // Get joke owner's ID for notification
    const { data: joke } = await supabase
      .from('jokes')
      .select('user_id')
      .eq('id', jokeId)
      .single();

    if (joke && joke.user_id !== supabase.auth.user().id) {
      await notificationService.create({
        userId: joke.user_id,
        type: 'NEW_COMMENT',
        data: { jokeId, commentId: data.id }
      });
    }

    return data;
  },

  async updateComment(commentId, content) {
    const { data, error } = await supabase
      .from('comments')
      .update({ content, is_edited: true })
      .eq('id', commentId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async deleteComment(commentId) {
    const { error } = await supabase
      .from('comments')
      .delete()
      .eq('id', commentId);
    
    if (error) throw error;
  },

  async getComments(jokeId) {
    const { data, error } = await supabase
      .from('comments')
      .select(`
        *,
        user:user_id (username, avatar_url)
      `)
      .eq('joke_id', jokeId)
      .order('created_at', { ascending: true });
    
    if (error) throw error;
    return data;
  },

  // Categories
  async getCategories() {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data;
  },

  async getJokesByCategory(categorySlug, page = 0, limit = 10) {
    const start = page * limit;
    const end = start + limit - 1;

    // Simplified query without category joins for now
    const { data, error } = await supabase
      .from('jokes')
      .select('*')
      .eq('is_private', false)
      .range(start, end)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  // Emoji Reactions
  async addReaction(jokeId, emoji) {
    const { data, error } = await supabase
      .from('emoji_reactions')
      .insert({
        joke_id: jokeId,
        user_id: supabase.auth.user().id,
        emoji
      });
    
    if (error) throw error;
    return data;
  },

  async removeReaction(jokeId, emoji) {
    const { data, error } = await supabase
      .from('emoji_reactions')
      .delete()
      .match({
        joke_id: jokeId,
        user_id: supabase.auth.user().id,
        emoji
      });
    
    if (error) throw error;
    return data;
  },

  async getReactions(jokeId) {
    const { data, error } = await supabase
      .from('emoji_reactions')
      .select('emoji, count(*)')
      .eq('joke_id', jokeId)
      .select('emoji, user_id')
      .then(({ data }) => {
        // Manually aggregate the reactions
        const counts = data.reduce((acc, { emoji }) => {
          acc[emoji] = (acc[emoji] || 0) + 1;
          return acc;
        }, {});
        return { data: counts };
      });

    if (error) throw error;
    return data;
  },

  // User Blocking
  async blockUser(blockedId) {
    const { data, error } = await supabase
      .from('user_blocks')
      .insert({
        blocker_id: supabase.auth.user().id,
        blocked_id: blockedId
      });
    
    if (error) throw error;
    return data;
  },

  async unblockUser(blockedId) {
    const { data, error } = await supabase
      .from('user_blocks')
      .delete()
      .match({
        blocker_id: supabase.auth.user().id,
        blocked_id: blockedId
      });
    
    if (error) throw error;
    return data;
  }
}; 
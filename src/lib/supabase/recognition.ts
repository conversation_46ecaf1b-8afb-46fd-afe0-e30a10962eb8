import { supabase } from '../../lib/supabase';
import type { Database } from './supabase-client';

export interface Spotlight {
  id: string;
  user_id: string;
  category: 'WEEKLY_SPOTLIGHT' | 'HALL_OF_FAME' | 'RISING_STAR';
  start_date: string;
  end_date: string;
  metrics: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  featured_joke_id: string;
  user?: {
    username: string;
    avatar_url: string;
  };
  featured_joke?: {
    title: string;
    description: string;
  };
}

export const recognitionService = {
  async updateWeeklySpotlight(): Promise<void> {
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - 7);

    // Get top performing creators
    const { data: topCreators } = await supabase
      .rpc('calculate_creator_performance', { 
        start_date: weekStart.toISOString() 
      });

    if (topCreators?.length > 0) {
      await supabase
        .from('community_spotlights')
        .insert({
          user_id: topCreators[0].user_id,
          category: 'WEEKLY_SPOTLIGHT',
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          metrics: topCreators[0].metrics,
          featured_joke_id: topCreators[0].top_joke_id
        });
    }
  },

  async getCurrentSpotlights(): Promise<Spotlight[]> {
    const { data, error } = await supabase
      .from('community_spotlights')
      .select(`
        *,
        user:user_id (
          id,
          username,
          avatar_url
        ),
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          metrics
        )
      `)
      .gte('end_date', new Date().toISOString())
      .order('start_date', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getHallOfFame(): Promise<Spotlight[]> {
    const { data, error } = await supabase
      .from('community_spotlights')
      .select(`
        *,
        user:users(username, avatar_url),
        featured_joke:jokes(title, description)
      `)
      .eq('category', 'HALL_OF_FAME')
      .order('start_date', { ascending: false });

    if (error) throw error;
    return data;
  }
}; 
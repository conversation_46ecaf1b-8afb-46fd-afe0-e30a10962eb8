import { supabase } from '../supabase';

// Type definitions
export type Database = {
  public: {
    Tables: {
      achievements: {
        Row: {
          id: string;
          code: string;
          title: string;
          description: string;
          icon: string;
          requirement: number;
          xp_reward: number;
          badge_color: string;
          category: string;
        };
      };
      user_achievements: {
        Row: {
          id: string;
          user_id: string;
          achievement_id: string;
          progress: number;
          unlocked_at: string | null;
          created_at: string;
        };
      };
      seasonal_rankings: {
        Row: {
          id: string;
          season_id: string;
          user_id: string;
          points: number;
          rank: string;
          updated_at: string;
        };
      };
      community_spotlights: {
        Row: {
          id: string;
          user_id: string;
          category: string;
          start_date: string;
          end_date: string;
          metrics: any;
          featured_joke_id: string;
        };
      };
    };
  };
}; 
import { supabase } from '../supabase';

export const achievementService = {
  async getAllAchievements() {
    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .order('category', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async getUserAchievements(userId) {
    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievements (*)
      `)
      .eq('user_id', userId);
    
    if (error) throw error;
    return data || [];
  },

  async updateProgress(userId, achievementId, progress) {
    const { data, error } = await supabase
      .from('user_achievements')
      .upsert({
        user_id: userId,
        achievement_id: achievementId,
        progress: progress,
        unlocked_at: progress >= 100 ? new Date().toISOString() : null
      })
      .select();
    
    if (error) throw error;
    return data;
  },

  async checkAndUnlockAchievements(userId) {
    try {
      // Get user stats
      const [jokesResult, likesResult, followersResult] = await Promise.all([
        supabase.from('jokes').select('id').eq('user_id', userId),
        supabase.from('joke_likes').select('id').eq('joke_id', 
          supabase.from('jokes').select('id').eq('user_id', userId)
        ),
        supabase.from('follows').select('id').eq('following_id', userId)
      ]);

      const jokeCount = jokesResult.data?.length || 0;
      const likeCount = likesResult.data?.length || 0;
      const followerCount = followersResult.data?.length || 0;

      // Get all achievements
      const achievements = await this.getAllAchievements();
      
      // Check each achievement
      for (const achievement of achievements) {
        let currentProgress = 0;
        
        switch (achievement.category) {
          case 'content':
            currentProgress = jokeCount;
            break;
          case 'engagement':
            if (achievement.title.includes('إعجاب')) {
              currentProgress = likeCount;
            }
            break;
          case 'social':
            if (achievement.title.includes('متابع')) {
              currentProgress = followerCount;
            }
            break;
        }

        // Update progress if it meets the requirement
        if (currentProgress >= achievement.requirement) {
          await this.updateProgress(userId, achievement.id, achievement.requirement);
        }
      }
    } catch (error) {
      console.error('Error checking achievements:', error);
    }
  }
};

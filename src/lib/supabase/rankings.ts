import { supabase } from '../../lib/supabase';

export interface UserRanking {
  id: string;
  season_id: string;
  user_id: string;
  points: number;
  rank: 'BRONZE' | 'SILVER' | 'GOLD' | 'DIAMOND';
  updated_at: string;
  user?: {
    username: string;
    avatar_url: string;
  };
}

export const rankingService = {
  async getCurrentSeason(): Promise<string> {
    const now = new Date();
    return `${now.getFullYear()}-${Math.floor(now.getMonth() / 3) + 1}`;
  },

  async updateUserRanking(userId: string, points: number): Promise<void> {
    const season = await this.getCurrentSeason();
    
    const { error } = await supabase
      .from('seasonal_rankings')
      .upsert({
        season_id: season,
        user_id: userId,
        points,
        rank: this.calculateRank(points),
        updated_at: new Date().toISOString()
      });

    if (error) throw error;
  },

  calculateRank(points: number): UserRanking['rank'] {
    if (points >= 25000) return 'DIAMOND';
    if (points >= 5000) return 'GOLD';
    if (points >= 1000) return 'SILVER';
    return 'BRONZE';
  },

  async getLeaderboard(season?: string): Promise<UserRanking[]> {
    const currentSeason = season || await this.getCurrentSeason();
    
    const { data, error } = await supabase
      .from('seasonal_rankings')
      .select(`
        *,
        user:users(username, avatar_url)
      `)
      .eq('season_id', currentSeason)
      .order('points', { ascending: false })
      .limit(100);

    if (error) throw error;
    return data;
  }
}; 
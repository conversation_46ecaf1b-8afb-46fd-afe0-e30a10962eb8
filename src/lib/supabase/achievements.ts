import { supabase } from '../../lib/supabase';
import { notificationService } from '../services/notification-service';

export interface Achievement {
  id: string;
  code: string;
  title: string;
  description: string;
  icon: string;
  requirement: number;
  xpReward: number;
  badgeColor: string;
  category: string;
}

export interface UserAchievement {
  achievementId: string;
  progress: number;
  unlockedAt: Date | null;
  achievement?: Achievement;
}

export const achievementService = {
  async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievement:achievements(*)
      `)
      .eq('user_id', userId);

    if (error) throw error;
    return data;
  },

  async checkAchievements(userId: string): Promise<void> {
    const { data: achievements } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievement:achievements(*)
      `)
      .eq('user_id', userId)
      .is('unlocked_at', null);

    for (const userAchievement of achievements || []) {
      if (userAchievement.progress >= userAchievement.achievement.requirement) {
        await this.unlockAchievement(userId, userAchievement.achievement_id);
      }
    }
  },

  async unlockAchievement(userId: string, achievementId: string): Promise<void> {
    const { data: achievement } = await supabase
      .from('achievements')
      .select('*')
      .eq('id', achievementId)
      .single();

    if (!achievement) throw new Error('Achievement not found');

    const { error } = await supabase
      .from('user_achievements')
      .update({ 
        unlocked_at: new Date().toISOString(),
        progress: achievement.requirement 
      })
      .match({ user_id: userId, achievement_id: achievementId });

    if (error) throw error;

    await notificationService.create({
      userId,
      type: 'ACHIEVEMENT_UNLOCKED',
      data: { achievementId }
    });
  }
}; 
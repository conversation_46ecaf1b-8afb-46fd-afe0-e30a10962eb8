import { supabase } from '../supabase';

export const generateTestData = async (userId: string) => {
  try {
    // 1. Create some jokes for the user
    const { data: jokes } = await supabase
      .from('jokes')
      .insert([
        {
          title: 'My First Trending Joke',
          description: 'This is going viral!',
          views_count: 1500,
          likes_count: 300,
          comments_count: 50
        },
        {
          title: 'Almost Achievement Joke',
          description: 'Getting closer to that badge!',
          views_count: 800,
          likes_count: 150,
          comments_count: 25
        }
      ])
      .select();

    // 2. Update achievement progress
    await supabase
      .from('user_achievements')
      .upsert([
        {
          user_id: userId,
          achievement_id: 'rookie_joker_id',
          progress: 8
        }
      ]);

    // 3. Add some ranking points
    await supabase
      .from('seasonal_rankings')
      .upsert({
        user_id: userId,
        season_id: '2024-1',
        points: 6000,
        rank: 'GOLD'
      });

    // 4. Create a spotlight
    if (jokes?.[0]?.id) {
      await supabase
        .from('community_spotlights')
        .insert({
          user_id: userId,
          category: 'WEEKLY_SPOTLIGHT',
          start_date: new Date().toISOString(),
          end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          metrics: {
            views: 1500,
            likes: 300,
            comments: 50,
            shares: 25
          },
          featured_joke_id: jokes[0].id
        });
    }

    console.log('Test data generated successfully!');
    return true;
  } catch (error) {
    console.error('Error generating test data:', error);
    return false;
  }
}; 
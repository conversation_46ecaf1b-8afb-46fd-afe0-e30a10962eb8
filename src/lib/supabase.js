import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client immediately
let supabaseInstance = null;

function getSupabaseClient() {
  if (!supabaseInstance) {
    // Clear any corrupted auth data on initialization
    try {
      const authKey = 'noukta-auth';
      const existingAuth = localStorage.getItem(`sb-${process.env.REACT_APP_SUPABASE_URL?.split('//')[1]?.split('.')[0]}-auth-token`);
      if (existingAuth) {
        try {
          JSON.parse(existingAuth);
        } catch (e) {
          // Clear corrupted auth data
          localStorage.removeItem(`sb-${process.env.REACT_APP_SUPABASE_URL?.split('//')[1]?.split('.')[0]}-auth-token`);
          localStorage.removeItem(authKey);
        }
      }
    } catch (e) {
      console.warn('Auth cleanup failed:', e);
    }

    supabaseInstance = createClient(
      process.env.REACT_APP_SUPABASE_URL,
      process.env.REACT_APP_SUPABASE_ANON_KEY,
      {
        auth: {
          persistSession: true,
          storageKey: 'noukta-auth'
        },
        db: {
          schema: 'public'
        },
        global: {
          headers: {
            'X-Client-Info': 'noukta-web'
          }
        },
        realtime: {
          params: {
            eventsPerSecond: 2
          }
        },
        // Add connection pooling and timeout settings
        fetch: (url, options = {}) => {
          return fetch(url, {
            ...options,
            signal: AbortSignal.timeout(30000), // 30 second timeout
          });
        }
      }
    );
  }
  return supabaseInstance;
}

const supabase = getSupabaseClient();

// Export the initialized client
export { supabase };

// Connection cleanup utility
export const cleanupConnections = () => {
  if (supabaseInstance) {
    try {
      // Remove all channels to prevent connection leaks
      supabaseInstance.removeAllChannels();
      console.log('🧹 Cleaned up Supabase connections');
    } catch (error) {
      console.warn('Connection cleanup failed:', error);
    }
  }
};

// Auto cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupConnections);

  // Periodic cleanup every 30 seconds
  setInterval(() => {
    if (supabaseInstance) {
      const channels = supabaseInstance.getChannels();
      if (channels.length > 10) {
        console.warn(`🚨 Too many channels: ${channels.length}, cleaning up...`);
        cleanupConnections();
      }
    }
  }, 30000);
}

// Helper functions that use the initialized client
export const jokes = {
  create: async (jokeData) => {
    // Do NOT include user_id; Supabase will set it automatically for the authenticated user
    const { data, error } = await supabase
      .from('jokes')
      .insert([jokeData])
      .select()
    return { data, error }
  },
  
  getRecent: async (limit = 10) => {
    const { data, error } = await supabase
      .from('jokes')
      .select('*')
      .eq('is_private', false)
      .order('created_at', { ascending: false })
      .limit(limit)
    return { data, error }
  },

  getTop: async (limit = 10) => {
    const { data, error } = await supabase
      .from('jokes')
      .select('*')
      .eq('is_private', false)
      .order('likes_count', { ascending: false })
      .limit(limit)
    return { data, error }
  }
}

// Reactions related operations
export const reactions = {
  create: async (reactionData) => {
    const { data, error } = await supabase
      .from('reactions')
      .insert([reactionData])
      .select()
    return { data, error }
  },
  
  getForJoke: async (jokeId) => {
    const { data, error } = await supabase
      .from('emoji_reactions')
      .select('*')
      .eq('joke_id', jokeId)
    return { data, error }
  }
}

// User profiles operations
export const profiles = {
  update: async (userId, updates) => {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
    return { data, error }
  },
  
  get: async (username) => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .single()
    return { data, error }
  }
}
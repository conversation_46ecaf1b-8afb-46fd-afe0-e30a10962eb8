import { createClient } from '@supabase/supabase-js'

// Validate environment variables
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

console.log('🔧 Initializing Supabase client...');
console.log('🔧 URL:', supabaseUrl);
console.log('🔧 Key length:', supabaseAnonKey.length);

// Create Supabase client with optimized configuration
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'noukta-auth',
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: {
      'X-Client-Info': 'noukta-web-v2'
    },
    fetch: (url, options = {}) => {
      console.log('🌐 Supabase fetch:', url.split('/').pop());

      // Create AbortController for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('⏰ Request timeout for:', url.split('/').pop());
        controller.abort();
      }, 15000); // 15 second timeout

      return fetch(url, {
        ...options,
        signal: controller.signal,
      }).finally(() => {
        clearTimeout(timeoutId);
      });
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 1
    }
  }
});

console.log('✅ Supabase client initialized successfully');

// Export the initialized client
export { supabase };

// Connection cleanup utility
export const cleanupConnections = () => {
  if (supabase) {
    try {
      // Remove all channels to prevent connection leaks
      supabase.removeAllChannels();
      console.log('🧹 Cleaned up Supabase connections');
    } catch (error) {
      console.warn('Connection cleanup failed:', error);
    }
  }
};

// Auto cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupConnections);

  // Periodic cleanup every 30 seconds
  setInterval(() => {
    if (supabase) {
      const channels = supabase.getChannels();
      if (channels.length > 10) {
        console.warn(`🚨 Too many channels: ${channels.length}, cleaning up...`);
        cleanupConnections();
      }
    }
  }, 30000);
}

// Helper functions that use the initialized client
export const jokes = {
  create: async (jokeData) => {
    // Do NOT include user_id; Supabase will set it automatically for the authenticated user
    const { data, error } = await supabase
      .from('jokes')
      .insert([jokeData])
      .select()
    return { data, error }
  },
  
  getRecent: async (limit = 10) => {
    const { data, error } = await supabase
      .from('jokes')
      .select('*')
      .eq('is_private', false)
      .order('created_at', { ascending: false })
      .limit(limit)
    return { data, error }
  },

  getTop: async (limit = 10) => {
    const { data, error } = await supabase
      .from('jokes')
      .select('*')
      .eq('is_private', false)
      .order('likes_count', { ascending: false })
      .limit(limit)
    return { data, error }
  }
}

// Reactions related operations
export const reactions = {
  create: async (reactionData) => {
    const { data, error } = await supabase
      .from('reactions')
      .insert([reactionData])
      .select()
    return { data, error }
  },
  
  getForJoke: async (jokeId) => {
    const { data, error } = await supabase
      .from('emoji_reactions')
      .select('*')
      .eq('joke_id', jokeId)
    return { data, error }
  }
}

// User profiles operations
export const profiles = {
  update: async (userId, updates) => {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
    return { data, error }
  },
  
  get: async (username) => {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('username', username)
      .single()
    return { data, error }
  }
}
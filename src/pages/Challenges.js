import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { Trophy, Clock, Users } from 'lucide-react';
import { supabase } from '../lib/supabase';

import LoadingSpinner from '../components/common/LoadingSpinner';

const Challenges = () => {
  const { t, i18n } = useTranslation();
  const [challenges, setChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchChallenges = async () => {
      try {
        const { data, error } = await supabase
          .from('challenges')
          .select(`
            *,
            participants:challenge_participants(count),
            jokes:challenge_jokes(count)
          `)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setChallenges(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchChallenges();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 p-4">
        Error loading challenges: {error}
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('challenges.title')}</h1>
        <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
          {t('challenges.createNew')}
        </button>
      </div>

      <div className="grid gap-6">
        {challenges.map(challenge => (
          <div key={challenge.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-4 mb-4">
              <Trophy className="w-8 h-8 text-yellow-500" />
              <div>
                <h2 className="text-xl font-bold">{challenge.title}</h2>
                <p className="text-gray-600">{challenge.description}</p>
              </div>
            </div>

            <div className="flex gap-6 text-sm text-gray-500 mb-4">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{t('challenges.endsIn')} {new Date(challenge.end_date).toLocaleDateString(i18n.language)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>{challenge.participants[0]?.count || 0} {t('challenges.participants')}</span>
              </div>
            </div>

            <button className="w-full bg-indigo-50 text-indigo-600 py-2 rounded-lg hover:bg-indigo-100">
              {t('challenges.participate')}
            </button>
          </div>
        ))}

        {challenges.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            {t('challenges.noChallenges')}
          </div>
        )}
      </div>
    </div>
  );
};

export default Challenges;
import { useTranslation } from 'react-i18next';
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { 
  User, Settings, Share2, Calendar, 
  Heart, Mic, MessageCircle, LogOut, Flag
} from 'lucide-react';
import { reportService } from '../services/reportService';
import { socialService } from '../lib/services/social-service';
import JokeCard from '../components/jokes/JokeCard';
import EditProfileForm from '../components/profile/EditProfileForm';
import { formatTimeAgo } from '../utils/timeFormat';
import { achievementService } from '../lib/supabase/achievements';
import { Award } from 'lucide-react';

const ITEMS_PER_PAGE = 10;

const Profile = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();

  // Debug logging to track re-renders
  console.log('Profile component render - id:', id, 'user?.id:', user?.id);
  const [profile, setProfile] = useState(null);
  const [activeTab, setActiveTab] = useState('jokes');
  const [items, setItems] = useState([]);
  const [stats, setStats] = useState({
    totalJokes: 0,
    totalLikes: 0,
    totalComments: 0,
    followers: 0,
    following: 0
  });
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [error, setError] = useState(null);
  const [achievements, setAchievements] = useState([]);
  // const [rankings, setRankings] = useState(null); // Temporarily disabled

  // Ref to track last fetched profile to prevent duplicate fetches
  const lastFetchedId = useRef(null);

  const isOwnProfile = user?.id === profile?.id;

  const fetchUserStats = useCallback(async (profileId) => {
    try {
      // Simplified stats fetching - get basic counts only
      const { count: totalJokes } = await supabase
        .from('jokes')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', profileId);

      // Get total likes received on user's jokes
      const { data: userJokes } = await supabase
        .from('jokes')
        .select('id')
        .eq('user_id', profileId);

      const jokeIds = userJokes?.map(joke => joke.id) || [];

      let totalLikes = 0;
      let totalComments = 0;

      if (jokeIds.length > 0) {
        try {
          // Get total likes - validate UUIDs first
          const validJokeIds = jokeIds.filter(id => id && typeof id === 'string' && id.length === 36);

          if (validJokeIds.length > 0) {
            const { count: likesCount, error: likesError } = await supabase
              .from('joke_likes')
              .select('id', { count: 'exact', head: true })
              .in('joke_id', validJokeIds);

            if (likesError) {
              console.error('Error fetching likes count:', likesError);
            } else {
              totalLikes = likesCount || 0;
            }

            // Get total comments
            const { count: commentsCount, error: commentsError } = await supabase
              .from('comments')
              .select('id', { count: 'exact', head: true })
              .in('joke_id', validJokeIds);

            if (commentsError) {
              console.error('Error fetching comments count:', commentsError);
            } else {
              totalComments = commentsCount || 0;
            }
          }
        } catch (error) {
          console.error('Error fetching stats:', error);
          // Continue with default values
        }
      }

      // Get followers count
      let followers = 0;
      let following = 0;

      try {
        const { count: followersCount, error: followersError } = await supabase
          .from('follows')
          .select('id', { count: 'exact', head: true })
          .eq('following_id', profileId);

        if (followersError) {
          console.error('Error fetching followers count:', followersError);
        } else {
          followers = followersCount || 0;
        }

        // Get following count
        const { count: followingCount, error: followingError } = await supabase
          .from('follows')
          .select('id', { count: 'exact', head: true })
          .eq('follower_id', profileId);

        if (followingError) {
          console.error('Error fetching following count:', followingError);
        } else {
          following = followingCount || 0;
        }
      } catch (error) {
        console.error('Error fetching follow stats:', error);
        // Continue with default values
      }

      setStats({
        totalJokes: totalJokes || 0,
        totalLikes: totalLikes || 0,
        totalComments: totalComments || 0,
        followers: followers || 0,
        following: following || 0
      });

      if (user && user.id !== profileId) {
        // Check if current user is following this profile
        const { data: followData } = await supabase
          .from('follows')
          .select('id')
          .eq('follower_id', user.id)
          .eq('following_id', profileId)
          .single();

        setIsFollowing(!!followData);

        // Check if current user has blocked this profile
        const { data: blockData } = await supabase
          .from('user_blocks')
          .select('id')
          .eq('blocker_id', user.id)
          .eq('blocked_id', profileId)
          .single();

        setIsBlocked(!!blockData);
      }

    } catch (error) {
      console.error('Error fetching user stats:', error);
      setStats({
        totalJokes: 0,
        totalLikes: 0,
        totalComments: 0,
        followers: 0,
        following: 0
      });
    }
  }, [user, setStats]);

  const fetchItems = useCallback(async (pageNum, profileId) => {
    if (!profileId) return;

    try {
      setLoadingMore(true);
      const offset = pageNum * ITEMS_PER_PAGE;

      let query;
      let transformData = (data) => data;

      switch (activeTab) {
        case 'jokes':
          query = supabase
            .from('jokes')
            .select(`
              *,
              profiles!user_id (username, avatar_url)
            `)
            .eq('user_id', profileId)
            .eq('is_private', false)
            .order('created_at', { ascending: false })
            .range(offset, offset + ITEMS_PER_PAGE - 1);
          break;

        case 'likes':
          query = supabase
            .from('joke_likes')
            .select(`
              joke_id,
              created_at,
              jokes!joke_id (
                *,
                profiles!user_id (username, avatar_url)
              )
            `)
            .eq('user_id', profileId)
            .order('created_at', { ascending: false })
            .range(offset, offset + ITEMS_PER_PAGE - 1);
          transformData = (data) => (data || []).map(item => item.jokes).filter(Boolean);
          break;

        case 'comments':
          query = supabase
            .from('comments')
            .select(`
              joke_id,
              content,
              created_at,
              jokes!joke_id (
                *,
                profiles!user_id (username, avatar_url)
              )
            `)
            .eq('user_id', profileId)
            .order('created_at', { ascending: false })
            .range(offset, offset + ITEMS_PER_PAGE - 1);
          transformData = (data) => (data || []).map(item => item.jokes).filter(Boolean);
          break;

        default:
          query = supabase
            .from('jokes')
            .select(`
              *,
              profiles!user_id (username, avatar_url)
            `)
            .eq('user_id', profileId)
            .eq('is_private', false)
            .order('created_at', { ascending: false })
            .range(offset, offset + ITEMS_PER_PAGE - 1);
      }

      const { data, error } = await query;

      if (error) throw error;

      const transformedData = transformData(data || []);

      if (pageNum === 0) {
        setItems(transformedData);
      } else {
        setItems(current => [...current, ...transformedData]);
      }

      setHasMore(transformedData.length === ITEMS_PER_PAGE);
      setPage(pageNum);
    } catch (err) {
      console.error('Error fetching items:', err);
    } finally {
      setLoadingMore(false);
    }
  }, [activeTab]);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const targetId = id || user?.id;

        if (!targetId) {
          throw new Error(t('profile.loginRequired'));
        }

        // Reset profile state before fetching
        setProfile(null);


        lastFetchedId.current = targetId;
        setLoading(true);
        setError(null);

        let profileData, profileError;

        // Check if targetId is a UUID (user ID) or username
        if (targetId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          // It's a UUID, fetch by ID
          const result = await supabase
            .from('profiles')
            .select('*')
            .eq('id', targetId)
            .maybeSingle();
          profileData = result.data;
          profileError = result.error;

        } else {
          // It's a username, fetch by username
          const result = await supabase
            .from('profiles')
            .select('*')
            .eq('username', targetId)
            .maybeSingle();
          profileData = result.data;
          profileError = result.error;
          console.log('Profile by username:', profileData, profileError);
        }

        if (profileError) {
          console.error('Profile fetch error:', profileError);
          throw profileError;
        }

        if (!profileData) {
          throw new Error(t('profile.notFound'));
        }

        setProfile(profileData);


        await fetchUserStats(profileData.id);
        
        await fetchItems(0, profileData.id);

      } catch (err) {
        console.error('Profile page error:', err);
        setError(err.message);
        if (err.message.includes(t('profile.loginRequired'))) {
          navigate('/auth', { state: { from: '/profile' } });
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();

    // Cleanup function to reset ref when dependencies change
    return () => {
      if (lastFetchedId.current !== (id || user?.id)) {
        lastFetchedId.current = null;
      }
    };
  }, [id, user?.id, fetchItems, fetchUserStats, navigate, t]);

  useEffect(() => {
    const loadProfileData = async () => {
      if (!user?.id) return;

      try {
        // Load achievements
        const userAchievements = await achievementService.getUserAchievements(user.id);
        setAchievements(userAchievements);

        // Skip rankings for now - table may not exist or have permission issues
        // const { data: rankingData } = await supabase
        //   .from('seasonal_rankings')
        //   .select('*')
        //   .eq('user_id', user.id)
        //   .single();
        // setRankings(rankingData);
      } catch (error) {
        console.error('Error loading profile data:', error);
      }
    };

    loadProfileData();
  }, [user?.id]);

  // Reload items when active tab changes
  useEffect(() => {
    if (profile?.id && !loading) {
      fetchItems(0, profile.id);
    }
  }, [activeTab, profile?.id, loading, fetchItems]);

  const handleFollow = async () => {
    if (!user) {
      navigate('/auth', { state: { from: `/profile/${id}` } });
      return;
    }

    try {
      if (isFollowing) {
        await socialService.unfollowUser(profile.id);
        setStats(prev => ({
          ...prev,
          followers: prev.followers - 1
        }));
      } else {
        await socialService.followUser(profile.id);
        setStats(prev => ({
          ...prev,
          followers: prev.followers + 1
        }));
      }
      
      setIsFollowing(!isFollowing);
    } catch (err) {
      console.error('Error toggling follow:', err);
    }
  };

  const handleBlock = async () => {
    if (!user) {
      navigate('/auth', { state: { from: `/profile/${id}` } });
      return;
    }

    try {
      if (isBlocked) {
        await socialService.unblockUser(profile.id);
      } else {
        await socialService.blockUser(profile.id);
      }
      setIsBlocked(!isBlocked);
    } catch (err) {
      console.error('Error toggling block:', err);
    }
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: `${profile.username} على نكتة`,
        text: `تابع ${profile.username} على نكتة`,
        url: window.location.href
      });
    } catch (err) {
      console.error('Error sharing:', err);
    }
  };

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut();
      navigate('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      {loading ? (
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg text-center">
          {error}
        </div>
      ) : profile ? (
        <>
          {isEditing && profile ? (
            <EditProfileForm
              profile={profile}
              onClose={() => setIsEditing(false)}
              onUpdate={(updatedData) => {
                setProfile(prev => ({ ...prev, ...updatedData }));
                setIsEditing(false);
              }}
            />
          ) : (
            <>
              {isOwnProfile && (!profile.avatar_url || !profile.bio) && (
                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 shadow-md rounded-lg">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <User className="h-6 w-6 text-yellow-400" aria-hidden="true" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-800">
                        {t('profile.incompleteProfileMessage')}
                      </p>
                    </div>
                    <div className="ml-auto pl-3">
                      <div className="-mx-1.5 -my-1.5">
                        <button
                          type="button"
                          onClick={() => setIsEditing(true)}
                          className="inline-flex bg-yellow-50 rounded-md p-1.5 text-yellow-500 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                        >
                          <span className="sr-only">{t('common.dismiss')}</span>
                          {t('profile.completeProfile')}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {/* Profile Header */}
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {profile.avatar_url ? (
                      <img 
                        src={profile.avatar_url} 
                        alt={profile.username}
                        className="w-24 h-24 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-24 h-24 p-6 bg-gray-100 rounded-full" />
                    )}
                    <div>
                      <h1 className="text-2xl font-bold">{profile.username}</h1>
                      {profile.bio && (
                        <p className="text-gray-600 mt-1">{profile.bio}</p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          انضم {formatTimeAgo(profile.created_at)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {isOwnProfile ? (
                      <>
                        <button
                          onClick={() => setIsEditing(true)}
                          className="p-2 text-gray-500 hover:bg-gray-100 rounded-full"
                          title={t('profile.editProfile')}
                        >
                          <Settings className="w-5 h-5" />
                        </button>
                        <button
                          onClick={handleLogout}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-full"
                          title={t('auth.signOut')}
                        >
                          <LogOut className="w-5 h-5" />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={handleFollow}
                          className={`px-4 py-2 rounded-lg font-medium ${
                            isFollowing
                              ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              : 'bg-indigo-600 text-white hover:bg-indigo-700'
                          }`}
                        >
                          {isFollowing ? t('social.unfollow') : t('social.follow')}
                        </button>
                        <button
                          onClick={handleShare}
                          className="p-2 text-gray-500 hover:bg-gray-100 rounded-full"
                        >
                          <Share2 className="w-5 h-5" />
                        </button>
                        <button
                          onClick={async () => {
                            if (window.confirm('Are you sure you want to report this user?')) {
                              try {
                                await reportService.submitReport(profile.id, 'user', 'Inappropriate profile content');
                                alert('User reported successfully!');
                              } catch (err) {
                                console.error('Error reporting user:', err);
                                alert('Failed to report user.');
                              }
                            }
                          }}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-full"
                          title={t('common.reportUser')}
                        >
                          <Flag className="w-5 h-5" />
                        </button>
                        <button
                          onClick={handleBlock}
                          className={`px-4 py-2 rounded-lg font-medium ${
                            isBlocked
                              ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              : 'bg-red-600 text-white hover:bg-red-700'
                          }`}
                        >
                          {isBlocked ? t('social.unblock') : t('social.block')}
                        </button>
                      </>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-5 gap-4 mt-6 text-center">
                  <div>
                    <div className="text-2xl font-bold">{stats.totalJokes}</div>
                    <div className="text-sm text-gray-500">{t('profile.jokes')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.totalLikes}</div>
                    <div className="text-sm text-gray-500">{t('social.likes')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.totalComments}</div>
                    <div className="text-sm text-gray-500">{t('social.comments')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.followers}</div>
                    <div className="text-sm text-gray-500">{t('social.followers')}</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{stats.following}</div>
                    <div className="text-sm text-gray-500">{t('social.following')}</div>
                  </div>
                </div>
              </div>

              {/* Tabs */}
              <div className="bg-white rounded-lg shadow mb-6">
                <div className="flex border-b">
                  <button
                    onClick={() => setActiveTab('jokes')}
                    className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 ${
                      activeTab === 'jokes'
                        ? 'border-indigo-600 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Mic className="w-4 h-4" />
                    {t('profile.jokes')}
                  </button>
                  <button
                    onClick={() => setActiveTab('likes')}
                    className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 ${
                      activeTab === 'likes'
                        ? 'border-indigo-600 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Heart className="w-4 h-4" />
                    {t('social.likes')}
                  </button>
                  <button
                    onClick={() => setActiveTab('comments')}
                    className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 ${
                      activeTab === 'comments'
                        ? 'border-indigo-600 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <MessageCircle className="w-4 h-4" />
                    {t('social.comments')}
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="space-y-4">
                {items.map(item => (
                  <div key={item.id} className="bg-white rounded-lg shadow">
                    <JokeCard joke={item} />
                  </div>
                ))}

                {loadingMore && (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                  </div>
                )}

                {!loading && !loadingMore && items.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    {t('common.noContent')}
                  </div>
                )}

                {hasMore && !loadingMore && (
                  <button
                    onClick={() => fetchItems(page + 1)}
                    className="w-full py-2 text-indigo-600 hover:bg-indigo-50 rounded-lg"
                  >
                    {t('common.loadMore')}
                  </button>
                )}
              </div>

              {/* Achievements Section */}
              <div className="mt-8 bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                  <Award className="text-yellow-500" />
                  {t('achievements.title')}
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {achievements.map((achievement) => (
                    <div 
                      key={achievement.achievement_id}
                      className="bg-gray-50 rounded-lg p-4 text-center"
                    >
                      <div className="text-3xl mb-2">{achievement.achievement?.icon}</div>
                      <h3 className="font-semibold">{achievement.achievement?.title}</h3>
                      <p className="text-sm text-gray-600">{achievement.achievement?.description}</p>
                      <div className="mt-2">
                        {achievement.unlocked_at ? (
                          <span className="text-green-500 text-sm">{t('achievements.unlocked')} 🎉</span>
                        ) : (
                          <span className="text-gray-500 text-sm">
                            {achievement.progress} / {achievement.achievement?.requirement}
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Rankings Section - Temporarily disabled */}
              {/* {rankings && (
                <div className="mt-8 bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
                    <Trophy className="text-orange-500" />
                    {t('rankings.seasonRankings')}
                  </h2>
                  <div className="flex items-center gap-4">
                    <div className="bg-gradient-to-r from-orange-400 to-orange-600 rounded-lg p-6 text-white">
                      <div className="text-sm">{t('rankings.currentRank')}</div>
                      <div className="text-3xl font-bold">{rankings.rank}</div>
                    </div>
                    <div className="bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg p-6 text-white">
                      <div className="text-sm">{t('rankings.seasonPoints')}</div>
                      <div className="text-3xl font-bold">{rankings.points}</div>
                    </div>
                    <div className="bg-gradient-to-r from-purple-400 to-purple-600 rounded-lg p-6 text-white">
                      <div className="text-sm">{t('rankings.season')}</div>
                      <div className="text-xl font-bold">{rankings.season_id}</div>
                    </div>
                  </div>
                </div>
              )} */}
            </>
          )}
        </>
      ) : (
        <div className="text-center text-gray-500">
          {t('profile.notFound')}
        </div>
      )}
    </div>
  );
};

export default Profile;
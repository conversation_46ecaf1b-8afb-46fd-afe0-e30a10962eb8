import React, { useState, useEffect } from 'react';

const TestPage = () => {
  const [status, setStatus] = useState('Testing...');
  const [jokes, setJokes] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        console.log('🧪 Test: Starting connection test...');
        setStatus('Importing Supabase...');
        
        const { supabase } = await import('../lib/supabase');
        console.log('🧪 Test: Supabase imported');
        setStatus('Testing simple query...');
        
        const { data, error } = await supabase
          .from('jokes')
          .select('id, title, created_at')
          .limit(3);
        
        console.log('🧪 Test: Query result:', { data, error });
        
        if (error) {
          throw error;
        }
        
        setJokes(data || []);
        setStatus(`Success! Found ${data?.length || 0} jokes`);
        
      } catch (err) {
        console.error('🧪 Test: Error:', err);
        setError(err.message);
        setStatus('Failed!');
      }
    };
    
    testConnection();
  }, []);

  return (
    <div className="max-w-2xl mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">🧪 Connection Test</h1>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-4">
        <p><strong>Status:</strong> {status}</p>
        {error && <p className="text-red-600"><strong>Error:</strong> {error}</p>}
      </div>
      
      <div className="bg-blue-50 p-4 rounded-lg mb-4">
        <h3 className="font-semibold mb-2">Environment Check:</h3>
        <p><strong>Supabase URL:</strong> {process.env.REACT_APP_SUPABASE_URL ? '✅ Set' : '❌ Not set'}</p>
        <p><strong>Supabase Key:</strong> {process.env.REACT_APP_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Not set'}</p>
      </div>
      
      {jokes.length > 0 && (
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">✅ Sample Jokes Found:</h3>
          {jokes.map(joke => (
            <div key={joke.id} className="border-b py-2">
              <p><strong>Title:</strong> {joke.title}</p>
              <p><strong>ID:</strong> {joke.id}</p>
              <p><strong>Created:</strong> {new Date(joke.created_at).toLocaleString()}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TestPage;

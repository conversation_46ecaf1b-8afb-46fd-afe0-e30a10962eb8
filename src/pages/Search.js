import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Search as SearchIcon } from 'lucide-react';
import { supabase } from '../lib/supabase';

const Search = () => {
  const { t } = useTranslation();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!query.trim()) return;

    setLoading(true);
    setError(null);

    try {
      // Use ilike for case-insensitive search instead of textSearch
      const { data, error } = await supabase
        .from('jokes')
        .select(`
          *,
          profiles!user_id (username, avatar_url)
        `)
        .or(`title.ilike.%${query}%, content.ilike.%${query}%`)
        .eq('is_private', false)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setResults(data || []);
    } catch (err) {
      console.error('Search error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSearch} className="mb-8">
        <div className="relative">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={t('search.placeholder')}
            className="w-full p-4 pr-12 rounded-lg border focus:ring-2 focus:ring-indigo-500"
          />
          <button
            type="submit"
            className="absolute left-2 top-1/2 -translate-y-1/2 p-2 text-gray-500 hover:text-indigo-500"
          >
            <SearchIcon className="w-6 h-6" />
          </button>
        </div>
      </form>

      {error && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div>{t('common.searching')}</div>
      ) : (
        <div className="space-y-4">
          {results.map(joke => (
            <div key={joke.id} className="bg-white rounded-lg shadow p-4">
              <div className="flex items-center gap-2 mb-2">
                {joke.profiles.avatar_url ? (
                  <img 
                    src={joke.profiles.avatar_url} 
                    alt={joke.profiles.username}
                    className="w-8 h-8 rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gray-100 rounded-full" />
                )}
                <span className="font-medium">{joke.profiles.username}</span>
              </div>
              <audio src={joke.audio_url} controls className="w-full mb-2" />
              <h3 className="font-medium">{joke.title}</h3>
            </div>
          ))}
          {results.length === 0 && query && !loading && (
            <div className="text-center text-gray-500">
              {t('search.noResults')}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Search;
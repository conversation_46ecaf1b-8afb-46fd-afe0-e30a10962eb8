import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import LoadingSpinner from '../components/common/LoadingSpinner';

const TopJokes = () => {
  const { t } = useTranslation();
  const [jokes, setJokes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchTopJokes = async () => {
      try {
        const { data, error } = await supabase
          .from('jokes')
          .select(`
            *,
            profiles!user_id (username, avatar_url)
          `)
          .order('likes_count', { ascending: false })
          .limit(20);

        if (error) throw error;
        setJokes(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTopJokes();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 p-4">
        Error loading top jokes: {error}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {jokes.length === 0 ? (
        <div className="text-center text-gray-500 py-8">
          {t('jokes.noTopJokesFound')}
        </div>
      ) : (
        jokes.map(joke => (
          <div key={joke.id} className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center gap-2 mb-2">
              {joke.profiles.avatar_url ? (
                <img 
                  src={joke.profiles.avatar_url} 
                  alt={joke.profiles.username}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div className="w-8 h-8 bg-gray-100 rounded-full" />
              )}
              <span className="font-medium">{joke.profiles.username}</span>
            </div>
            <audio src={joke.audio_url} controls className="w-full mb-2" />
            <h3 className="font-medium">{joke.title}</h3>
            <div className="text-sm text-gray-500">❤️ {joke.likes_count}</div>
          </div>
        ))
      )}
    </div>
  );
};

export default TopJokes;
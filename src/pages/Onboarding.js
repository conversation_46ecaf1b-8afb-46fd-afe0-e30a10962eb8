import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { useTranslation } from 'react-i18next';

const Onboarding = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleCompleteOnboarding = async () => {
    setLoading(true);
    setError(null);
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      if (user) {
        const { error: updateError } = await supabase.auth.updateUser({
          data: { onboarded: true }
        });

        if (updateError) throw updateError;

        // Refresh the session to update AuthContext with new user metadata
        const { error: refreshError } = await supabase.auth.refreshSession();
        if (refreshError) throw refreshError;

        navigate('/'); // Redirect to home after onboarding
      } else {
        throw new Error('User not found.');
      }
    } catch (err) {
      console.error('Error completing onboarding:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">{t('onboarding.welcomeTitle')}</h2>
            <p className="text-gray-700 mb-6">{t('onboarding.welcomeText')}</p>
            <button
              onClick={() => setStep(2)}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              {t('onboarding.next')}
            </button>
          </div>
        );
      case 2:
        return (
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">{t('onboarding.featureTitle')}</h2>
            <ul className="list-disc list-inside text-left text-gray-700 mb-6 mx-auto max-w-sm">
              <li>{t('onboarding.feature1')}</li>
              <li>{t('onboarding.feature2')}</li>
              <li>{t('onboarding.feature3')}</li>
            </ul>
            <button
              onClick={handleCompleteOnboarding}
              disabled={loading}
              className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? t('common.loading') : t('onboarding.complete')}
            </button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-lg w-full">
        {error && (
          <div className="bg-red-100 text-red-700 p-3 rounded-lg mb-4">
            {error}
          </div>
        )}
        {renderStep()}
      </div>
    </div>
  );
};

export default Onboarding;

import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import JokeCard from '../components/jokes/JokeCard';
import CategoryFilter from '../components/categories/CategoryFilter';
import SearchBar from '../components/search/SearchBar';
import LoadingSpinner from '../components/common/LoadingSpinner';

const Home = () => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');


  // Simplified state management
  const [jokes, setJokes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);

  // Simple fetch function
  const fetchJokes = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const { supabase } = await import('../lib/supabase');

      let query = supabase
        .from('jokes')
        .select(`
          id,
          title,
          audio_url,
          user_id,
          created_at,
          likes_count,
          plays_count,
          comments_count,
          voice_effect,
          is_private,
          profiles!user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_private', false)
        .order('created_at', { ascending: false })
        .limit(20);

      // Apply category filter
      if (selectedCategory && selectedCategory !== 'all') {
        // For now, just get all jokes - category filtering can be added later
      }

      // Apply search filter
      if (searchQuery) {
        query = query.ilike('title', `%${searchQuery}%`);
      }

      const { data, error: fetchError } = await query;

      if (fetchError) {
        throw fetchError;
      }

      setJokes(data || []);
      setHasMore(false); // Simplified - no pagination for now
    } catch (err) {
      console.error('Error fetching jokes:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [selectedCategory, searchQuery]);

  // Fetch jokes on mount and when filters change
  useEffect(() => {
    fetchJokes();
  }, [fetchJokes]);

  const loadMore = () => {
    // Simplified - no load more for now
  };

  const refresh = () => {
    fetchJokes();
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {t('home.title')}
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          {t('home.subtitle')}
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        <SearchBar onSearch={handleSearch} />
        <CategoryFilter
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />
      </div>

      {/* Error State */}
      {error && (
        <div className="text-center py-4 text-red-500 mb-6">
          <p>{error}</p>
          <button
            onClick={refresh}
            className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
          >
            {t('common.retry')}
          </button>
        </div>
      )}

      {/* Jokes Grid */}
      <div className="space-y-6">
        {jokes.length > 0 ? (
          jokes.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
            />
          ))
        ) : !loading ? (
          <div className="text-center py-12">
            <p className="text-gray-500">{t('home.noJokes')}</p>
          </div>
        ) : null}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {/* Load More Button */}
      {!loading && hasMore && jokes.length > 0 && (
        <div className="text-center mt-8">
          <button
            onClick={loadMore}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.loadMore')}
          </button>
        </div>
      )}
    </div>
  );
};

export default Home;
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../components/common/LoadingSpinner';
import JokeCard from '../components/jokes/JokeCard';
import { supabase } from '../lib/supabase';

const SimpleHome = () => {
  const { t } = useTranslation();
  const [jokes, setJokes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true; // Prevent double execution

    const loadJokes = async () => {
      if (!isMounted) {
        console.log('🔥 SimpleHome: Skipping - component unmounted');
        return;
      }

      const startTime = Date.now();
      try {
        console.log('🔥🔥🔥 SimpleHome: STARTING FRESH LOAD - Timestamp:', startTime);
        setLoading(true);
        setError(null);
        setJokes([]);

        console.log('🔥 SimpleHome: Using static Supabase client...');
        console.log('🔥 SimpleHome: Supabase URL:', process.env.REACT_APP_SUPABASE_URL?.substring(0, 30) + '...');

        // Test connection first
        console.log('🔥 SimpleHome: Testing connection...');
        try {
          const { data: testData, error: testError } = await supabase
            .from('jokes')
            .select('id')
            .limit(1);
          console.log('🔥 SimpleHome: Connection test result:', { testData, testError });
        } catch (testErr) {
          console.error('🔥 SimpleHome: Connection test failed:', testErr);
        }

        console.log('🔥 SimpleHome: About to execute main query...');
        const queryStart = Date.now();

        const { data, error: queryError } = await supabase
          .from('jokes')
          .select(`
            id,
            title,
            audio_url,
            user_id,
            created_at,
            likes_count,
            plays_count,
            comments_count,
            voice_effect,
            is_private,
            profiles!user_id (
              username,
              avatar_url
            )
          `)
          .eq('is_private', false)
          .order('created_at', { ascending: false })
          .limit(10);

        const queryEnd = Date.now();
        console.log('🔥 SimpleHome: Query completed in', queryEnd - queryStart, 'ms');
        console.log('🔥 SimpleHome: Query result:', { data, queryError });

        if (queryError) {
          console.error('🔥 SimpleHome: Query error:', queryError);
          throw queryError;
        }

        if (!isMounted) return; // Check again before setting state

        console.log('🔥 SimpleHome: Setting jokes state...');
        setJokes(data || []);
        console.log('🔥 SimpleHome: Jokes state set successfully -', data?.length || 0, 'jokes');

      } catch (err) {
        console.error('🔥 SimpleHome: CATCH ERROR:', err);
        if (isMounted) {
          setError(err.message);
        }
      } finally {
        if (isMounted) {
          const endTime = Date.now();
          setLoading(false);
          console.log('🔥🔥🔥 SimpleHome: LOADING COMPLETED - Total time:', endTime - startTime, 'ms');
        }
      }
    };

    console.log('🔥 SimpleHome: useEffect triggered');
    loadJokes();

    return () => {
      isMounted = false;
      console.log('🔥 SimpleHome: Cleanup - setting isMounted to false');
    };
  }, []);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading jokes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {t('home.title')}
        </h1>
        <p className="text-gray-600">
          🔥 FRESH SIMPLE HOME v2.0 - {jokes.length} jokes found
        </p>
        <p className="text-xs text-gray-400">
          Loaded at: {new Date().toLocaleTimeString()}
        </p>
      </div>

      <div className="space-y-6">
        {jokes.length > 0 ? (
          jokes.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
              onLike={() => {}}
              currentChallenge={null}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No jokes found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleHome;

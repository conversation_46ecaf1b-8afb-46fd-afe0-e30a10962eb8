import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import LoadingSpinner from '../components/common/LoadingSpinner';
import JokeCard from '../components/jokes/JokeCard';
import { dataService } from '../services/dataService';

const SimpleHome = () => {
  const { t } = useTranslation();
  const [jokes, setJokes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;
    let loadingStarted = false;

    const loadJokes = async () => {
      if (!isMounted || loadingStarted) {
        console.log('🚀 SimpleHome: Skipping - unmounted or already loading');
        return;
      }

      loadingStarted = true;
      const startTime = Date.now();

      try {
        console.log('🚀🚀🚀 PHASE 1: STARTING DATA SERVICE LOAD');
        setLoading(true);
        setError(null);
        setJokes([]);

        // Test connection first
        console.log('🔌 Phase 1: Testing connection...');
        const healthCheck = await dataService.testConnection();

        if (!healthCheck.healthy) {
          throw new Error(`Connection failed: ${healthCheck.error}`);
        }

        console.log('✅ Phase 1: Connection healthy, fetching jokes...');

        // Fetch jokes using new data service
        const result = await dataService.getJokes({
          limit: 10,
          isPrivate: false,
          orderBy: 'created_at',
          ascending: false
        });

        if (!isMounted) return;

        if (!result.success) {
          throw new Error(result.error);
        }

        console.log('✅ Phase 1: Data service returned', result.data.length, 'jokes');
        setJokes(result.data);

      } catch (err) {
        console.error('❌ Phase 1: Error:', err);
        if (isMounted) {
          setError(err.message);
        }
      } finally {
        if (isMounted) {
          const endTime = Date.now();
          setLoading(false);
          console.log('🚀🚀🚀 PHASE 1: COMPLETED - Total time:', endTime - startTime, 'ms');
        }
      }
    };

    console.log('🚀 Phase 1: useEffect triggered');
    loadJokes();

    return () => {
      isMounted = false;
      console.log('🚀 Phase 1: Cleanup');
    };
  }, []);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading jokes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          {t('home.title')}
        </h1>
        <p className="text-gray-600">
          🔥 FRESH SIMPLE HOME v2.0 - {jokes.length} jokes found
        </p>
        <p className="text-xs text-gray-400">
          Loaded at: {new Date().toLocaleTimeString()}
        </p>
      </div>

      <div className="space-y-6">
        {jokes.length > 0 ? (
          jokes.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
              onLike={() => {}}
              currentChallenge={null}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No jokes found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleHome;

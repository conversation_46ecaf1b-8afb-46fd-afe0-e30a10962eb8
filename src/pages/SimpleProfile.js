import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/common/LoadingSpinner';
import JokeCard from '../components/jokes/JokeCard';
import { supabase } from '../lib/supabase';

const SimpleProfile = () => {
  const { id } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [jokes, setJokes] = useState([]);

  useEffect(() => {
    const loadProfile = async () => {
      try {
        console.log('🔥 SimpleProfile: Starting fresh load for:', id || user?.id);
        setLoading(true);
        setError(null);
        setProfile(null);
        setJokes([]);

        const targetId = id || user?.id;
        if (!targetId) {
          navigate('/auth');
          return;
        }

        console.log('🔥 SimpleProfile: Using static Supabase client');

        // Simple profile query
        console.log('🔥 SimpleProfile: Fetching profile...');
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', targetId)
          .single();

        console.log('🔥 SimpleProfile: Profile result:', { profileData, profileError });

        if (profileError) {
          throw new Error('Profile not found');
        }

        setProfile(profileData);
        console.log('🔥 SimpleProfile: Profile set:', profileData.username);

        // Simple jokes query
        console.log('🔥 SimpleProfile: Fetching user jokes...');
        const { data: jokesData, error: jokesError } = await supabase
          .from('jokes')
          .select(`
            id,
            title,
            audio_url,
            user_id,
            created_at,
            likes_count,
            plays_count,
            comments_count,
            voice_effect,
            is_private,
            profiles!user_id (
              username,
              avatar_url
            )
          `)
          .eq('user_id', targetId)
          .order('created_at', { ascending: false })
          .limit(10);

        console.log('🔥 SimpleProfile: Jokes result:', { jokesData, jokesError });

        if (!jokesError) {
          setJokes(jokesData || []);
        }

      } catch (err) {
        console.error('🔥 SimpleProfile: Error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
        console.log('🔥 SimpleProfile: Load completed');
      }
    };

    loadProfile();
  }, [id, user?.id, navigate]);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <LoadingSpinner />
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Error:</strong> {error}
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-gray-500">Profile not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Profile Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center gap-4">
          {profile.avatar_url && (
            <img
              src={profile.avatar_url}
              alt={profile.username}
              className="w-20 h-20 rounded-full"
            />
          )}
          <div>
            <h1 className="text-2xl font-bold">{profile.username}</h1>
            <p className="text-gray-600">{profile.bio || 'No bio available'}</p>
            <p className="text-sm text-gray-500">
              Joined {new Date(profile.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{jokes.length}</div>
          <div className="text-gray-600">Jokes</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <div className="text-2xl font-bold text-green-600">0</div>
          <div className="text-gray-600">Followers</div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">0</div>
          <div className="text-gray-600">Following</div>
        </div>
      </div>

      {/* Jokes */}
      <div className="space-y-6">
        <h2 className="text-xl font-bold">Recent Jokes</h2>
        {jokes.length > 0 ? (
          jokes.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
              onLike={() => {}}
              currentChallenge={null}
            />
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No jokes found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleProfile;

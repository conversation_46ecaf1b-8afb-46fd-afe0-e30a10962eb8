import React from 'react';
import { useTranslation } from 'react-i18next';
import { Clock } from 'lucide-react';
import Joke<PERSON>ard from '../components/jokes/JokeCard';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { useRecentJokes } from '../hooks/useJokes';

const RecentJokes = () => {
  const { t } = useTranslation();
  const {
    jokes,
    loading,
    error,
    hasMore,
    loadMore,
    refresh
  } = useRecentJokes(20);

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="text-center space-y-4 mb-8">
        <div className="flex justify-center">
          <Clock className="h-12 w-12 text-indigo-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900">
          {t('navigation.sections.recentJokes')}
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          {t('recentJokes.subtitle')}
        </p>
      </div>

      {/* Error State */}
      {error && (
        <div className="text-center py-4 text-red-500 mb-6">
          <p>{error}</p>
          <button
            onClick={refresh}
            className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
          >
            {t('common.retry')}
          </button>
        </div>
      )}

      {/* Jokes Grid */}
      <div className="space-y-6">
        {jokes.length > 0 ? (
          jokes.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
            />
          ))
        ) : !loading ? (
          <div className="text-center py-12">
            <p className="text-gray-500">{t('recentJokes.noJokes')}</p>
          </div>
        ) : null}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {/* Load More Button */}
      {!loading && hasMore && jokes.length > 0 && (
        <div className="text-center mt-8">
          <button
            onClick={loadMore}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            {t('common.loadMore')}
          </button>
        </div>
      )}
    </div>
  );
};

export default RecentJokes;
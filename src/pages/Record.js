import { useTranslation } from 'react-i18next';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAudioRecorder } from '../hooks/useAudioRecorder';
import { useAuth } from '../contexts/AuthContext';
import { categoryService } from '../services/categoryService';
import { Mic, StopCircle, Upload, Trash2, Volume2 } from 'lucide-react';

const Record = () => {
  const { t } = useTranslation();
  const {
    isRecording,
    audioUrl,
    error: recordingError,
    loading,
    startRecording,
    stopRecording,
    uploadAudio,
    resetRecording,
    duration,
  } = useAudioRecorder();
  
  const [title, setTitle] = useState('');
  const [effect, setEffect] = useState('normal');
  const [isPrivate, setIsPrivate] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleRecordClick = () => {
    if (!user) {
      navigate('/auth', { state: { from: '/record' } });
      return;
    }
    
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  useEffect(() => {
    setError(null);
  }, [isRecording]);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await categoryService.getAllCategories();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };

    loadCategories();
  }, []);

  const formatDuration = (seconds) => {
    // Handle invalid or undefined values
    if (!seconds || isNaN(seconds) || seconds < 0) {
      return '0:00';
    }

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (isRecording && duration >= 180) {
      stopRecording();
    }
  }, [isRecording, duration, stopRecording]);

  const handleUpload = async () => {
    console.log('handleUpload called'); // Debug log
    try {
      if (!title.trim()) {
        console.log('Title validation failed:', title);
        setError(t('recorder.titleRequired') || 'Title is required');
        return;
      }

      console.log('Starting upload with:', { title, effect, isPrivate, selectedCategories });
      const joke = await uploadAudio(title, effect, isPrivate);
      console.log('Upload result:', joke);

      if (joke) {
        // Add categories to the joke if any are selected
        if (selectedCategories.length > 0) {
          try {
            for (const categoryId of selectedCategories) {
              await categoryService.addJokeToCategory(joke.id, categoryId);
            }
          } catch (categoryError) {
            console.error('Error adding categories:', categoryError);
            // Don't fail the upload if category assignment fails
          }
        }

        console.log('Navigating to profile:', user.id);
        navigate(`/profile/${user.id}`);
      } else {
        console.log('Upload failed - no joke returned');
        setError('Upload failed. Please try again.');
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError('Error uploading joke: ' + err.message);
    }
  };

  const voiceEffects = [
    { id: 'normal', name: t('recorder.effects.normal') },
    { id: 'deep', name: t('recorder.effects.deep') },
    { id: 'high', name: t('recorder.effects.high') },
    { id: 'robot', name: t('recorder.effects.robot') },
    { id: 'echo', name: t('recorder.effects.echo') }
  ];

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">{t('recorder.title')}</h1>
      
      {(error || recordingError) && (
        <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-4">
          {error || recordingError}
        </div>
      )}

      <div className="space-y-6">
        {/* Title Input - Made more prominent */}
        <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-4">
          <label className="block text-lg font-semibold text-blue-900 mb-3">
            📝 {t('recorder.jokeTitle')} *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder={t('recorder.titlePlaceholder')}
            className="w-full px-4 py-3 text-lg border-2 border-blue-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
            maxLength={100}
          />
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-blue-600">* {t('recorder.required')}</span>
            <span className="text-sm text-gray-500">{title.length}/100</span>
          </div>
        </div>

        {!audioUrl ? (
          <div className="relative">
            <button
              onClick={handleRecordClick}
              disabled={!title.trim() && !isRecording}
              className={`w-full h-48 rounded-xl font-medium flex flex-col items-center justify-center gap-4 transition-all duration-300 ${
                !title.trim() && !isRecording
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : isRecording
                  ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse shadow-lg'
                  : 'bg-emerald-500 hover:bg-emerald-600 text-white shadow-lg hover:shadow-xl'
              }`}
            >
              {!title.trim() && !isRecording ? (
                <>
                  <Mic className="w-12 h-12 opacity-50" />
                  <span>{t('recorder.enterTitleFirst')}</span>
                </>
              ) : isRecording ? (
                <>
                  <StopCircle className="w-12 h-12" />
                  <span>{t('recorder.stopRecording')}</span>
                </>
              ) : (
                <>
                  <Mic className="w-12 h-12" />
                  <span>{user ? t('recorder.startRecording') : t('recorder.loginToContinue')}</span>
                </>
              )}
            </button>
            {isRecording && (
              <>
                <div className="absolute top-4 left-4 animate-pulse">
                  <div className="w-3 h-3 bg-red-600 rounded-full"></div>
                </div>
                <div className="absolute bottom-4 left-4 text-white font-mono">
                  {formatDuration(duration)}
                </div>
              </>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <audio src={audioUrl} controls className="w-full mb-4" />
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 sm:grid-cols-5 gap-2">
                  {voiceEffects.map(ve => (
                    <button
                      key={ve.id}
                      onClick={() => setEffect(ve.id)}
                      className={`p-2 rounded-lg flex items-center justify-center gap-2 ${
                        effect === ve.id
                          ? 'bg-indigo-600 text-white'
                          : 'bg-white border hover:bg-gray-50'
                      }`}
                    >
                      <Volume2 className="w-4 h-4" />
                      <span>{ve.name}</span>
                    </button>
                  ))}
                </div>

                {/* Category Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {t('recorder.categories')}
                  </label>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        type="button"
                        onClick={() => {
                          setSelectedCategories(prev =>
                            prev.includes(category.id)
                              ? prev.filter(id => id !== category.id)
                              : [...prev, category.id]
                          );
                        }}
                        className={`p-2 rounded-lg text-sm border ${
                          selectedCategories.includes(category.id)
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'bg-white border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {t(`categories.${category.slug}`) || category.name}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <label className="flex items-start gap-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={isPrivate}
                      onChange={(e) => setIsPrivate(e.target.checked)}
                      className="rounded text-indigo-600 mt-1"
                    />
                    <div>
                      <span className="text-gray-900 font-medium">🔒 {t('recorder.privateJoke')}</span>
                      <p className="text-sm text-gray-600 mt-1">{t('recorder.privateJokeDesc')}</p>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={handleUpload}
                disabled={loading || !title.trim()}
                className="flex-1 py-2 bg-emerald-500 text-white rounded-lg disabled:opacity-50 flex items-center justify-center gap-2"
              >
                <Upload className="w-4 h-4" />
                {loading ? t('recorder.uploading') : t('recorder.upload')}
              </button>
              
              <button
                onClick={resetRecording}
                disabled={loading}
                className="py-2 px-4 bg-gray-200 rounded-lg disabled:opacity-50 flex items-center justify-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                {t('recorder.reRecord')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Record;
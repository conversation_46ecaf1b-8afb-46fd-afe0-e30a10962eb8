import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Heart } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import JokeCard from '../components/jokes/JokeCard';

const Favorites = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        if (!user) return;

        const { data, error } = await supabase
          .from('joke_likes')
          .select(`
            jokes:joke_id (
              *,
              profiles!user_id (username, avatar_url)
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;
        
        // Extract jokes from the nested structure
        const likedJokes = data
          .map(item => item.jokes)
          .filter(Boolean)
          .map(joke => ({
            ...joke,
            is_liked: true // Since these are favorites, they're all liked
          }));

        setFavorites(likedJokes);
      } catch (err) {
        console.error('Error fetching favorites:', err);
        setError(t('errors.favorites.fetch'));
      } finally {
        setLoading(false);
      }
    };

    fetchFavorites();
  }, [user, t]);

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">{t('auth.required')}</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <Heart className="text-red-500" />
          {t('favorites.title')}
        </h1>
        <p className="text-gray-600">
          {t('favorites.subtitle')}
        </p>
      </div>

      {/* Favorites List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-purple-500 border-t-transparent"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-red-600">
          {error}
        </div>
      ) : favorites.length > 0 ? (
        <div className="space-y-6">
          {favorites.map((joke) => (
            <JokeCard
              key={joke.id}
              joke={joke}
              onLike={async (id, liked) => {
                // Update local state immediately
                setFavorites(prev => 
                  liked 
                    ? prev 
                    : prev.filter(j => j.id !== id)
                );

                // Update in database
                const { error } = await supabase
                  .from('joke_likes')
                  .delete()
                  .match({ user_id: user.id, joke_id: id });

                if (error) {
                  console.error('Error updating like:', error);
                  // Revert on error
                  setFavorites(prev => [...prev, joke]);
                }
              }}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">{t('favorites.empty')}</p>
        </div>
      )}
    </div>
  );
};

export default Favorites; 
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Trophy, Star, Award, Medal, Mic, Heart, Smile, Zap } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

const achievementTypes = {
  JOKES_CREATED: {
    icon: <Mic className="w-6 h-6" />,
    color: 'bg-blue-500'
  },
  LIKES_RECEIVED: {
    icon: <Heart className="w-6 h-6" />,
    color: 'bg-red-500'
  },
  REACTIONS_GIVEN: {
    icon: <Smile className="w-6 h-6" />,
    color: 'bg-yellow-500'
  },
  DAYS_STREAK: {
    icon: <Zap className="w-6 h-6" />,
    color: 'bg-purple-500'
  }
};

const AchievementCard = ({ achievement }) => {
  const { t } = useTranslation();
  const progress = achievement.progress / achievement.target * 100;
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6 relative overflow-hidden">
      {/* Progress bar background */}
      <div 
        className="absolute bottom-0 left-0 h-1 bg-gray-100 w-full"
      />
      
      {/* Progress bar */}
      <div 
        className="absolute bottom-0 left-0 h-1 bg-purple-500 transition-all duration-500"
        style={{ width: `${progress}%` }}
      />
      
      {/* Achievement content */}
      <div className="flex items-start gap-4">
        <div className={`${achievementTypes[achievement.type]?.color || 'bg-gray-500'} p-3 rounded-lg text-white`}>
          {achievementTypes[achievement.type]?.icon || <Award className="w-6 h-6" />}
        </div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-lg mb-1">
            {t(`achievements.${achievement.type}.title`)}
          </h3>
          <p className="text-gray-600 text-sm mb-2">
            {t(`achievements.${achievement.type}.description`)}
          </p>
          
          {/* Progress text */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">
              {achievement.progress} / {achievement.target}
            </span>
            <span className="text-purple-600 font-medium">
              {Math.round(progress)}%
            </span>
          </div>
        </div>
        
        {/* Completion badge */}
        {achievement.completed && (
          <div className="absolute top-4 right-4 text-green-500">
            <Medal className="w-6 h-6" />
          </div>
        )}
      </div>
    </div>
  );
};

const Achievements = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [achievements, setAchievements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAchievements = async () => {
      try {
        if (!user) return;

        const { data, error } = await supabase
          .from('user_achievements')
          .select('*')
          .eq('user_id', user.id);

        if (error) throw error;
        setAchievements(data || []);
      } catch (err) {
        console.error('Error fetching achievements:', err);
        setError(t('errors.achievements.fetch'));
      } finally {
        setLoading(false);
      }
    };

    fetchAchievements();
  }, [user, t]);

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">{t('auth.required')}</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
          <Trophy className="text-yellow-500" />
          {t('achievements.title')}
        </h1>
        <p className="text-gray-600">
          {t('achievements.subtitle')}
        </p>
      </div>

      {/* Achievement Stats */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <Trophy className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {achievements.filter(a => a.completed).length}
          </div>
          <div className="text-sm text-gray-500">{t('achievements.completed')}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <Star className="w-6 h-6 text-purple-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {achievements.length}
          </div>
          <div className="text-sm text-gray-500">{t('achievements.total')}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <Award className="w-6 h-6 text-blue-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {Math.round(
              (achievements.filter(a => a.completed).length / achievements.length) * 100
            )}%
          </div>
          <div className="text-sm text-gray-500">{t('achievements.completion')}</div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <Medal className="w-6 h-6 text-green-500 mx-auto mb-2" />
          <div className="text-2xl font-bold text-gray-900">
            {achievements.reduce((sum, a) => sum + (a.points || 0), 0)}
          </div>
          <div className="text-sm text-gray-500">{t('achievements.points')}</div>
        </div>
      </div>

      {/* Achievements List */}
      {loading ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-purple-500 border-t-transparent"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12 text-red-600">
          {error}
        </div>
      ) : (
        <div className="space-y-4">
          {achievements.map((achievement) => (
            <AchievementCard
              key={achievement.id}
              achievement={achievement}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default Achievements; 
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const [verifying, setVerifying] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = searchParams.get('token');
    const type = searchParams.get('type');

    const verifyEmail = async () => {
      try {
        if (type === 'signup' && token) {
          const { error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'signup'
          });

          if (error) throw error;
          
          // Wait a bit before redirecting
          setTimeout(() => {
            navigate('/auth', { 
              state: { message: 'Email verified successfully! Please log in.' }
            });
          }, 2000);
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setVerifying(false);
      }
    };

    verifyEmail();
  }, [searchParams, navigate]);

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      {verifying ? (
        <div>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من البريد الإلكتروني...</p>
        </div>
      ) : error ? (
        <div className="text-red-600">
          <p>حدث خطأ أثناء التحقق من البريد الإلكتروني:</p>
          <p className="mt-2">{error}</p>
        </div>
      ) : (
        <div className="text-green-600">
          <p>تم التحقق من البريد الإلكتروني بنجاح!</p>
          <p className="mt-2">جاري إعادة التوجيه...</p>
        </div>
      )}
    </div>
  );
};

export default VerifyEmail; 
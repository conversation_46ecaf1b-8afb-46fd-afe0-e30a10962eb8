import React, { useState, useEffect } from 'react';

const TestConnection = () => {
  const [status, setStatus] = useState('Testing...');
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    const testConnection = async () => {
      try {
        addLog('🧪 Starting connection test...');
        
        // Test 1: Environment variables
        addLog(`🔧 Supabase URL: ${process.env.REACT_APP_SUPABASE_URL?.substring(0, 30)}...`);
        addLog(`🔧 Supabase Key length: ${process.env.REACT_APP_SUPABASE_ANON_KEY?.length}`);
        
        // Test 2: Import Supabase
        addLog('📦 Importing Supabase...');
        const { supabase } = await import('../lib/supabase');
        addLog('✅ Supabase imported successfully');
        
        // Test 3: Simple fetch with timeout
        addLog('🌐 Testing simple fetch...');
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          addLog('⏰ Aborting due to timeout');
          controller.abort();
        }, 5000);
        
        try {
          const response = await fetch('https://httpbin.org/get', {
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          addLog(`✅ Simple fetch works: ${response.status}`);
        } catch (fetchErr) {
          clearTimeout(timeoutId);
          addLog(`❌ Simple fetch failed: ${fetchErr.message}`);
        }
        
        // Test 4: Supabase connection
        addLog('🔌 Testing Supabase connection...');
        
        const supabaseController = new AbortController();
        const supabaseTimeoutId = setTimeout(() => {
          addLog('⏰ Aborting Supabase query due to timeout');
          supabaseController.abort();
        }, 10000);
        
        try {
          // Try the simplest possible query
          const { data, error } = await supabase
            .from('jokes')
            .select('id')
            .limit(1);
          
          clearTimeout(supabaseTimeoutId);
          
          if (error) {
            addLog(`❌ Supabase query error: ${error.message}`);
            setStatus('Connection Error');
          } else {
            addLog(`✅ Supabase query success: ${data?.length || 0} rows`);
            setStatus('Connection Success!');
          }
        } catch (supabaseErr) {
          clearTimeout(supabaseTimeoutId);
          addLog(`❌ Supabase query exception: ${supabaseErr.message}`);
          setStatus('Connection Failed');
        }
        
      } catch (err) {
        addLog(`❌ Test failed: ${err.message}`);
        setStatus('Test Failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">🧪 Connection Test</h1>
        
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Status: {status}</h2>
        </div>
        
        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Logs:</h3>
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-sm font-mono">
                {log}
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-6">
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            🔄 Retry Test
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestConnection;

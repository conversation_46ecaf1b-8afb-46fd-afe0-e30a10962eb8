// Auth Types
export interface User {
  id: string;
  email: string;
  user_metadata?: {
    username?: string;
    avatar_url?: string;
    onboarded?: boolean;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  username: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
  notification_settings?: {
    email_notifications: boolean;
    push_notifications: boolean;
    reaction_notifications: boolean;
    comment_notifications: boolean;
    follow_notifications: boolean;
  };
  onboarded: boolean;
}

// Joke Types
export interface Joke {
  id: string;
  title: string;
  audio_url: string;
  user_id: string;
  created_at: string;
  likes_count: number;
  plays_count: number;
  comments_count: number;
  voice_effect?: string;
  is_private: boolean;
  content?: string;
  profiles?: {
    username: string;
    avatar_url?: string;
  };
  joke_categories?: {
    categories: {
      id: string;
      name: string;
      slug: string;
    };
  }[];
  is_liked?: boolean;
  reactions?: ReactionCounts;
  user_reactions?: string[];
}

export interface JokeFilters {
  category?: string;
  search?: string;
  userId?: string;
  sortBy?: 'created_at' | 'likes_count' | 'plays_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface CreateJokeData {
  title: string;
  audio_url: string;
  user_id: string;
  voice_effect?: string;
  is_private?: boolean;
  category_ids?: string[];
  content?: string;
}

// Reaction Types
export interface Reaction {
  id: string;
  user_id: string;
  joke_id: string;
  emoji: string;
  created_at: string;
}

export interface ReactionCounts {
  [emoji: string]: number;
}

// Comment Types
export interface Comment {
  id: string;
  joke_id: string;
  user_id: string;
  content: string;
  parent_id?: string;
  is_edited: boolean;
  created_at: string;
  updated_at: string;
  profiles?: {
    username: string;
    avatar_url?: string;
  };
  replies?: Comment[];
}

// Category Types
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
  joke_count?: number;
}

// Achievement Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  badge_color: string;
  category: string;
  requirement: number;
  xp_reward: number;
  created_at: string;
}

export interface UserAchievement {
  user_id: string;
  achievement_id: string;
  progress: number;
  unlocked_at?: string;
  achievement?: Achievement;
}

// Follow Types
export interface Follow {
  follower_id: string;
  following_id: string;
  created_at: string;
}

// Report Types
export interface Report {
  id: string;
  reporter_id: string;
  reported_item_id: string;
  report_type: 'joke' | 'comment' | 'user';
  reason: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  created_at: string;
  updated_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T | null;
  error: any;
  count?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  hasMore: boolean;
  nextOffset?: number;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  success: boolean;
}

// Audio Types
export interface AudioState {
  isPlaying: boolean;
  isLoading: boolean;
  isBuffering: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  error: string | null;
}

export interface VoiceEffect {
  id: string;
  name: string;
  label: string;
  description: string;
  icon: string;
}

// Language Types
export interface Language {
  code: string;
  name: string;
  dir: 'ltr' | 'rtl';
  icon: string;
}

export interface TranslationData {
  [key: string]: string | TranslationData;
}

// Search Types
export interface SearchResult {
  jokes: Joke[];
  users: Profile[];
  categories: Category[];
  total: number;
}

export interface SearchFilters {
  query: string;
  type?: 'all' | 'jokes' | 'users' | 'categories';
  category?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  sortBy?: 'relevance' | 'date' | 'popularity';
}

// Notification Types
export interface Notification {
  id: string;
  user_id: string;
  type: 'like' | 'comment' | 'follow' | 'reaction' | 'achievement';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

// Challenge Types (if implemented)
export interface Challenge {
  id: string;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  prize_description?: string;
  rules: string[];
  status: 'upcoming' | 'active' | 'ended';
  participant_count: number;
}

// Leaderboard Types
export interface LeaderboardEntry {
  user_id: string;
  username: string;
  avatar_url?: string;
  score: number;
  rank: number;
  change: number; // Position change from previous period
}

export interface Leaderboard {
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  entries: LeaderboardEntry[];
  user_rank?: number;
  last_updated: string;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: any) => string | null;
  };
}

export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Event Types
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: number;
}

// Storage Types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface StorageAdapter {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

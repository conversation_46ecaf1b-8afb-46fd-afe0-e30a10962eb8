export type JokeCategory = {
    id: number;
    name: string;
    name_ar: string;
    slug: string;
    age_restriction: number | null;
  };
  
  export type Profile = {
    id: string;
    username: string;
    avatar_url: string;
    bio: string;
    created_at: string;
    badges: string[];
    following_count: number;
    followers_count: number;
    jokes_count: number;
  };
  
  export type Joke = {
    id: string;
    title: string;
    audio_url: string;
    user_id: string;
    category_id: number;
    created_at: string;
    updated_at: string;
    likes_count: number;
    plays_count: number;
    reports_count: number;
    is_approved: boolean;
    is_private: boolean;
    transcription: string | null;
    age_restriction: number | null;
  };
  
  export type Collection = {
    id: string;
    name: string;
    user_id: string;
    created_at: string;
    is_private: boolean;
    jokes_count: number;
  };
  
  export type Badge = {
    id: string;
    name: string;
    name_ar: string;
    description: string;
    description_ar: string;
    icon_url: string;
    requirement_type: 'jokes_count' | 'likes_count' | 'followers_count';
    requirement_value: number;
  };
  
  export type Report = {
    id: string;
    joke_id: string;
    reporter_id: string;
    reason: string;
    status: 'pending' | 'resolved' | 'rejected';
    created_at: string;
    resolved_at: string | null;
  };
  
  export type Follow = {
    follower_id: string;
    following_id: string;
    created_at: string;
  };
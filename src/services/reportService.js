import { supabase } from '../lib/supabase';

export const reportService = {
  async submitReport(reportedItemId, reportType, reason) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User must be authenticated to submit a report.');

    const { data, error } = await supabase
      .from('reports')
      .insert({
        reporter_id: user.id,
        reported_item_id: reportedItemId,
        report_type: reportType,
        reason: reason,
      });

    if (error) throw error;
    return data;
  },
};
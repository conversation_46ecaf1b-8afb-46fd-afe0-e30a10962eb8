import { supabase } from '../lib/supabase';
import * as Sentry from '@sentry/react';

export interface ApiResponse<T> {
  data: T | null;
  error: any;
  count?: number;
}

export interface QueryOptions {
  select?: string;
  filters?: Record<string, any>;
  orderBy?: { column: string; ascending?: boolean };
  range?: { from: number; to: number };
  limit?: number;
  single?: boolean;
  count?: 'exact' | 'planned' | 'estimated';
}

export class ApiClient {
  private static instance: ApiClient;
  private requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  private getCacheKey(table: string, options: QueryOptions): string {
    return `${table}_${JSON.stringify(options)}`;
  }

  private isValidCache(cacheEntry: { timestamp: number; ttl: number }): boolean {
    return Date.now() - cacheEntry.timestamp < cacheEntry.ttl;
  }

  private setCache(key: string, data: any, ttl: number = this.DEFAULT_TTL): void {
    this.requestCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  private getCache(key: string): any | null {
    const cacheEntry = this.requestCache.get(key);
    if (cacheEntry && this.isValidCache(cacheEntry)) {
      return cacheEntry.data;
    }
    if (cacheEntry) {
      this.requestCache.delete(key);
    }
    return null;
  }

  public clearCache(pattern?: string): void {
    if (pattern) {
      const keys = Array.from(this.requestCache.keys());
      keys.forEach(key => {
        if (key.includes(pattern)) {
          this.requestCache.delete(key);
        }
      });
    } else {
      this.requestCache.clear();
    }
  }

  async query<T>(
    table: string,
    options: QueryOptions = {},
    useCache: boolean = true,
    cacheTtl?: number
  ): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(table, options);
    
    // Check cache first
    if (useCache) {
      const cachedData = this.getCache(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    try {
      let query = supabase.from(table);

      // Apply select
      if (options.select) {
        query = query.select(options.select, { count: options.count });
      } else {
        query = query.select('*', { count: options.count });
      }

      // Apply filters
      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              query = query.in(key, value);
            } else if (typeof value === 'object' && value.operator) {
              // Support for complex filters like { operator: 'gte', value: 10 }
              query = query[value.operator](key, value.value);
            } else {
              query = query.eq(key, value);
            }
          }
        });
      }

      // Apply ordering
      if (options.orderBy) {
        query = query.order(options.orderBy.column, { 
          ascending: options.orderBy.ascending ?? false 
        });
      }

      // Apply range or limit
      if (options.range) {
        query = query.range(options.range.from, options.range.to);
      } else if (options.limit) {
        query = query.limit(options.limit);
      }

      // Execute query
      const result = options.single ? await query.single() : await query;

      const response: ApiResponse<T> = {
        data: result.data,
        error: result.error,
        count: result.count,
      };

      // Cache successful responses
      if (useCache && !result.error) {
        this.setCache(cacheKey, response, cacheTtl);
      }

      return response;
    } catch (error) {
      console.error(`API query error for table ${table}:`, error);
      
      // Report to Sentry
      Sentry.captureException(error, {
        tags: {
          operation: 'api_query',
          table,
        },
        extra: {
          options,
        },
      });

      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown API error'),
      };
    }
  }

  async insert<T>(
    table: string,
    data: any | any[],
    options: { select?: string; upsert?: boolean } = {}
  ): Promise<ApiResponse<T>> {
    try {
      let query = supabase.from(table);

      if (options.upsert) {
        query = query.upsert(data);
      } else {
        query = query.insert(data);
      }

      if (options.select) {
        query = query.select(options.select);
      }

      const result = await query;

      // Clear related cache entries
      this.clearCache(table);

      return {
        data: result.data,
        error: result.error,
      };
    } catch (error) {
      console.error(`API insert error for table ${table}:`, error);
      
      Sentry.captureException(error, {
        tags: {
          operation: 'api_insert',
          table,
        },
        extra: {
          data,
          options,
        },
      });

      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown API error'),
      };
    }
  }

  async update<T>(
    table: string,
    data: any,
    filters: Record<string, any>,
    options: { select?: string } = {}
  ): Promise<ApiResponse<T>> {
    try {
      let query = supabase.from(table).update(data);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      if (options.select) {
        query = query.select(options.select);
      }

      const result = await query;

      // Clear related cache entries
      this.clearCache(table);

      return {
        data: result.data,
        error: result.error,
      };
    } catch (error) {
      console.error(`API update error for table ${table}:`, error);
      
      Sentry.captureException(error, {
        tags: {
          operation: 'api_update',
          table,
        },
        extra: {
          data,
          filters,
          options,
        },
      });

      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown API error'),
      };
    }
  }

  async delete<T>(
    table: string,
    filters: Record<string, any>,
    options: { select?: string } = {}
  ): Promise<ApiResponse<T>> {
    try {
      let query = supabase.from(table).delete();

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        query = query.eq(key, value);
      });

      if (options.select) {
        query = query.select(options.select);
      }

      const result = await query;

      // Clear related cache entries
      this.clearCache(table);

      return {
        data: result.data,
        error: result.error,
      };
    } catch (error) {
      console.error(`API delete error for table ${table}:`, error);
      
      Sentry.captureException(error, {
        tags: {
          operation: 'api_delete',
          table,
        },
        extra: {
          filters,
          options,
        },
      });

      return {
        data: null,
        error: error instanceof Error ? error : new Error('Unknown API error'),
      };
    }
  }

  // Real-time subscription helper
  subscribeToChanges(
    table: string,
    callback: (payload: any) => void,
    filters?: Record<string, any>
  ) {
    let channel = supabase.channel(`${table}_changes`);

    const changeConfig: any = {
      event: '*',
      schema: 'public',
      table,
    };

    if (filters) {
      const filterString = Object.entries(filters)
        .map(([key, value]) => `${key}=eq.${value}`)
        .join(',');
      changeConfig.filter = filterString;
    }

    channel = channel.on('postgres_changes', changeConfig, (payload: any) => {
      // Clear cache when data changes
      this.clearCache(table);
      callback(payload);
    });

    const subscription = channel.subscribe();

    return {
      unsubscribe: () => subscription.unsubscribe(),
    };
  }
}

// Export singleton instance
export const apiClient = ApiClient.getInstance();

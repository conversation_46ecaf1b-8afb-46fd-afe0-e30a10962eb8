import { supabase } from '../lib/supabase';

export const searchService = {
    async searchJokes(query, { category = null, limit = 10, offset = 0 } = {}) {
        let supabaseQuery = supabase
            .from('jokes')
            .select('*')
            .eq('is_private', false)
            .textSearch('search_vector', query);

        // Category filtering temporarily disabled due to schema issues
        // Will be re-enabled once joke_categories relationships are stable

        const { data, error, count } = await supabaseQuery
            .range(offset, offset + limit - 1)
            .order('created_at', { ascending: false });

        if (error) throw error;
        return { jokes: data, count };
    },

    async getJokesByCategory(categorySlug, { limit = 10, offset = 0 } = {}) {
        // Simplified query without category joins for now
        const { data, error, count } = await supabase
            .from('jokes')
            .select('*', { count: 'exact' })
            .eq('is_private', false)
            .range(offset, offset + limit - 1)
            .order('created_at', { ascending: false });

        if (error) throw error;
        return { jokes: data, count };
    }
}; 
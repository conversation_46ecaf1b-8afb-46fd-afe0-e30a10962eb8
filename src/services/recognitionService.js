import { supabase } from '../lib/supabase';

export const recognitionService = {
  async getCurrentSpotlights() {
    // Simplified query without complex joins for now
    const { data, error } = await supabase
      .from('community_spotlights')
      .select('*')
      .gte('end_date', new Date().toISOString())
      .order('start_date', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getSpotlightsByCategory(category) {
    const { data, error } = await supabase
      .from('community_spotlights')
      .select(`
        id,
        category,
        start_date,
        end_date,
        metrics,
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          audio_url,
          user_id,
          created_at,
          profiles!user_id (
            username,
            avatar_url
          )
        )
      `)
      .eq('category', category)
      .gte('end_date', new Date().toISOString())
      .order('start_date', { ascending: false });

    if (error) throw error;
    return data;
  }
}; 
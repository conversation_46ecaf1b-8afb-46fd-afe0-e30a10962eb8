import { supabase } from '../lib/supabase';

export const categoryService = {
    async testConnection() {
        try {
            const { data, error } = await supabase
                .from('categories')
                .select('*');
            
            if (error) throw error;
            console.log('Database connection successful!');
            console.log('Categories found:', data.length);
            return { success: true, data };
        } catch (error) {
            console.error('Database connection error:', error);
            return { success: false, error };
        }
    },

    async getAllCategories() {
        const { data, error } = await supabase
            .from('categories')
            .select('*')
            .order('name');
        
        if (error) throw error;
        return data;
    },

    async getJokeCategories(jokeId) {
        // Simplified query without joins for now
        const { data, error } = await supabase
            .from('joke_categories')
            .select('category_id')
            .eq('joke_id', jokeId);

        if (error) throw error;
        return data || [];
    },

    async addJokeToCategory(jokeId, categoryId) {
        const { error } = await supabase
            .from('joke_categories')
            .upsert({ joke_id: jokeId, category_id: categoryId });
        
        if (error) throw error;
    },

    async removeJokeFromCategory(jokeId, categoryId) {
        const { error } = await supabase
            .from('joke_categories')
            .delete()
            .match({ joke_id: jokeId, category_id: categoryId });
        
        if (error) throw error;
    }
}; 
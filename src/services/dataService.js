import { supabase } from '../lib/supabase';

class DataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // Connection health check with timeout
  async healthCheck() {
    try {
      console.log('🏥 DataService: Health check starting...');
      const start = Date.now();

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), 5000);
      });

      // Create the query promise
      const queryPromise = supabase
        .from('jokes')
        .select('id')
        .limit(1);

      console.log('🏥 DataService: Executing health check query...');

      // Race between query and timeout
      const { data, error } = await Promise.race([queryPromise, timeoutPromise]);

      const duration = Date.now() - start;
      console.log(`🏥 DataService: Health check completed in ${duration}ms`);

      if (error) {
        console.error('🏥 DataService: Health check failed:', error);
        return { healthy: false, error: error.message, duration };
      }

      console.log('🏥 DataService: Health check successful, got data:', !!data);
      return { healthy: true, duration };
    } catch (err) {
      const duration = Date.now() - Date.now();
      console.error('🏥 DataService: Health check exception:', err);
      return { healthy: false, error: err.message, duration };
    }
  }

  // Get jokes with proper error handling
  async getJokes(options = {}) {
    const {
      limit = 10,
      offset = 0,
      userId = null,
      isPrivate = false,
      orderBy = 'created_at',
      ascending = false
    } = options;

    try {
      console.log('📊 DataService: Fetching jokes...', options);
      const start = Date.now();

      let query = supabase
        .from('jokes')
        .select(`
          id,
          title,
          audio_url,
          user_id,
          created_at,
          likes_count,
          plays_count,
          comments_count,
          voice_effect,
          is_private,
          profiles!user_id (
            username,
            avatar_url
          )
        `)
        .eq('is_private', isPrivate)
        .order(orderBy, { ascending })
        .range(offset, offset + limit - 1);

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error, count } = await query;
      const duration = Date.now() - start;

      console.log(`📊 DataService: Query completed in ${duration}ms`);
      console.log(`📊 DataService: Retrieved ${data?.length || 0} jokes`);

      if (error) {
        console.error('📊 DataService: Query error:', error);
        throw new Error(`Database query failed: ${error.message}`);
      }

      return {
        data: data || [],
        count,
        duration,
        success: true
      };

    } catch (err) {
      console.error('📊 DataService: Exception:', err);
      return {
        data: [],
        count: 0,
        duration: 0,
        success: false,
        error: err.message
      };
    }
  }

  // Get user profile
  async getUserProfile(userId) {
    try {
      console.log('👤 DataService: Fetching profile for:', userId);
      const start = Date.now();

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      const duration = Date.now() - start;
      console.log(`👤 DataService: Profile query completed in ${duration}ms`);

      if (error) {
        console.error('👤 DataService: Profile error:', error);
        throw new Error(`Profile query failed: ${error.message}`);
      }

      return {
        data,
        duration,
        success: true
      };

    } catch (err) {
      console.error('👤 DataService: Profile exception:', err);
      return {
        data: null,
        duration: 0,
        success: false,
        error: err.message
      };
    }
  }

  // Test connection with retry
  async testConnection(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`🔄 DataService: Connection test attempt ${attempt}/${maxRetries}`);
      
      const result = await this.healthCheck();
      
      if (result.healthy) {
        console.log('✅ DataService: Connection test passed');
        return result;
      }
      
      if (attempt < maxRetries) {
        const delay = attempt * 1000; // Exponential backoff
        console.log(`⏳ DataService: Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    console.error('❌ DataService: All connection attempts failed');
    return { healthy: false, error: 'Max retries exceeded' };
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    console.log('🧹 DataService: Cache cleared');
  }
}

// Export singleton instance
export const dataService = new DataService();

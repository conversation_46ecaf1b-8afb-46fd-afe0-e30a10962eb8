import { supabase } from '../lib/supabase';

export const reactionService = {
  /**
   * Add a reaction to a joke
   * @param {string} jokeId - The ID of the joke
   * @param {string} emoji - The emoji reaction
   * @returns {Promise<Object>} The created reaction
   */
  async addReaction(jokeId, emoji) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User must be authenticated');

    const { data, error } = await supabase
      .from('emoji_reactions')
      .insert([
        {
          joke_id: jokeId,
          emoji: emoji,
          user_id: user.id
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
    return data;
  },

  /**
   * Remove a reaction from a joke
   * @param {string} jokeId - The ID of the joke
   * @param {string} emoji - The emoji reaction
   * @returns {Promise<void>}
   */
  async removeReaction(jokeId, emoji) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User must be authenticated');

    const { error } = await supabase
      .from('emoji_reactions')
      .delete()
      .match({
        joke_id: jokeId,
        emoji: emoji,
        user_id: user.id
      });

    if (error) {
      console.error('Error removing reaction:', error);
      throw error;
    }
  },

  /**
   * Get reaction counts for a joke
   * @param {string} jokeId - The ID of the joke
   * @returns {Promise<Object>} Object with emoji counts
   */
  async getReactionCounts(jokeId) {
    try {
      const { data, error } = await supabase
        .rpc('get_joke_reaction_counts', { joke_uuid: jokeId });

      if (error) {
        console.error('Error getting reaction counts:', error);
        return {};
      }

      return data.reduce((acc, { emoji, count }) => {
        acc[emoji] = parseInt(count, 10);
        return acc;
      }, {});
    } catch (error) {
      console.error('Error getting reaction counts:', error);
      return {};
    }
  },

  /**
   * Get user's reactions for a joke
   * @param {string} jokeId - The ID of the joke
   * @returns {Promise<string[]>} Array of emojis the user has reacted with
   */
  async getUserReactions(jokeId) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .rpc('get_user_joke_reactions', { 
          joke_uuid: jokeId,
          user_uuid: user.id
        });

      if (error) {
        console.error('Error getting user reactions:', error);
        return [];
      }

      return data.map(row => row.emoji);
    } catch (error) {
      console.error('Error getting user reactions:', error);
      return [];
    }
  },

  /**
   * Toggle a reaction on a joke
   * @param {string} jokeId - The ID of the joke
   * @param {string} emoji - The emoji reaction
   * @returns {Promise<Object>} Updated reaction state
   */
  async toggleReaction(jokeId, emoji) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User must be authenticated');

      const { data, error } = await supabase
        .rpc('toggle_emoji_reaction', {
          p_emoji: emoji,
          p_joke_id: jokeId
        });

      if (error) throw error;

      // Check if we got a successful response
      const result = Array.isArray(data) ? data[0] : data;
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to toggle reaction');
      }

      return {
        added: result.is_added,
        emoji,
        error: null
      };
    } catch (error) {
      console.error('Error toggling reaction:', error);
      throw error;
    }
  }
}; 
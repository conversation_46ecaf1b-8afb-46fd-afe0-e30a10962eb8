import { supabase } from '../lib/supabase';
import { contentFilterService } from './contentFilterService';
import * as Sentry from '@sentry/react';

export interface Comment {
  id: string;
  joke_id: string;
  user_id: string;
  content: string;
  parent_id?: string;
  is_edited: boolean;
  created_at: string;
  updated_at?: string;
  // Joined data
  user?: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  replies?: Comment[];
  reply_count?: number;
}

export interface CreateCommentData {
  joke_id: string;
  content: string;
  parent_id?: string;
}

export interface UpdateCommentData {
  content: string;
}

export interface CommentsResponse {
  comments: Comment[];
  total_count: number;
  has_more: boolean;
}

class CommentsService {
  /**
   * Get comments for a joke with pagination and threading
   */
  async getComments(
    jokeId: string,
    options: {
      limit?: number;
      offset?: number;
      includeReplies?: boolean;
    } = {}
  ): Promise<{ data: CommentsResponse | null; error: any }> {
    try {
      const { limit = 20, offset = 0, includeReplies = true } = options;

      // Get top-level comments first
      let query = supabase
        .from('comments')
        .select(`
          *,
          user:user_id(id, username, avatar_url)
        `)
        .eq('joke_id', jokeId)
        .is('parent_id', null)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data: topLevelComments, error: commentsError } = await query;

      if (commentsError) {
        throw commentsError;
      }

      let comments = topLevelComments || [];

      // Get replies for each top-level comment if requested
      if (includeReplies && comments.length > 0) {
        const commentIds = comments.map((c: any) => c.id);
        
        const { data: replies, error: repliesError } = await supabase
          .from('comments')
          .select(`
            *,
            user:user_id(id, username, avatar_url)
          `)
          .in('parent_id', commentIds)
          .order('created_at', { ascending: true });

        if (repliesError) {
          throw repliesError;
        }

        // Group replies by parent comment
        const repliesByParent = (replies || []).reduce((acc: any, reply: any) => {
          if (!acc[reply.parent_id]) {
            acc[reply.parent_id] = [];
          }
          acc[reply.parent_id].push(reply);
          return acc;
        }, {} as Record<string, Comment[]>);

        // Attach replies to their parent comments
        comments = comments.map((comment: any) => ({
          ...comment,
          replies: repliesByParent[comment.id] || [],
          reply_count: (repliesByParent[comment.id] || []).length
        }));
      }

      // Get total count for pagination
      const { count, error: countError } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('joke_id', jokeId)
        .is('parent_id', null);

      if (countError) {
        throw countError;
      }

      const totalCount = count || 0;
      const hasMore = offset + limit < totalCount;

      return {
        data: {
          comments,
          total_count: totalCount,
          has_more: hasMore
        },
        error: null
      };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_comments' },
        extra: { jokeId, options }
      });
      return { data: null, error };
    }
  }

  /**
   * Create a new comment
   */
  async createComment(commentData: CreateCommentData): Promise<{ data: Comment | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      // Content filtering
      const contentAnalysis = contentFilterService.analyzeContent(commentData.content);
      
      if (!contentAnalysis.isClean) {
        if (contentAnalysis.issues.hateSpeech) {
          return { 
            data: null, 
            error: { message: 'Comment contains inappropriate content and cannot be posted' }
          };
        }
        
        // Clean the content if it has minor issues
        commentData.content = contentFilterService.cleanText(commentData.content);
      }

      const { data, error } = await supabase
        .from('comments')
        .insert([{
          ...commentData,
          user_id: user.id
        }])
        .select(`
          *,
          user:user_id(id, username, avatar_url)
        `)
        .single();

      if (error) {
        throw error;
      }

      // Auto-flag if needed
      if (contentFilterService.shouldAutoFlag(commentData.content)) {
        // Create a report for this comment
        await supabase
          .from('reports')
          .insert([{
            reporter_id: user.id, // System report
            reported_item_id: data.id,
            reported_user_id: user.id,
            report_type: 'comment',
            reason: 'inappropriate_content',
            description: 'Auto-flagged by content filter',
            status: 'pending'
          }]);
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'create_comment' },
        extra: { commentData }
      });
      return { data: null, error };
    }
  }

  /**
   * Update a comment
   */
  async updateComment(commentId: string, updates: UpdateCommentData): Promise<{ data: Comment | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      // Content filtering
      const contentAnalysis = contentFilterService.analyzeContent(updates.content);
      
      if (!contentAnalysis.isClean) {
        if (contentAnalysis.issues.hateSpeech) {
          return { 
            data: null, 
            error: { message: 'Comment contains inappropriate content and cannot be updated' }
          };
        }
        
        // Clean the content if it has minor issues
        updates.content = contentFilterService.cleanText(updates.content);
      }

      const { data, error } = await supabase
        .from('comments')
        .update({
          ...updates,
          is_edited: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', commentId)
        .eq('user_id', user.id) // Ensure user can only update their own comments
        .select(`
          *,
          user:user_id(id, username, avatar_url)
        `)
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'update_comment' },
        extra: { commentId, updates }
      });
      return { data: null, error };
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(commentId: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', user.id); // Ensure user can only delete their own comments

      if (error) {
        throw error;
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'delete_comment' },
        extra: { commentId }
      });
      return { data: null, error };
    }
  }

  /**
   * Get comment count for a joke
   */
  async getCommentCount(jokeId: string): Promise<{ data: number; error: any }> {
    try {
      const { count, error } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('joke_id', jokeId);

      if (error) {
        throw error;
      }

      return { data: count || 0, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_comment_count' },
        extra: { jokeId }
      });
      return { data: 0, error };
    }
  }

  /**
   * Subscribe to real-time comment updates
   */
  subscribeToComments(jokeId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`comments:${jokeId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'comments',
          filter: `joke_id=eq.${jokeId}`
        },
        callback
      )
      .subscribe();
  }
}

export const commentsService = new CommentsService();

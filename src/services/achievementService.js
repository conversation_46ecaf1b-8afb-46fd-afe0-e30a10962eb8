import { supabase } from '../lib/supabase';

export const achievementService = {
  async getUserAchievements(userId) {
    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        progress,
        unlocked_at,
        achievement:achievement_id (
          id,
          title,
          description,
          icon,
          badge_color,
          category,
          requirement,
          xp_reward
        )
      `)
      .eq('user_id', userId);
    
    if (error) throw error;
    return data;
  },

  async updateAchievementProgress(userId, achievementId, progress) {
    const { data, error } = await supabase
      .from('user_achievements')
      .upsert({
        user_id: userId,
        achievement_id: achievementId,
        progress,
        unlocked_at: progress >= 100 ? new Date().toISOString() : null
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
}; 
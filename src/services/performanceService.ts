import * as Sentry from '@sentry/react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

interface CacheConfig {
  maxSize: number;
  ttl: number; // Time to live in milliseconds
}

class PerformanceService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly DEFAULT_CACHE_CONFIG: CacheConfig = {
    maxSize: 100,
    ttl: 5 * 60 * 1000 // 5 minutes
  };

  /**
   * Cache management
   */
  setCache(key: string, data: any, config?: Partial<CacheConfig>): void {
    const { maxSize, ttl } = { ...this.DEFAULT_CACHE_CONFIG, ...config };

    // Clean expired entries
    this.cleanExpiredCache();

    // Remove oldest entries if cache is full
    if (this.cache.size >= maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  getCache(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  clearCache(pattern?: string): void {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of Array.from(this.cache.keys())) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Performance monitoring
   */
  measurePerformance<T>(
    operation: string,
    fn: () => Promise<T> | T
  ): Promise<{ result: T; metrics: PerformanceMetrics }> {
    return new Promise(async (resolve, reject) => {
      const startTime = performance.now();
      const startMemory = this.getMemoryUsage();

      try {
        const result = await fn();
        const endTime = performance.now();
        const endMemory = this.getMemoryUsage();

        const metrics: PerformanceMetrics = {
          loadTime: endTime - startTime,
          renderTime: endTime - startTime, // Simplified for now
          interactionTime: 0, // Would be measured separately
          memoryUsage: endMemory - startMemory
        };

        // Log slow operations
        if (metrics.loadTime > 1000) {
          console.warn(`Slow operation detected: ${operation} took ${metrics.loadTime}ms`);
          
          Sentry.addBreadcrumb({
            message: `Slow operation: ${operation}`,
            level: 'warning',
            data: metrics
          });
        }

        resolve({ result, metrics });
      } catch (error) {
        reject(error);
      }
    });
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Image optimization
   */
  optimizeImageUrl(url: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  } = {}): string {
    if (!url) return url;

    // If it's already optimized or a data URL, return as is
    if (url.includes('?') || url.startsWith('data:')) {
      return url;
    }

    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);

    const queryString = params.toString();
    return queryString ? `${url}?${queryString}` : url;
  }

  /**
   * Lazy loading utilities
   */
  createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver {
    const defaultOptions: IntersectionObserverInit = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    };

    return new IntersectionObserver(callback, defaultOptions);
  }

  /**
   * Debounce utility for performance
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  /**
   * Throttle utility for performance
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Preload resources
   */
  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  }

  preloadAudio(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.oncanplaythrough = () => resolve();
      audio.onerror = reject;
      audio.src = src;
      audio.load();
    });
  }

  /**
   * Bundle size optimization
   */
  async loadComponentDynamically<T>(
    importFn: () => Promise<{ default: T }>
  ): Promise<T> {
    try {
      const module = await importFn();
      return module.default;
    } catch (error) {
      console.error('Failed to load component dynamically:', error);
      throw error;
    }
  }

  /**
   * Network optimization
   */
  isSlowConnection(): boolean {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return connection.effectiveType === 'slow-2g' || 
             connection.effectiveType === '2g' ||
             connection.saveData;
    }
    return false;
  }

  getNetworkInfo(): {
    effectiveType?: string;
    downlink?: number;
    saveData?: boolean;
  } {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        saveData: connection.saveData
      };
    }
    return {};
  }

  /**
   * Memory management
   */
  cleanupResources(): void {
    // Clear caches
    this.clearCache();

    // Force garbage collection if available (development only)
    if (process.env.NODE_ENV === 'development' && 'gc' in window) {
      (window as any).gc();
    }
  }

  /**
   * Performance monitoring for React components
   */
  measureComponentRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    
    renderFn();
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;

    if (renderTime > 16) { // More than one frame (60fps)
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`);
    }

    // Log to analytics
    if (renderTime > 100) {
      Sentry.addBreadcrumb({
        message: `Slow component render: ${componentName}`,
        level: 'warning',
        data: { renderTime }
      });
    }
  }

  /**
   * Virtual scrolling helper
   */
  calculateVisibleItems(
    containerHeight: number,
    itemHeight: number,
    scrollTop: number,
    totalItems: number,
    overscan: number = 5
  ): { startIndex: number; endIndex: number; visibleItems: number } {
    const visibleItems = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2);

    return { startIndex, endIndex, visibleItems };
  }

  /**
   * Service worker utilities
   */
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully');
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
      }
    }
    return null;
  }

  /**
   * Offline detection
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  onNetworkChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Return cleanup function
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }
}

export const performanceService = new PerformanceService();

import * as Sentry from '@sentry/react';

// Basic profanity word list (expandable)
const PROFANITY_WORDS = [
  // English profanity (basic list - can be expanded)
  'damn', 'hell', 'shit', 'fuck', 'bitch', 'ass', 'bastard', 'crap',
  // Add more words as needed
  
  // Arabic profanity (basic list)
  'كلب', 'حمار', 'غبي', 'أحمق',
  // Add more Arabic words as needed
];

// Spam patterns
const SPAM_PATTERNS = [
  /(.)\1{4,}/g, // Repeated characters (aaaaa)
  /^[A-Z\s!]{10,}$/g, // All caps with exclamation
  /(buy now|click here|free money|make money|earn \$)/gi,
  /(http[s]?:\/\/[^\s]+)/gi, // URLs
  /(\d{10,})/g, // Long numbers (phone numbers)
];

// Hate speech keywords
const HATE_SPEECH_KEYWORDS = [
  'terrorist', 'nazi', 'kill yourself', 'die', 'suicide',
  // Add more hate speech keywords
];

export interface ContentAnalysis {
  isClean: boolean;
  issues: {
    profanity: boolean;
    spam: boolean;
    hateSpeech: boolean;
    excessiveCaps: boolean;
    suspiciousLinks: boolean;
  };
  confidence: number; // 0-1 scale
  flaggedWords: string[];
  suggestions?: string[];
}

export interface FilterOptions {
  strictMode?: boolean;
  allowMildProfanity?: boolean;
  checkSpam?: boolean;
  checkHateSpeech?: boolean;
}

class ContentFilterService {
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u0600-\u06FF]/g, ' ') // Keep alphanumeric and Arabic
      .replace(/\s+/g, ' ')
      .trim();
  }

  private checkProfanity(text: string, options: FilterOptions): { found: boolean; words: string[] } {
    const normalizedText = this.normalizeText(text);
    const foundWords: string[] = [];

    for (const word of PROFANITY_WORDS) {
      const normalizedWord = this.normalizeText(word);
      if (normalizedText.includes(normalizedWord)) {
        foundWords.push(word);
      }
    }

    // Check for leetspeak variations (f*ck, sh!t, etc.)
    const leetVariations = normalizedText
      .replace(/[0@]/g, 'o')
      .replace(/[1!]/g, 'i')
      .replace(/[3]/g, 'e')
      .replace(/[4]/g, 'a')
      .replace(/[5]/g, 's')
      .replace(/[7]/g, 't')
      .replace(/[\*]/g, 'u');

    for (const word of PROFANITY_WORDS) {
      const normalizedWord = this.normalizeText(word);
      if (leetVariations.includes(normalizedWord) && !foundWords.includes(word)) {
        foundWords.push(word);
      }
    }

    return {
      found: foundWords.length > 0,
      words: foundWords
    };
  }

  private checkSpam(text: string): boolean {
    for (const pattern of SPAM_PATTERNS) {
      if (pattern.test(text)) {
        return true;
      }
    }

    // Check for excessive repetition
    const words = text.split(/\s+/);
    const wordCount = new Map<string, number>();
    
    for (const word of words) {
      const normalized = this.normalizeText(word);
      if (normalized.length > 2) {
        wordCount.set(normalized, (wordCount.get(normalized) || 0) + 1);
      }
    }

    // Flag if any word appears more than 3 times
    for (const count of Array.from(wordCount.values())) {
      if (count > 3) {
        return true;
      }
    }

    return false;
  }

  private checkHateSpeech(text: string): { found: boolean; words: string[] } {
    const normalizedText = this.normalizeText(text);
    const foundWords: string[] = [];

    for (const keyword of HATE_SPEECH_KEYWORDS) {
      const normalizedKeyword = this.normalizeText(keyword);
      if (normalizedText.includes(normalizedKeyword)) {
        foundWords.push(keyword);
      }
    }

    return {
      found: foundWords.length > 0,
      words: foundWords
    };
  }

  private checkExcessiveCaps(text: string): boolean {
    const capsCount = (text.match(/[A-Z]/g) || []).length;
    const totalLetters = (text.match(/[A-Za-z]/g) || []).length;
    
    if (totalLetters === 0) return false;
    
    const capsRatio = capsCount / totalLetters;
    return capsRatio > 0.6 && text.length > 10; // More than 60% caps in text longer than 10 chars
  }

  private checkSuspiciousLinks(text: string): boolean {
    const urlPattern = /(http[s]?:\/\/[^\s]+|www\.[^\s]+|\b[a-zA-Z0-9-]+\.[a-zA-Z]{2,}\b)/gi;
    return urlPattern.test(text);
  }

  /**
   * Analyze content for various issues
   */
  analyzeContent(text: string, options: FilterOptions = {}): ContentAnalysis {
    try {
      const {
        strictMode = false,
        allowMildProfanity = false,
        checkSpam = true,
        checkHateSpeech = true
      } = options;

      const profanityCheck = this.checkProfanity(text, options);
      const spamCheck = checkSpam ? this.checkSpam(text) : false;
      const hateSpeechCheck = checkHateSpeech ? this.checkHateSpeech(text) : { found: false, words: [] };
      const excessiveCapsCheck = this.checkExcessiveCaps(text);
      const suspiciousLinksCheck = this.checkSuspiciousLinks(text);

      const issues = {
        profanity: profanityCheck.found,
        spam: spamCheck,
        hateSpeech: hateSpeechCheck.found,
        excessiveCaps: excessiveCapsCheck,
        suspiciousLinks: suspiciousLinksCheck
      };

      const flaggedWords = [
        ...profanityCheck.words,
        ...hateSpeechCheck.words
      ];

      // Calculate confidence based on number of issues
      const issueCount = Object.values(issues).filter(Boolean).length;
      const confidence = Math.min(issueCount * 0.3, 1); // Max confidence of 1

      const isClean = !Object.values(issues).some(Boolean);

      const suggestions: string[] = [];
      if (issues.profanity) suggestions.push('Remove inappropriate language');
      if (issues.spam) suggestions.push('Reduce repetitive content');
      if (issues.hateSpeech) suggestions.push('Remove offensive content');
      if (issues.excessiveCaps) suggestions.push('Use normal capitalization');
      if (issues.suspiciousLinks) suggestions.push('Remove suspicious links');

      return {
        isClean,
        issues,
        confidence,
        flaggedWords,
        suggestions: suggestions.length > 0 ? suggestions : undefined
      };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'content_analysis' },
        extra: { text: text.substring(0, 100) } // Only log first 100 chars for privacy
      });

      // Return safe default on error
      return {
        isClean: true,
        issues: {
          profanity: false,
          spam: false,
          hateSpeech: false,
          excessiveCaps: false,
          suspiciousLinks: false
        },
        confidence: 0,
        flaggedWords: []
      };
    }
  }

  /**
   * Clean text by replacing flagged words with asterisks
   */
  cleanText(text: string, options: FilterOptions = {}): string {
    try {
      const analysis = this.analyzeContent(text, options);
      
      if (analysis.isClean) {
        return text;
      }

      let cleanedText = text;

      // Replace profanity with asterisks
      for (const word of analysis.flaggedWords) {
        const regex = new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        cleanedText = cleanedText.replace(regex, '*'.repeat(word.length));
      }

      return cleanedText;

    } catch (error) {
      Sentry.captureException(error);
      return text; // Return original text on error
    }
  }

  /**
   * Check if content should be auto-flagged for review
   */
  shouldAutoFlag(text: string, options: FilterOptions = {}): boolean {
    const analysis = this.analyzeContent(text, options);
    
    // Auto-flag if:
    // - Contains hate speech
    // - Contains profanity and confidence > 0.7
    // - Multiple issues detected
    return (
      analysis.issues.hateSpeech ||
      (analysis.issues.profanity && analysis.confidence > 0.7) ||
      Object.values(analysis.issues).filter(Boolean).length >= 2
    );
  }

  /**
   * Get content rating (safe, warning, blocked)
   */
  getContentRating(text: string, options: FilterOptions = {}): 'safe' | 'warning' | 'blocked' {
    const analysis = this.analyzeContent(text, options);

    if (analysis.issues.hateSpeech) {
      return 'blocked';
    }

    if (analysis.issues.profanity || analysis.issues.spam) {
      return 'warning';
    }

    return 'safe';
  }
}

export const contentFilterService = new ContentFilterService();

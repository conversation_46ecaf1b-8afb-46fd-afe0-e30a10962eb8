import { supabase } from '../lib/supabase';
import * as Sentry from '@sentry/react';

export interface LeaderboardEntry {
  user_id: string;
  username: string;
  avatar_url?: string;
  bio?: string;
  rank: number;
  score: number;
  total_jokes: number;
  total_likes: number;
  total_reactions: number;
  total_comments: number;
  total_followers: number;
  streak_days: number;
  badges: string[];
  is_current_user?: boolean;
}

export interface SeasonalRanking {
  id: string;
  user_id: string;
  season_id: string;
  rank: number;
  points: number;
  jokes_count: number;
  engagement_score: number;
  created_at: string;
  updated_at: string;
}

export interface Season {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  description?: string;
  prize_pool?: string;
}

export interface LeaderboardFilters {
  timeframe?: 'daily' | 'weekly' | 'monthly' | 'seasonal' | 'all-time';
  category?: 'overall' | 'engagement' | 'consistency' | 'trending';
  limit?: number;
  offset?: number;
}

class LeaderboardService {
  private readonly SCORING_WEIGHTS = {
    joke_posted: 10,
    like_received: 2,
    reaction_received: 3,
    comment_received: 5,
    follower_gained: 8,
    daily_streak: 5,
    trending_bonus: 20
  };

  /**
   * Get leaderboard rankings
   */
  async getLeaderboard(filters: LeaderboardFilters = {}): Promise<{ data: LeaderboardEntry[] | null; error: any }> {
    try {
      const {
        timeframe = 'weekly',
        category = 'overall',
        limit = 50,
        offset = 0
      } = filters;

      let rankings: LeaderboardEntry[] = [];

      switch (category) {
        case 'engagement':
          rankings = await this.getEngagementLeaderboard(timeframe, limit, offset);
          break;
        case 'consistency':
          rankings = await this.getConsistencyLeaderboard(timeframe, limit, offset);
          break;
        case 'trending':
          rankings = await this.getTrendingLeaderboard(timeframe, limit, offset);
          break;
        case 'overall':
        default:
          rankings = await this.getOverallLeaderboard(timeframe, limit, offset);
          break;
      }

      // Mark current user
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        rankings = rankings.map(entry => ({
          ...entry,
          is_current_user: entry.user_id === user.id
        }));
      }

      return { data: rankings, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_leaderboard' },
        extra: { filters }
      });
      return { data: null, error };
    }
  }

  /**
   * Get overall leaderboard
   */
  private async getOverallLeaderboard(
    timeframe: string,
    limit: number,
    offset: number
  ): Promise<LeaderboardEntry[]> {
    // Use the simple RPC function we created
    const { data, error } = await supabase
      .rpc('get_overall_leaderboard', {
        limit_count: limit
      });

    if (error) {
      throw error;
    }

    return (data || []).map((entry: any, index: number) => ({
      user_id: entry.user_id,
      username: entry.username,
      avatar_url: entry.avatar_url,
      rank: index + 1,
      score: entry.score || 0,
      total_jokes: entry.total_jokes || 0,
      total_likes: entry.total_likes || 0,
      total_reactions: 0,
      total_comments: 0,
      total_followers: 0,
      streak_days: 0,
      badges: [],
      total_plays: entry.total_plays || 0
    }));
  }

  /**
   * Get engagement leaderboard
   */
  private async getEngagementLeaderboard(
    timeframe: string,
    limit: number,
    offset: number
  ): Promise<LeaderboardEntry[]> {
    // Fallback to overall leaderboard for now
    return this.getOverallLeaderboard(timeframe, limit, offset);
  }

  /**
   * Get consistency leaderboard (based on posting frequency)
   */
  private async getConsistencyLeaderboard(
    timeframe: string,
    limit: number,
    offset: number
  ): Promise<LeaderboardEntry[]> {
    // Fallback to overall leaderboard for now
    return this.getOverallLeaderboard(timeframe, limit, offset);
  }

  /**
   * Get trending leaderboard (recent high performers)
   */
  private async getTrendingLeaderboard(
    timeframe: string,
    limit: number,
    offset: number
  ): Promise<LeaderboardEntry[]> {
    // Fallback to overall leaderboard for now
    return this.getOverallLeaderboard(timeframe, limit, offset);
  }

  /**
   * Get user's rank and stats
   */
  async getUserRank(userId: string, category = 'overall'): Promise<{ data: LeaderboardEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .rpc('get_user_rank', {
          target_user_id: userId,
          category_type: category
        });

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        return { data: null, error: null };
      }

      const userStats = data[0];
      const entry: LeaderboardEntry = {
        ...userStats,
        score: this.calculateOverallScore(userStats),
        badges: this.calculateBadges(userStats),
        is_current_user: true
      };

      return { data: entry, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_user_rank' },
        extra: { userId, category }
      });
      return { data: null, error };
    }
  }

  /**
   * Get seasonal rankings
   */
  async getSeasonalRankings(seasonId?: string): Promise<{ data: SeasonalRanking[] | null; error: any }> {
    try {
      let query = supabase
        .from('seasonal_rankings')
        .select(`
          *,
          profiles!user_id(username, avatar_url),
          seasons:season_id(name, start_date, end_date)
        `)
        .order('rank', { ascending: true });

      if (seasonId) {
        query = query.eq('season_id', seasonId);
      }

      const { data, error } = await query.limit(100);

      if (error) {
        throw error;
      }

      return { data: data || [], error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_seasonal_rankings' }
      });
      return { data: null, error };
    }
  }

  /**
   * Get active seasons
   */
  async getActiveSeasons(): Promise<{ data: Season[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('seasons')
        .select('*')
        .eq('is_active', true)
        .order('start_date', { ascending: false });

      if (error) {
        throw error;
      }

      return { data: data || [], error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_active_seasons' }
      });
      return { data: null, error };
    }
  }

  /**
   * Calculate overall score
   */
  private calculateOverallScore(entry: any): number {
    return (
      (entry.total_jokes || 0) * this.SCORING_WEIGHTS.joke_posted +
      (entry.total_likes || 0) * this.SCORING_WEIGHTS.like_received +
      (entry.total_reactions || 0) * this.SCORING_WEIGHTS.reaction_received +
      (entry.total_comments || 0) * this.SCORING_WEIGHTS.comment_received +
      (entry.total_followers || 0) * this.SCORING_WEIGHTS.follower_gained +
      (entry.streak_days || 0) * this.SCORING_WEIGHTS.daily_streak
    );
  }

  /**
   * Calculate badges based on achievements
   */
  private calculateBadges(entry: any): string[] {
    const badges: string[] = [];

    // Joke count badges
    if (entry.total_jokes >= 100) badges.push('🎭 Comedy Legend');
    else if (entry.total_jokes >= 50) badges.push('🎪 Joke Master');
    else if (entry.total_jokes >= 10) badges.push('🎯 Rising Star');

    // Engagement badges
    const totalEngagement = (entry.total_likes || 0) + (entry.total_reactions || 0) + (entry.total_comments || 0);
    if (totalEngagement >= 1000) badges.push('🔥 Viral Sensation');
    else if (totalEngagement >= 500) badges.push('⭐ Crowd Favorite');
    else if (totalEngagement >= 100) badges.push('👏 Audience Pleaser');

    // Consistency badges
    if (entry.streak_days >= 30) badges.push('🏆 Consistency King');
    else if (entry.streak_days >= 14) badges.push('📅 Daily Performer');
    else if (entry.streak_days >= 7) badges.push('🎯 Week Warrior');

    // Follower badges
    if (entry.total_followers >= 1000) badges.push('👑 Influencer');
    else if (entry.total_followers >= 100) badges.push('🌟 Popular Creator');
    else if (entry.total_followers >= 10) badges.push('🤝 Community Builder');

    // Special badges
    if (entry.rank === 1) badges.push('🥇 Champion');
    else if (entry.rank <= 3) badges.push('🥉 Top 3');
    else if (entry.rank <= 10) badges.push('🏅 Top 10');

    return badges;
  }

  /**
   * Get time filter for queries
   */
  private getTimeFilter(timeframe: string): string {
    const now = new Date();
    switch (timeframe) {
      case 'daily':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case 'weekly':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'monthly':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case 'seasonal':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(0).toISOString();
    }
  }

  /**
   * Update user's seasonal ranking
   */
  async updateSeasonalRanking(userId: string, seasonId: string): Promise<{ data: any; error: any }> {
    try {
      // Calculate user's current stats
      const userStats = await this.calculateUserStats(userId);
      
      const { data, error } = await supabase
        .from('seasonal_rankings')
        .upsert({
          user_id: userId,
          season_id: seasonId,
          points: userStats.score,
          jokes_count: userStats.total_jokes,
          engagement_score: userStats.total_likes + userStats.total_reactions + userStats.total_comments
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'update_seasonal_ranking' }
      });
      return { data: null, error };
    }
  }

  /**
   * Calculate user stats
   */
  private async calculateUserStats(userId: string): Promise<any> {
    const { data, error } = await supabase
      .rpc('calculate_user_stats', { target_user_id: userId });

    if (error) {
      throw error;
    }

    return data[0] || {};
  }
}

export const leaderboardService = new LeaderboardService();

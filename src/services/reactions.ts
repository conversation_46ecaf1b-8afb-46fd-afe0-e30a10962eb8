import { apiClient, ApiResponse } from './api';
import { supabase } from '../lib/supabase';
import { Reaction, ReactionCounts } from '../types';

export interface ToggleReactionResult {
  added: boolean;
  emoji: string;
  error: null;
}

export class ReactionsService {
  private static instance: ReactionsService;
  private reactionCache = new Map<string, { counts: ReactionCounts; userReactions: string[]; timestamp: number }>();
  private readonly CACHE_TTL = 2 * 60 * 1000; // 2 minutes

  static getInstance(): ReactionsService {
    if (!ReactionsService.instance) {
      ReactionsService.instance = new ReactionsService();
    }
    return ReactionsService.instance;
  }

  private getCacheKey(jokeId: string, userId?: string): string {
    return `${jokeId}_${userId || 'anonymous'}`;
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_TTL;
  }

  private clearCache(jokeId: string): void {
    // Clear all cache entries for this joke
    const keys = Array.from(this.reactionCache.keys());
    keys.forEach(key => {
      if (key.startsWith(jokeId)) {
        this.reactionCache.delete(key);
      }
    });
  }

  /**
   * Add a reaction to a joke
   */
  async addReaction(jokeId: string, emoji: string): Promise<ApiResponse<Reaction>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return {
          data: null,
          error: new Error('User must be authenticated'),
        };
      }

      const result = await apiClient.insert<Reaction>(
        'emoji_reactions',
        {
          joke_id: jokeId,
          emoji: emoji,
          user_id: user.id,
        },
        { select: '*' }
      );

      if (!result.error) {
        this.clearCache(jokeId);
      }

      return result;
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to add reaction'),
      };
    }
  }

  /**
   * Remove a reaction from a joke
   */
  async removeReaction(jokeId: string, emoji: string): Promise<ApiResponse<any>> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return {
          data: null,
          error: new Error('User must be authenticated'),
        };
      }

      const result = await apiClient.delete(
        'emoji_reactions',
        {
          joke_id: jokeId,
          emoji: emoji,
          user_id: user.id,
        }
      );

      if (!result.error) {
        this.clearCache(jokeId);
      }

      return result;
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to remove reaction'),
      };
    }
  }

  /**
   * Get reaction counts for a joke using RPC function
   */
  async getReactionCounts(jokeId: string): Promise<ReactionCounts> {
    try {
      const { data, error } = await supabase
        .rpc('get_joke_reaction_counts', { joke_uuid: jokeId });

      if (error) {
        console.error('Error getting reaction counts:', error);
        return {};
      }

      return (data || []).reduce((acc: ReactionCounts, { emoji, count }: any) => {
        acc[emoji] = parseInt(count, 10);
        return acc;
      }, {});
    } catch (error) {
      console.error('Error getting reaction counts:', error);
      return {};
    }
  }

  /**
   * Get user's reactions for a joke using RPC function
   */
  async getUserReactions(jokeId: string): Promise<string[]> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .rpc('get_user_joke_reactions', { 
          joke_uuid: jokeId,
          user_uuid: user.id
        });

      if (error) {
        console.error('Error getting user reactions:', error);
        return [];
      }

      return (data || []).map((row: any) => row.emoji);
    } catch (error) {
      console.error('Error getting user reactions:', error);
      return [];
    }
  }

  /**
   * Get both reaction counts and user reactions with caching
   */
  async getReactionsData(jokeId: string): Promise<{
    counts: ReactionCounts;
    userReactions: string[];
  }> {
    const { data: { user } } = await supabase.auth.getUser();
    const cacheKey = this.getCacheKey(jokeId, user?.id);
    
    // Check cache first
    const cached = this.reactionCache.get(cacheKey);
    if (cached && this.isValidCache(cached.timestamp)) {
      return {
        counts: cached.counts,
        userReactions: cached.userReactions,
      };
    }

    // Fetch fresh data
    const [counts, userReactions] = await Promise.all([
      this.getReactionCounts(jokeId),
      this.getUserReactions(jokeId),
    ]);

    // Cache the result
    this.reactionCache.set(cacheKey, {
      counts,
      userReactions,
      timestamp: Date.now(),
    });

    return { counts, userReactions };
  }

  /**
   * Toggle a reaction on a joke using RPC function
   */
  async toggleReaction(jokeId: string, emoji: string): Promise<ToggleReactionResult> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User must be authenticated');
      }

      const { data, error } = await supabase
        .rpc('toggle_emoji_reaction', {
          p_emoji: emoji,
          p_joke_id: jokeId
        });

      if (error) throw error;

      // Check if we got a successful response
      const result = Array.isArray(data) ? data[0] : data;
      if (!result?.success) {
        throw new Error(result?.error || 'Failed to toggle reaction');
      }

      // Clear cache after successful toggle
      this.clearCache(jokeId);

      return {
        added: result.is_added,
        emoji,
        error: null,
      };
    } catch (error) {
      console.error('Error toggling reaction:', error);
      throw error;
    }
  }

  /**
   * Subscribe to reaction changes for a joke
   */
  subscribeToReactions(jokeId: string, callback: (payload: any) => void) {
    return apiClient.subscribeToChanges('emoji_reactions', (payload) => {
      // Clear cache when reactions change
      this.clearCache(jokeId);
      callback(payload);
    }, { joke_id: jokeId });
  }

  /**
   * Clear all reaction caches
   */
  clearAllCache(): void {
    this.reactionCache.clear();
  }

  /**
   * Get popular reactions across all jokes
   */
  async getPopularReactions(limit = 10): Promise<Array<{ emoji: string; count: number }>> {
    try {
      const { data, error } = await supabase
        .rpc('get_popular_reactions', { result_limit: limit });

      if (error) {
        console.error('Error getting popular reactions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error getting popular reactions:', error);
      return [];
    }
  }
}

// Export singleton instance
export const reactionsService = ReactionsService.getInstance();

// Export the old service for backward compatibility
export const reactionService = {
  addReaction: (jokeId: string, emoji: string) => reactionsService.addReaction(jokeId, emoji),
  removeReaction: (jokeId: string, emoji: string) => reactionsService.removeReaction(jokeId, emoji),
  getReactionCounts: (jokeId: string) => reactionsService.getReactionCounts(jokeId),
  getUserReactions: (jokeId: string) => reactionsService.getUserReactions(jokeId),
  toggleReaction: (jokeId: string, emoji: string) => reactionsService.toggleReaction(jokeId, emoji),
};

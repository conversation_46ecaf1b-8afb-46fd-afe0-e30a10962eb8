import { apiClient, ApiResponse } from './api';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ilt<PERSON>, CreateJokeData } from '../types';

const JOKES_SELECT = `
  id,
  title,
  audio_url,
  user_id,
  created_at,
  likes_count,
  plays_count,
  comments_count,
  voice_effect,
  is_private,
  profiles!user_id (
    username,
    avatar_url
  )
`;

export interface UpdateJokeData {
  title?: string;
  voice_effect?: string;
  is_private?: boolean;
}

export class JokesService {
  private static instance: JokesService;

  static getInstance(): JokesService {
    if (!JokesService.instance) {
      JokesService.instance = new JokesService();
    }
    return JokesService.instance;
  }

  async getJokes(filters: JokeFilters = {}): Promise<ApiResponse<Joke[]>> {
    const {
      category,
      search,
      userId,
      sortBy = 'created_at',
      sortOrder = 'desc',
      limit = 20,
      offset = 0,
    } = filters;

    const queryFilters: Record<string, any> = {};

    // Apply user filter
    if (userId) {
      queryFilters.user_id = userId;
    }

    // Apply privacy filter (only show public jokes unless it's the user's own)
    if (!userId) {
      queryFilters.is_private = false;
    }

    const options = {
      select: JOKES_SELECT,
      filters: queryFilters,
      orderBy: { column: sortBy, ascending: sortOrder === 'asc' },
      range: { from: offset, to: offset + limit - 1 },
      count: 'exact' as const,
    };

    // Handle category filter (requires join)
    if (category && category !== 'all') {
      // For category filtering, we need to modify the query
      // This is a limitation of the generic API client
      // We'll handle this case specially
      return this.getJokesByCategory(category, filters);
    }

    // Handle search filter
    if (search) {
      // For text search, we also need special handling
      return this.searchJokes(search, filters);
    }

    return apiClient.query<Joke[]>('jokes', options);
  }

  private async getJokesByCategory(
    category: string,
    filters: JokeFilters
  ): Promise<ApiResponse<Joke[]>> {
    const {
      sortBy = 'created_at',
      sortOrder = 'desc',
      limit = 20,
      offset = 0,
    } = filters;

    try {
      // Use direct Supabase query for complex joins
      const { supabase } = await import('../lib/supabase');

      let query = supabase
        .from('jokes')
        .select(`
          id,
          title,
          audio_url,
          user_id,
          created_at,
          likes_count,
          plays_count,
          comments_count,
          voice_effect,
          is_private,
          profiles!user_id (
            username,
            avatar_url
          ),
          joke_categories!inner (
            categories!inner (
              slug
            )
          )
        `, { count: 'exact' })
        .eq('joke_categories.categories.slug', category)
        .eq('is_private', false)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);

      const result = await query;

      return {
        data: result.data,
        error: result.error,
        count: result.count,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to fetch jokes by category'),
      };
    }
  }

  private async searchJokes(
    searchQuery: string,
    filters: JokeFilters
  ): Promise<ApiResponse<Joke[]>> {
    const {
      sortBy = 'created_at',
      sortOrder = 'desc',
      limit = 20,
      offset = 0,
    } = filters;

    try {
      // Use direct Supabase query for text search
      const { supabase } = await import('../lib/supabase');
      
      let query = supabase
        .from('jokes')
        .select(`
          id,
          title,
          audio_url,
          user_id,
          created_at,
          likes_count,
          plays_count,
          comments_count,
          voice_effect,
          is_private,
          profiles!user_id (
            username,
            avatar_url
          )
        `, { count: 'exact' })
        .or(`title.ilike.%${searchQuery}%, content.ilike.%${searchQuery}%`)
        .eq('is_private', false)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);

      const result = await query;

      return {
        data: result.data,
        error: result.error,
        count: result.count,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to search jokes'),
      };
    }
  }

  async getJoke(id: string): Promise<ApiResponse<Joke>> {
    return apiClient.query<Joke>(
      'jokes',
      {
        select: JOKES_SELECT,
        filters: { id },
        single: true,
      }
    );
  }

  async createJoke(jokeData: CreateJokeData): Promise<ApiResponse<Joke>> {
    const { category_ids, ...jokeFields } = jokeData;

    // First create the joke
    const jokeResult = await apiClient.insert<Joke>(
      'jokes',
      jokeFields,
      { select: JOKES_SELECT }
    );

    if (jokeResult.error || !jokeResult.data) {
      return jokeResult;
    }

    const joke = Array.isArray(jokeResult.data) ? jokeResult.data[0] : jokeResult.data;

    // Then create category associations if provided
    if (category_ids && category_ids.length > 0) {
      const categoryAssociations = category_ids.map(categoryId => ({
        joke_id: joke.id,
        category_id: categoryId,
      }));

      await apiClient.insert('joke_categories', categoryAssociations);
    }

    return {
      data: joke,
      error: null,
    };
  }

  async updateJoke(id: string, updates: UpdateJokeData): Promise<ApiResponse<Joke>> {
    return apiClient.update<Joke>(
      'jokes',
      updates,
      { id },
      { select: JOKES_SELECT }
    );
  }

  async deleteJoke(id: string): Promise<ApiResponse<any>> {
    return apiClient.delete('jokes', { id });
  }

  async incrementPlayCount(id: string): Promise<ApiResponse<Joke>> {
    try {
      const { supabase } = await import('../lib/supabase');
      
      const { data, error } = await supabase.rpc('increment_play_count', {
        joke_id: id
      });

      return {
        data,
        error,
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to increment play count'),
      };
    }
  }

  async toggleLike(jokeId: string, userId: string): Promise<ApiResponse<any>> {
    try {
      const { supabase } = await import('../lib/supabase');
      
      // Check if like exists
      const { data: existingLike } = await supabase
        .from('joke_likes')
        .select('*')
        .eq('joke_id', jokeId)
        .eq('user_id', userId)
        .single();

      if (existingLike) {
        // Remove like
        const { error } = await supabase
          .from('joke_likes')
          .delete()
          .eq('joke_id', jokeId)
          .eq('user_id', userId);

        if (error) throw error;

        // Decrement likes count
        await supabase.rpc('decrement_likes_count', { joke_id: jokeId });

        return { data: { liked: false }, error: null };
      } else {
        // Add like
        const { error } = await supabase
          .from('joke_likes')
          .insert({ joke_id: jokeId, user_id: userId });

        if (error) throw error;

        // Increment likes count
        await supabase.rpc('increment_likes_count', { joke_id: jokeId });

        return { data: { liked: true }, error: null };
      }
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error : new Error('Failed to toggle like'),
      };
    }
  }

  async getUserJokes(userId: string, limit = 20, offset = 0): Promise<ApiResponse<Joke[]>> {
    return this.getJokes({
      userId,
      sortBy: 'created_at',
      sortOrder: 'desc',
      limit,
      offset,
    });
  }

  async getTopJokes(limit = 20, offset = 0): Promise<ApiResponse<Joke[]>> {
    return this.getJokes({
      sortBy: 'likes_count',
      sortOrder: 'desc',
      limit,
      offset,
    });
  }

  async getRecentJokes(limit = 20, offset = 0): Promise<ApiResponse<Joke[]>> {
    return this.getJokes({
      sortBy: 'created_at',
      sortOrder: 'desc',
      limit,
      offset,
    });
  }

  // Real-time subscription for jokes
  subscribeToJokes(callback: (payload: any) => void) {
    return apiClient.subscribeToChanges('jokes', callback);
  }

  // Real-time subscription for specific joke
  subscribeToJoke(jokeId: string, callback: (payload: any) => void) {
    return apiClient.subscribeToChanges('jokes', callback, { id: jokeId });
  }
}

// Export singleton instance
export const jokesService = JokesService.getInstance();

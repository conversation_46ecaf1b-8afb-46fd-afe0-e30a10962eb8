import { supabase } from '../lib/supabase';
import * as Sentry from '@sentry/react';

export type ReportType = 'joke' | 'user' | 'comment';
export type ReportStatus = 'pending' | 'reviewed' | 'approved' | 'rejected' | 'escalated';
export type ReportReason = 
  | 'inappropriate_content'
  | 'spam'
  | 'harassment'
  | 'hate_speech'
  | 'violence'
  | 'sexual_content'
  | 'copyright'
  | 'misinformation'
  | 'other';

export interface Report {
  id: string;
  reporter_id: string;
  reported_item_id: string;
  reported_user_id?: string;
  report_type: ReportType;
  reason: ReportReason;
  description?: string;
  status: ReportStatus;
  reviewed_by?: string;
  reviewed_at?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateReportData {
  reported_item_id: string;
  reported_user_id?: string;
  report_type: ReportType;
  reason: ReportReason;
  description?: string;
}

export interface UpdateReportData {
  status?: ReportStatus;
  admin_notes?: string;
  reviewed_by?: string;
}

export interface ReportStats {
  total_reports: number;
  pending_reports: number;
  reviewed_reports: number;
  approved_reports: number;
  rejected_reports: number;
}

class ReportService {
  /**
   * Create a new report
   */
  async createReport(reportData: CreateReportData): Promise<{ data: Report | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('reports')
        .insert([{
          reporter_id: user.id,
          ...reportData
        }])
        .select()
        .single();

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'create_report' },
          extra: { reportData }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Get reports for admin panel
   */
  async getReports(filters?: {
    status?: ReportStatus;
    type?: ReportType;
    limit?: number;
    offset?: number;
  }): Promise<{ data: Report[] | null; error: any }> {
    try {
      let query = supabase
        .from('reports')
        .select(`
          *,
          reporter:reporter_id(username, avatar_url),
          reported_user:reported_user_id(username, avatar_url)
        `)
        .order('created_at', { ascending: false });

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.type) {
        query = query.eq('report_type', filters.type);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'get_reports' },
          extra: { filters }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Update report status (admin only)
   */
  async updateReport(reportId: string, updates: UpdateReportData): Promise<{ data: Report | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const updateData: any = { ...updates };
      
      if (updates.status && updates.status !== 'pending') {
        updateData.reviewed_by = user.id;
        updateData.reviewed_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('reports')
        .update(updateData)
        .eq('id', reportId)
        .select()
        .single();

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'update_report' },
          extra: { reportId, updates }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Get report statistics
   */
  async getReportStats(): Promise<{ data: ReportStats | null; error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_report_stats');

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'get_report_stats' }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Block a user
   */
  async blockUser(blockedUserId: string, reason?: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('user_blocks')
        .insert([{
          blocker_id: user.id,
          blocked_id: blockedUserId,
          reason
        }])
        .select()
        .single();

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'block_user' },
          extra: { blockedUserId, reason }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Unblock a user
   */
  async unblockUser(blockedUserId: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('user_blocks')
        .delete()
        .eq('blocker_id', user.id)
        .eq('blocked_id', blockedUserId);

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'unblock_user' },
          extra: { blockedUserId }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Get blocked users for current user
   */
  async getBlockedUsers(): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('user_blocks')
        .select(`
          *,
          blocked_user:blocked_id(username, avatar_url)
        `)
        .eq('blocker_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'get_blocked_users' }
        });
      }

      return { data, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: null, error };
    }
  }

  /**
   * Check if a user is blocked
   */
  async isUserBlocked(userId: string): Promise<{ data: boolean; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: false, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase.rpc('is_user_blocked', {
        target_user_id: userId,
        current_user_id: user.id
      });

      if (error) {
        Sentry.captureException(error, {
          tags: { operation: 'is_user_blocked' },
          extra: { userId }
        });
      }

      return { data: data || false, error };
    } catch (error) {
      Sentry.captureException(error);
      return { data: false, error };
    }
  }
}

export const reportService = new ReportService();

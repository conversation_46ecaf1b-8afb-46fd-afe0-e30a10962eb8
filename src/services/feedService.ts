import { supabase } from '../lib/supabase';
import { followingService } from './followingService';
import * as Sentry from '@sentry/react';

export interface FeedItem {
  id: string;
  title: string;
  content?: string;
  audio_url: string;
  user_id: string;
  created_at: string;
  updated_at?: string;
  is_private: boolean;
  effect?: string;
  duration?: number;
  
  // Engagement metrics
  likes_count: number;
  comments_count: number;
  reactions_count: number;
  total_engagement: number;
  
  // User data
  profiles?: {
    id: string;
    username: string;
    avatar_url?: string;
    bio?: string;
  };
  
  // User interaction state
  is_liked?: boolean;
  user_reactions?: string[];
  
  // Feed algorithm data
  feed_score?: number;
  feed_reason?: string;
  is_trending?: boolean;
  is_from_following?: boolean;
}

export interface FeedOptions {
  algorithm?: 'chronological' | 'engagement' | 'personalized' | 'trending';
  limit?: number;
  offset?: number;
  includeFollowing?: boolean;
  includeDiscovery?: boolean;
  timeRange?: 'hour' | 'day' | 'week' | 'month' | 'all';
}

export interface FeedResponse {
  items: FeedItem[];
  has_more: boolean;
  next_offset: number;
  algorithm_used: string;
}

class FeedService {
  private readonly ENGAGEMENT_WEIGHTS = {
    like: 1,
    reaction: 1.5,
    comment: 3,
    follow_bonus: 2
  };

  private readonly TIME_DECAY_HOURS = 24;

  /**
   * Get personalized feed for user
   */
  async getPersonalizedFeed(options: FeedOptions = {}): Promise<{ data: FeedResponse | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        // Return public feed for non-authenticated users
        return this.getPublicFeed(options);
      }

      const {
        algorithm = 'personalized',
        limit = 20,
        offset = 0,
        includeFollowing = true,
        includeDiscovery = true,
        timeRange = 'week'
      } = options;

      let feedItems: FeedItem[] = [];

      switch (algorithm) {
        case 'chronological':
          feedItems = await this.getChronologicalFeed(user.id, { limit, offset, timeRange });
          break;
        case 'engagement':
          feedItems = await this.getEngagementFeed(user.id, { limit, offset, timeRange });
          break;
        case 'trending':
          feedItems = await this.getTrendingFeed(user.id, { limit, offset, timeRange });
          break;
        case 'personalized':
        default:
          feedItems = await this.getPersonalizedFeedItems(user.id, {
            limit,
            offset,
            includeFollowing,
            includeDiscovery,
            timeRange
          });
          break;
      }

      // Add user interaction state
      feedItems = await this.enrichWithUserState(feedItems, user.id);

      return {
        data: {
          items: feedItems,
          has_more: feedItems.length === limit,
          next_offset: offset + feedItems.length,
          algorithm_used: algorithm
        },
        error: null
      };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_personalized_feed' },
        extra: { options }
      });
      return { data: null, error };
    }
  }

  /**
   * Get public feed (for non-authenticated users)
   */
  async getPublicFeed(options: FeedOptions = {}): Promise<{ data: FeedResponse | null; error: any }> {
    try {
      const { limit = 20, offset = 0, algorithm = 'trending' } = options;

      const { data, error } = await supabase
        .from('jokes')
        .select(`
          *,
          profiles!user_id(id, username, avatar_url, bio)
        `)
        .eq('is_private', false)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      const feedItems = await this.calculateEngagementScores(data || []);

      return {
        data: {
          items: feedItems,
          has_more: feedItems.length === limit,
          next_offset: offset + feedItems.length,
          algorithm_used: 'public'
        },
        error: null
      };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_public_feed' }
      });
      return { data: null, error };
    }
  }

  /**
   * Get chronological feed
   */
  private async getChronologicalFeed(
    userId: string,
    options: { limit: number; offset: number; timeRange: string }
  ): Promise<FeedItem[]> {
    const { limit, offset, timeRange } = options;
    const timeFilter = this.getTimeFilter(timeRange);

    const { data, error } = await supabase
      .from('jokes')
      .select(`
        *,
        profiles!user_id(id, username, avatar_url, bio)
      `)
      .eq('is_private', false)
      .gte('created_at', timeFilter)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    return this.calculateEngagementScores(data || []);
  }

  /**
   * Get engagement-based feed
   */
  private async getEngagementFeed(
    userId: string,
    options: { limit: number; offset: number; timeRange: string }
  ): Promise<FeedItem[]> {
    const { limit, offset, timeRange } = options;
    const timeFilter = this.getTimeFilter(timeRange);

    // Get jokes with engagement metrics
    const { data, error } = await supabase
      .rpc('get_jokes_with_engagement', {
        time_filter: timeFilter,
        limit_count: limit,
        offset_count: offset
      });

    if (error) {
      throw error;
    }

    return this.calculateEngagementScores(data || []);
  }

  /**
   * Get trending feed
   */
  private async getTrendingFeed(
    userId: string,
    options: { limit: number; offset: number; timeRange: string }
  ): Promise<FeedItem[]> {
    const { limit, offset, timeRange } = options;
    const timeFilter = this.getTimeFilter(timeRange);

    // Get trending jokes (high engagement in short time)
    const { data, error } = await supabase
      .rpc('get_trending_jokes', {
        time_filter: timeFilter,
        limit_count: limit,
        offset_count: offset
      });

    if (error) {
      throw error;
    }

    return this.calculateEngagementScores(data || [], true);
  }

  /**
   * Get personalized feed items
   */
  private async getPersonalizedFeedItems(
    userId: string,
    options: {
      limit: number;
      offset: number;
      includeFollowing: boolean;
      includeDiscovery: boolean;
      timeRange: string;
    }
  ): Promise<FeedItem[]> {
    const { limit, offset, includeFollowing, includeDiscovery, timeRange } = options;
    const timeFilter = this.getTimeFilter(timeRange);

    let feedItems: FeedItem[] = [];

    // 1. Get content from followed users (70% of feed)
    if (includeFollowing) {
      const followingFeed = await this.getFollowingFeed(userId, {
        limit: Math.ceil(limit * 0.7),
        timeFilter
      });
      feedItems = [...feedItems, ...followingFeed];
    }

    // 2. Get discovery content (30% of feed)
    if (includeDiscovery && feedItems.length < limit) {
      const discoveryFeed = await this.getDiscoveryFeed(userId, {
        limit: limit - feedItems.length,
        timeFilter,
        excludeIds: feedItems.map(item => item.id)
      });
      feedItems = [...feedItems, ...discoveryFeed];
    }

    // 3. Sort by personalized score
    feedItems = this.sortByPersonalizedScore(feedItems, userId);

    return feedItems.slice(offset, offset + limit);
  }

  /**
   * Get feed from followed users
   */
  private async getFollowingFeed(
    userId: string,
    options: { limit: number; timeFilter: string }
  ): Promise<FeedItem[]> {
    const { limit, timeFilter } = options;

    const { data, error } = await supabase
      .from('jokes')
      .select(`
        *,
        profiles!user_id(id, username, avatar_url, bio)
      `)
      .eq('is_private', false)
      .gte('created_at', timeFilter)
      .in('user_id', 
        supabase
          .from('follows')
          .select('following_id')
          .eq('follower_id', userId)
      )
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    const items = await this.calculateEngagementScores(data || []);
    return items.map((item: any) => ({ ...item, is_from_following: true, feed_reason: 'Following' }));
  }

  /**
   * Get discovery feed
   */
  private async getDiscoveryFeed(
    userId: string,
    options: { limit: number; timeFilter: string; excludeIds: string[] }
  ): Promise<FeedItem[]> {
    const { limit, timeFilter, excludeIds } = options;

    let query = supabase
      .from('jokes')
      .select(`
        *,
        profiles!user_id(id, username, avatar_url, bio)
      `)
      .eq('is_private', false)
      .gte('created_at', timeFilter);

    if (excludeIds.length > 0) {
      query = query.not('id', 'in', `(${excludeIds.join(',')})`);
    }

    const { data, error } = await query
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    const items = await this.calculateEngagementScores(data || []);
    return items.map((item: any) => ({ ...item, is_from_following: false, feed_reason: 'Discovery' }));
  }

  /**
   * Calculate engagement scores for jokes
   */
  private async calculateEngagementScores(jokes: any[], isTrending = false): Promise<FeedItem[]> {
    const jokeIds = jokes.map(joke => joke.id);

    // Get engagement data
    const [likesData, reactionsData, commentsData] = await Promise.all([
      this.getLikesCount(jokeIds),
      this.getReactionsCount(jokeIds),
      this.getCommentsCount(jokeIds)
    ]);

    return jokes.map(joke => {
      const likes = likesData[joke.id] || 0;
      const reactions = reactionsData[joke.id] || 0;
      const comments = commentsData[joke.id] || 0;

      const totalEngagement = likes + reactions + comments;
      const ageHours = (Date.now() - new Date(joke.created_at).getTime()) / (1000 * 60 * 60);
      const timeDecay = Math.exp(-ageHours / this.TIME_DECAY_HOURS);

      const engagementScore = (
        likes * this.ENGAGEMENT_WEIGHTS.like +
        reactions * this.ENGAGEMENT_WEIGHTS.reaction +
        comments * this.ENGAGEMENT_WEIGHTS.comment
      ) * timeDecay;

      return {
        ...joke,
        likes_count: likes,
        reactions_count: reactions,
        comments_count: comments,
        total_engagement: totalEngagement,
        feed_score: engagementScore,
        is_trending: isTrending && engagementScore > 10 && ageHours < 24
      };
    });
  }

  /**
   * Sort by personalized score
   */
  private sortByPersonalizedScore(items: FeedItem[], userId: string): FeedItem[] {
    return items.sort((a, b) => {
      let scoreA = a.feed_score || 0;
      let scoreB = b.feed_score || 0;

      // Boost content from followed users
      if (a.is_from_following) scoreA *= this.ENGAGEMENT_WEIGHTS.follow_bonus;
      if (b.is_from_following) scoreB *= this.ENGAGEMENT_WEIGHTS.follow_bonus;

      // Boost trending content
      if (a.is_trending) scoreA *= 1.5;
      if (b.is_trending) scoreB *= 1.5;

      return scoreB - scoreA;
    });
  }

  /**
   * Enrich feed items with user interaction state
   */
  private async enrichWithUserState(items: FeedItem[], userId: string): Promise<FeedItem[]> {
    if (items.length === 0) return items;

    const jokeIds = items.map(item => item.id);

    // Get user likes
    const { data: likes } = await supabase
      .from('joke_likes')
      .select('joke_id')
      .eq('user_id', userId)
      .in('joke_id', jokeIds);

    // Get user reactions
    const { data: reactions } = await supabase
      .from('emoji_reactions')
      .select('joke_id, emoji')
      .eq('user_id', userId)
      .in('joke_id', jokeIds);

    const likedJokes = new Set((likes || []).map((like: any) => like.joke_id));
    const userReactionsByJoke = (reactions || []).reduce((acc: any, reaction: any) => {
      if (!acc[reaction.joke_id]) acc[reaction.joke_id] = [];
      acc[reaction.joke_id].push(reaction.emoji);
      return acc;
    }, {} as Record<string, string[]>);

    return items.map(item => ({
      ...item,
      is_liked: likedJokes.has(item.id),
      user_reactions: userReactionsByJoke[item.id] || []
    }));
  }

  /**
   * Helper methods
   */
  private getTimeFilter(timeRange: string): string {
    const now = new Date();
    switch (timeRange) {
      case 'hour':
        return new Date(now.getTime() - 60 * 60 * 1000).toISOString();
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(0).toISOString();
    }
  }

  private async getLikesCount(jokeIds: string[]): Promise<Record<string, number>> {
    const { data } = await supabase
      .from('joke_likes')
      .select('joke_id')
      .in('joke_id', jokeIds);

    return (data || []).reduce((acc: any, like: any) => {
      acc[like.joke_id] = (acc[like.joke_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private async getReactionsCount(jokeIds: string[]): Promise<Record<string, number>> {
    const { data } = await supabase
      .from('emoji_reactions')
      .select('joke_id')
      .in('joke_id', jokeIds);

    return (data || []).reduce((acc: any, reaction: any) => {
      acc[reaction.joke_id] = (acc[reaction.joke_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private async getCommentsCount(jokeIds: string[]): Promise<Record<string, number>> {
    const { data } = await supabase
      .from('comments')
      .select('joke_id')
      .in('joke_id', jokeIds);

    return (data || []).reduce((acc: any, comment: any) => {
      acc[comment.joke_id] = (acc[comment.joke_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }
}

export const feedService = new FeedService();

import { supabase } from '../lib/supabase';
import * as Sentry from '@sentry/react';

export interface FollowRelationship {
  follower_id: string;
  following_id: string;
  created_at: string;
}

export interface UserProfile {
  id: string;
  username: string;
  avatar_url?: string;
  bio?: string;
  followers_count?: number;
  following_count?: number;
  is_following?: boolean;
  is_followed_by?: boolean;
}

export interface FollowStats {
  followers_count: number;
  following_count: number;
}

class FollowingService {
  /**
   * Follow a user
   */
  async followUser(userId: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      if (user.id === userId) {
        return { data: null, error: { message: 'Cannot follow yourself' } };
      }

      const { data, error } = await supabase
        .from('follows')
        .insert([{
          follower_id: user.id,
          following_id: userId
        }])
        .select()
        .single();

      if (error) {
        // Handle duplicate follow attempts gracefully
        if (error.code === '23505') { // Unique constraint violation
          return { data: null, error: { message: 'Already following this user' } };
        }
        throw error;
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'follow_user' },
        extra: { userId }
      });
      return { data: null, error };
    }
  }

  /**
   * Unfollow a user
   */
  async unfollowUser(userId: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      const { data, error } = await supabase
        .from('follows')
        .delete()
        .eq('follower_id', user.id)
        .eq('following_id', userId);

      if (error) {
        throw error;
      }

      return { data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'unfollow_user' },
        extra: { userId }
      });
      return { data: null, error };
    }
  }

  /**
   * Check if current user is following another user
   */
  async isFollowing(userId: string): Promise<{ data: boolean; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: false, error: null };
      }

      const { data, error } = await supabase
        .from('follows')
        .select('follower_id')
        .eq('follower_id', user.id)
        .eq('following_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return { data: !!data, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'is_following' },
        extra: { userId }
      });
      return { data: false, error };
    }
  }

  /**
   * Get follow statistics for a user
   */
  async getFollowStats(userId: string): Promise<{ data: FollowStats | null; error: any }> {
    try {
      // Get followers count
      const { count: followersCount, error: followersError } = await supabase
        .from('follows')
        .select('*', { count: 'exact', head: true })
        .eq('following_id', userId);

      if (followersError) {
        throw followersError;
      }

      // Get following count
      const { count: followingCount, error: followingError } = await supabase
        .from('follows')
        .select('*', { count: 'exact', head: true })
        .eq('follower_id', userId);

      if (followingError) {
        throw followingError;
      }

      return {
        data: {
          followers_count: followersCount || 0,
          following_count: followingCount || 0
        },
        error: null
      };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_follow_stats' },
        extra: { userId }
      });
      return { data: null, error };
    }
  }

  /**
   * Get followers list for a user
   */
  async getFollowers(
    userId: string,
    options: { limit?: number; offset?: number } = {}
  ): Promise<{ data: UserProfile[] | null; error: any }> {
    try {
      const { limit = 20, offset = 0 } = options;

      const { data, error } = await supabase
        .from('follows')
        .select(`
          follower_id,
          created_at,
          follower:follower_id(
            id,
            username,
            avatar_url,
            bio
          )
        `)
        .eq('following_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      const followers = (data || []).map((follow: any) => ({
        ...follow.follower,
        followed_at: follow.created_at
      }));

      return { data: followers, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_followers' },
        extra: { userId, options }
      });
      return { data: null, error };
    }
  }

  /**
   * Get following list for a user
   */
  async getFollowing(
    userId: string,
    options: { limit?: number; offset?: number } = {}
  ): Promise<{ data: UserProfile[] | null; error: any }> {
    try {
      const { limit = 20, offset = 0 } = options;

      const { data, error } = await supabase
        .from('follows')
        .select(`
          following_id,
          created_at,
          following:following_id(
            id,
            username,
            avatar_url,
            bio
          )
        `)
        .eq('follower_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      const following = (data || []).map((follow: any) => ({
        ...follow.following,
        followed_at: follow.created_at
      }));

      return { data: following, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_following' },
        extra: { userId, options }
      });
      return { data: null, error };
    }
  }

  /**
   * Get suggested users to follow
   */
  async getSuggestedUsers(
    options: { limit?: number; exclude?: string[] } = {}
  ): Promise<{ data: UserProfile[] | null; error: any }> {
    try {
      const { limit = 10, exclude = [] } = options;
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      // Get users that current user is not following
      // and have recent activity (posted jokes)
      let query = supabase
        .from('profiles')
        .select(`
          id,
          username,
          avatar_url,
          bio,
          created_at
        `)
        .neq('id', user.id);

      // Exclude specified users
      if (exclude.length > 0) {
        query = query.not('id', 'in', `(${exclude.join(',')})`);
      }

      const { data: allUsers, error: usersError } = await query
        .order('created_at', { ascending: false })
        .limit(limit * 3); // Get more to filter out already followed

      if (usersError) {
        throw usersError;
      }

      // Get users current user is already following
      const { data: following, error: followingError } = await supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', user.id);

      if (followingError) {
        throw followingError;
      }

      const followingIds = new Set((following || []).map((f: any) => f.following_id));

      // Filter out already followed users
      const suggestedUsers = (allUsers || [])
        .filter((u: any) => !followingIds.has(u.id))
        .slice(0, limit);

      return { data: suggestedUsers, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_suggested_users' },
        extra: { options }
      });
      return { data: null, error };
    }
  }

  /**
   * Get mutual followers between current user and another user
   */
  async getMutualFollowers(userId: string): Promise<{ data: UserProfile[] | null; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: null, error: { message: 'User not authenticated' } };
      }

      // Get users that both current user and target user follow
      const { data, error } = await supabase
        .rpc('get_mutual_followers', {
          user1_id: user.id,
          user2_id: userId
        });

      if (error) {
        throw error;
      }

      return { data: data || [], error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'get_mutual_followers' },
        extra: { userId }
      });
      return { data: null, error };
    }
  }

  /**
   * Subscribe to follow relationship changes
   */
  subscribeToFollows(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`follows:${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'follows',
          filter: `following_id=eq.${userId}`
        },
        callback
      )
      .subscribe();
  }

  /**
   * Batch check follow status for multiple users
   */
  async batchCheckFollowStatus(userIds: string[]): Promise<{ data: Record<string, boolean>; error: any }> {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { data: {}, error: null };
      }

      const { data, error } = await supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', user.id)
        .in('following_id', userIds);

      if (error) {
        throw error;
      }

      const followingSet = new Set((data || []).map((f: any) => f.following_id));
      const result = userIds.reduce((acc, id) => {
        acc[id] = followingSet.has(id);
        return acc;
      }, {} as Record<string, boolean>);

      return { data: result, error: null };

    } catch (error) {
      Sentry.captureException(error, {
        tags: { operation: 'batch_check_follow_status' },
        extra: { userIds }
      });
      return { data: {}, error };
    }
  }
}

export const followingService = new FollowingService();

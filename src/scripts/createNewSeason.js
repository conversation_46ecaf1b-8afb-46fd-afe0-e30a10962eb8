const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

dotenv.config({ path: '../../.env' });

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL and Key must be provided in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createNewSeason() {
  const now = new Date();
  const seasonId = `${now.getFullYear()}-${Math.floor(now.getMonth() / 3) + 1}`;
  const startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
  const endDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);

  const { data, error } = await supabase
    .from('seasons')
    .insert([
      { id: seasonId, start_date: startDate, end_date: endDate },
    ]);

  if (error) {
    console.error('Error creating new season:', error);
  } else {
    console.log('Successfully created new season:', data);
  }
}

createNewSeason();

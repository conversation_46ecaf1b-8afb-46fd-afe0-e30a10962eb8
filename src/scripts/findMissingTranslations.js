const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Load translations from i18n.js
const i18nContent = require('../i18n').default.getDataByLanguage('fr').translation;

// Function to flatten nested objects into dot notation
function flattenObject(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? prefix + '.' : '';
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k], pre + k));
    } else {
      acc[pre + k] = obj[k];
    }
    return acc;
  }, {});
}

// Function to extract translation keys from a file
function extractTranslationKeys(content, filePath) {
  const keys = new Set();
  
  // Skip if the file is a migration or SQL file
  if (filePath.includes('migrations/') || filePath.endsWith('.sql')) {
    return [];
  }

  // Common patterns for translation key usage
  const patterns = [
    // t('key') or t("key")
    /t\(['"]([^'"]+)['"]\)/g,
    // t(`key`)
    /t\(`([^`]+)`\)/g,
    // t('key', {
    /t\(['"]([^'"]+)['"],\s*{/g,
  ];

  // Extract keys from all patterns
  patterns.forEach(pattern => {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      if (match[1] && !match[1].includes('${')) {
        keys.add(match[1]);
      }
    }
  });

  return Array.from(keys);
}

// Filter out system keys that don't need translation
function isTranslatableKey(key) {
  // Skip if the key contains SQL-like content
  if (key.includes('select') || key.includes('from') || key.includes('where')) {
    return false;
  }

  // Skip if the key is a template literal or contains special characters
  if (key.includes('${') || key.includes('*') || key.includes('\n')) {
    return false;
  }

  // Skip system patterns
  const systemPatterns = [
    /^web-vitals$/,
    /^\*$/,
    /^\.$/,
    /^@$/,
    /^id$/,
    /^key$/,
    /^type$/,
    /^token$/,
    /^user_id$/,
    /^emoji,\s/,
    /^\n$/,
    /^audio$/,
    /^profiles:/,
    /^jokes:/,
    /^categories:/,
    /^featured_joke:/
  ];

  return !systemPatterns.some(pattern => pattern.test(key));
}

async function main() {
  console.log('🔍 Starting translation audit...\n');

  // Get all JS/JSX files in src directory
  const srcFiles = execSync('find src -type f -name "*.js*"')
    .toString()
    .split('\n')
    .filter(Boolean)
    .filter(file => !file.includes('migrations/') && !file.endsWith('.sql'));

  // Collect all translation keys used in the codebase
  const usedKeys = new Set();
  const keyLocations = new Map(); // Track where each key is used

  srcFiles.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    const keys = extractTranslationKeys(content, file);
    keys.forEach(key => {
      usedKeys.add(key);
      if (!keyLocations.has(key)) {
        keyLocations.set(key, []);
      }
      keyLocations.get(key).push(file);
    });
  });

  // Get existing translations
  const existingTranslations = {
    fr: flattenObject(i18nContent),
    ar: flattenObject(require('../i18n').default.getDataByLanguage('ar').translation)
  };

  // Find missing translations
  const missingTranslations = {
    fr: [],
    ar: []
  };

  // Filter out system keys and collect missing translations
  const translatableKeys = Array.from(usedKeys).filter(isTranslatableKey);

  translatableKeys.forEach(key => {
    ['fr', 'ar'].forEach(lang => {
      if (!existingTranslations[lang][key]) {
        missingTranslations[lang].push(key);
      }
    });
  });

  // Generate report
  console.log('📊 Translation Keys Analysis\n');
  console.log(`Total files scanned: ${srcFiles.length}`);
  console.log(`Total translation keys found: ${usedKeys.size}`);
  console.log(`Translatable keys: ${translatableKeys.length}`);

  ['fr', 'ar'].forEach(lang => {
    const langEmoji = lang === 'fr' ? '🇫🇷' : '🇲🇦';
    console.log(`\n${langEmoji} ${lang.toUpperCase()} Missing Translations (${missingTranslations[lang].length}):`);
    
    if (missingTranslations[lang].length === 0) {
      console.log('✅ All translations present');
    } else {
      missingTranslations[lang].forEach(key => {
        const locations = keyLocations.get(key);
        console.log(`❌ ${key}`);
        console.log(`   Found in: ${locations.map(l => l.replace('src/', '')).join(', ')}`);
      });
    }
  });

  // Generate SQL
  if (missingTranslations.fr.length > 0 || missingTranslations.ar.length > 0) {
    const sqlPath = path.join(__dirname, '../../supabase/migrations/20241129161560_add_ui_translations.sql');
    let sql = '-- Add UI translations\n';
    sql += 'INSERT INTO translations (language, key, value)\nVALUES\n';

    const sqlValues = [];
    
    if (missingTranslations.fr.length > 0) {
      sql += '\n    -- French translations\n';
      missingTranslations.fr.forEach(key => {
        sqlValues.push(`    ('fr', '${key}', '[NEEDS TRANSLATION] ${key}')`);
      });
    }

    if (missingTranslations.ar.length > 0) {
      sql += '\n    -- Arabic translations\n';
      missingTranslations.ar.forEach(key => {
        sqlValues.push(`    ('ar', '${key}', '[يحتاج إلى ترجمة] ${key}')`);
      });
    }

    sql += sqlValues.join(',\n');
    sql += '\nON CONFLICT (language, key) \nDO UPDATE SET \n    value = EXCLUDED.value,\n    updated_at = TIMEZONE(\'utc\'::text, NOW());';

    fs.writeFileSync(sqlPath, sql);
    console.log(`\n📝 SQL migration generated: ${sqlPath}`);
  }
}

main().catch(console.error); 
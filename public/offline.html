<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Noukta</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .container {
            max-width: 500px;
            width: 100%;
        }

        .icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            justify-content: center;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            border-color: transparent;
        }

        .btn-primary:hover {
            background: white;
            transform: translateY(-2px);
        }

        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .status.online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status.offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1rem;
            }
            
            .icon {
                width: 80px;
                height: 80px;
                font-size: 32px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            📱
        </div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. 
            Don't worry, you can still browse some content that's been cached!
        </p>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="tryReconnect()">
                🔄 Try Again
            </button>
            
            <a href="/" class="btn">
                🏠 Go to Home
            </a>
            
            <button class="btn" onclick="showCachedContent()">
                📱 Browse Offline Content
            </button>
        </div>
        
        <div class="status offline" id="connectionStatus">
            <span class="pulse">🔴</span> You are currently offline
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const status = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                status.className = 'status online';
                status.innerHTML = '<span>🟢</span> You are back online!';
                
                // Auto-redirect after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                status.className = 'status offline';
                status.innerHTML = '<span class="pulse">🔴</span> You are currently offline';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Try to reconnect
        function tryReconnect() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '⏳ Checking...';
            btn.disabled = true;
            
            // Simple connectivity check
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(() => {
                    btn.innerHTML = '✅ Connected!';
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                })
                .catch(() => {
                    btn.innerHTML = '❌ Still Offline';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }, 2000);
                });
        }

        // Show cached content
        function showCachedContent() {
            // This would show a list of cached pages/content
            alert('This feature would show available offline content. For now, try going to the home page to see cached jokes!');
        }

        // Initialize
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            if (!navigator.onLine) {
                // Try a lightweight request to check actual connectivity
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        // If this succeeds but navigator.onLine is false,
                        // the browser might not have detected the connection yet
                        if (!navigator.onLine) {
                            updateConnectionStatus();
                        }
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 5000);

        // Service Worker messaging
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'CONNECTIVITY_CHANGED') {
                    updateConnectionStatus();
                }
            });
        }
    </script>
</body>
</html>

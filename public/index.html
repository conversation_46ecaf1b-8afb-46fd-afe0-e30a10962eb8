<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover, user-scalable=no" />
    <meta name="theme-color" content="#10b981" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="format-detection" content="telephone=no" />
    <meta
      name="description"
      content="نكتة - منصة النكت الصوتية بالدارجة المغربية | Noukta - Plateforme de blagues audio en darija"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Arabic Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&family=Noto+Kufi+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    
    <!-- French Font -->
    <link 
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    
    <title>نكتة | Noukta</title>

    <!-- Force cache refresh -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- Preload Critical Assets -->
    <!-- Logo preload removed to prevent unused preload warning -->
    
    <!-- Meta Tags for Social Sharing -->
    <meta property="og:title" content="نكتة | Noukta" />
    <meta property="og:description" content="نكتة - منصة النكت الصوتية بالدارجة المغربية | Noukta - Plateforme de blagues audio en darija" />
    <meta property="og:image" content="%PUBLIC_URL%/logo512.png" />
    <meta property="og:url" content="https://noukta.app" />
    <meta name="twitter:card" content="summary_large_image" />
    
    <!-- iOS Specific -->
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash.png" />
    <meta name="apple-mobile-web-app-title" content="نكتة | Noukta" />
    
    <!-- PWA Assets -->
    <link rel="mask-icon" href="%PUBLIC_URL%/safari-pinned-tab.svg" color="#10b981" />
    <meta name="msapplication-TileColor" content="#10b981" />
    
    <!-- Prevent Phone Number Detection -->
    <meta name="format-detection" content="telephone=no" />
    
    <style>
      /* Prevent pull-to-refresh and overscroll on iOS */
      html {
        overflow: hidden;
        height: 100%;
        -webkit-overflow-scrolling: touch;
      }
      body {
        height: 100%;
        overflow: auto;
        overscroll-behavior-y: none;
      }
      /* Hide scrollbars while keeping functionality */
      ::-webkit-scrollbar {
        display: none;
      }
      
      /* Language-specific styles */
      [lang="ar"] {
        font-family: 'Noto Sans Arabic', 'Noto Kufi Arabic', system-ui, -apple-system, sans-serif;
      }
      
      [lang="fr"] {
        font-family: 'Poppins', 'Inter', system-ui, -apple-system, sans-serif;
      }
      
      /* Direction-specific margins */
      [dir="rtl"] .ml-auto {
        margin-left: 0;
        margin-right: auto;
      }
      
      [dir="ltr"] .mr-auto {
        margin-right: 0;
        margin-left: auto;
      }
    </style>
  <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=%REACT_APP_GA_MEASUREMENT_ID%"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', '%REACT_APP_GA_MEASUREMENT_ID%');
    </script>
  </head>
  <body class="safe-padding">
    <noscript>
      <div lang="ar">عذراً، يجب تفعيل JavaScript لتشغيل هذا التطبيق.</div>
      <div lang="fr">Désolé, JavaScript doit être activé pour exécuter cette application.</div>
    </noscript>
    <div id="root"></div>
  </body>
</html>

# Software Requirements Specification (SRD) - Noukta

## 1. Introduction

### 1.1. Purpose
This Software Requirements Specification (SRD) details the functional and non-functional requirements for the Noukta application. It serves as a technical blueprint for the development team, guiding the implementation, testing, and deployment phases.

### 1.2. Scope
The scope of this SRD covers the technical specifications required to build and deploy the Noukta web application, encompassing frontend, backend (Supabase), and infrastructure considerations. It aligns with the product vision outlined in the PRD.

### 1.3. Definitions, Acronyms, and Abbreviations
*   **MVP:** Minimum Viable Product
*   **PWA:** Progressive Web App
*   **RLS:** Row Level Security
*   **Supabase:** Backend-as-a-Service platform used for database, authentication, and storage.
*   **i18n:** Internationalization
*   **UI:** User Interface
*   **UX:** User Experience
*   **API:** Application Programming Interface

### 1.4. Recent Architecture Updates (2025-07-01)
The application has undergone significant architectural improvements to enhance performance, maintainability, and developer experience:

**✅ COMPLETED - Database Setup & Authentication (2025-07-01)**
- Database schema successfully deployed to production Supabase instance
- All tables, RLS policies, functions, and triggers created
- Authentication system working with user registration and sign-in
- Connection test component confirms all services operational
- Initial data populated: 10 categories and 15 achievements

#### 1.4.1. Authentication System Consolidation
- **Unified Auth Hook**: Merged duplicate authentication implementations into a single TypeScript-based `useAuth` hook
- **Improved Error Handling**: Consistent error states and retry mechanisms
- **Profile Management**: Automatic profile creation and onboarding flow integration

#### 1.4.2. Data Layer Refactoring
- **Service Layer**: Implemented centralized API services (`jokesService`, `reactionsService`) with caching
- **Optimistic Updates**: Real-time UI updates with automatic rollback on failure
- **Request Deduplication**: Prevents duplicate API calls and improves performance

#### 1.4.3. Performance Optimizations
- **Component Memoization**: Added `React.memo` to prevent unnecessary re-renders
- **Data Caching**: Implemented intelligent caching with TTL and invalidation strategies
- **Async Operation Management**: Standardized loading states, error handling, and retry logic

#### 1.4.4. Type Safety Improvements
- **Comprehensive Types**: Added TypeScript definitions for all data structures
- **API Response Types**: Standardized response formats across all services
- **Hook Type Safety**: Improved type inference and compile-time error detection

#### 1.4.5. Error Handling & Monitoring
- **Error Boundaries**: Global error catching with Sentry integration
- **Graceful Degradation**: Fallback UI components for error states
- **Development Tools**: Enhanced debugging information in development mode
*   **KPI:** Key Performance Indicator

### 1.4. References
*   `docs/PRD.md`: Product Requirements Document
*   `docs/ROADMAP.md`: Project Roadmap
*   `docs/COMPLETION_CHECKLIST.md`: Development Completion Checklist
*   `docs/EMOJI_SYSTEM.md`: Emoji Reaction System Specification

### 1.5. Overview
Noukta is an audio-centric social platform for sharing jokes. This document elaborates on the technical requirements for its various components, including user management, content handling, social features, and system-level considerations.

## 2. Overall Description

### 2.1. Product Perspective
Noukta is a standalone web application built with React on the frontend and Supabase as its backend. It integrates with browser APIs for audio recording and playback, and leverages Supabase's authentication, database, and storage services.

### 2.2. Product Functions
*   Allow users to register, log in, and manage their profiles.
*   Enable recording, uploading, and playback of audio jokes.
*   Provide real-time voice effects during recording.
*   Support social interactions: liking, commenting, reacting with emojis, following/unfollowing users, blocking users, and reporting content/users.
*   Display jokes in various feeds (home, top, recent, category-filtered).
*   Implement search functionality for jokes and users.
*   Track and display user achievements and seasonal rankings.
*   Support multiple languages for the user interface.

### 2.3. User Characteristics
*   **Content Creators:** Users who record and upload jokes. Require access to microphone and stable internet for uploads.
*   **Content Consumers:** Users who listen to jokes and interact with content. Require stable internet for streaming.
*   **Administrators:** Users with elevated privileges for content moderation and platform management.
*   **Super Administrators:** Platform owners with full system access and configuration capabilities.

### 2.4. Constraints
*   **Technology Stack:** React for frontend, Supabase for backend.
*   **Browser Compatibility:** Must function correctly on modern web browsers (Chrome, Firefox, Safari, Edge) and mobile equivalents.
*   **Audio Format:** Primary audio format for uploads and playback is WebM.
*   **Real-time Features:** Rely on Supabase Realtime for immediate updates.

### 2.5. Assumptions and Dependencies
*   **Assumptions:**
    *   Supabase services (Auth, Database, Storage, Realtime, Edge Functions) are available and performant.
    *   Users grant microphone access for recording.
    *   Client-side JavaScript is enabled.
*   **Dependencies:**
    *   `@supabase/supabase-js` for Supabase client interactions.
    *   `react-router-dom` for client-side routing.
    *   `i18next` and `react-i18next` for internationalization.
    *   `framer-motion` for animations.
    *   `@sentry/react` for error logging.

## 2.6. Updated Technical Architecture (2025-07-01)

### 2.6.1. Frontend Architecture Improvements

#### Service Layer Pattern
The application now implements a centralized service layer that abstracts data access and business logic:

```typescript
// Example: Jokes Service
export class JokesService {
  async getJokes(filters: JokeFilters): Promise<ApiResponse<Joke[]>>
  async createJoke(data: CreateJokeData): Promise<ApiResponse<Joke>>
  async toggleLike(jokeId: string, userId: string): Promise<ApiResponse<any>>
}
```

#### Unified State Management
- **Custom Store**: Lightweight state management without external dependencies
- **Optimistic Updates**: Immediate UI feedback with automatic rollback on errors
- **Persistent Preferences**: User settings cached in localStorage
- **Cache Management**: Intelligent data caching with TTL and invalidation

#### Enhanced Hook Architecture
```typescript
// Centralized data hooks with caching and error handling
const { jokes, loading, error, loadMore } = useJokes({
  filters: { category: 'comedy', limit: 20 },
  autoFetch: true,
  realtime: true
});

// Optimistic reaction management
const { reactions, toggleReaction } = useReactions(jokeId);
```

#### Error Handling Strategy
- **Error Boundaries**: Global error catching with graceful fallbacks
- **Retry Logic**: Automatic retry with exponential backoff
- **Sentry Integration**: Comprehensive error reporting and monitoring
- **User Feedback**: Clear error messages with actionable recovery options

### 2.6.2. Performance Optimizations

#### Component Optimization
- **React.memo**: Prevents unnecessary re-renders of expensive components
- **Callback Memoization**: Stable function references to prevent child re-renders
- **Lazy Loading**: Code splitting for route-based components

#### Data Layer Optimization
- **Request Deduplication**: Prevents duplicate API calls
- **Response Caching**: Intelligent caching with configurable TTL
- **Optimistic Updates**: Immediate UI updates with server reconciliation
- **Real-time Subscriptions**: Efficient WebSocket management

#### Bundle Optimization
- **Tree Shaking**: Eliminates unused code from production builds
- **Code Splitting**: Route-based and component-based splitting
- **Asset Optimization**: Compressed images and optimized audio formats

### 2.6.3. Type Safety Improvements

#### Comprehensive Type System
```typescript
// Centralized type definitions
export interface Joke {
  id: string;
  title: string;
  audio_url: string;
  user_id: string;
  // ... complete type definitions
}

// API response standardization
export interface ApiResponse<T> {
  data: T | null;
  error: any;
  count?: number;
}
```

#### Hook Type Safety
- **Generic Hooks**: Type-safe data fetching and state management
- **Inference**: Automatic type inference for better developer experience
- **Compile-time Validation**: Catch type errors during development

## 3. Specific Requirements

### 3.1. Functional Requirements

#### 3.1.1. User Authentication
*   **REQ-AUTH-001:** The system SHALL allow users to register with email and password.
*   **REQ-AUTH-002:** The system SHALL allow users to log in with email and password.
*   **REQ-AUTH-003:** The system SHALL support social login (e.g., Google, Facebook - *future consideration*).
*   **REQ-AUTH-004:** The system SHALL send email verification upon registration.
*   **REQ-AUTH-005:** The system SHALL redirect unonboarded users to an onboarding flow upon first login.
*   **REQ-AUTH-006:** The system SHALL allow users to update their profile information (username, bio, avatar).

#### 3.1.2. Joke Management
*   **REQ-JOKE-001:** The system SHALL allow authenticated users to record audio jokes.
*   **REQ-JOKE-002:** The system SHALL allow users to apply voice effects to their recordings.
*   **REQ-JOKE-003:** The system SHALL store audio jokes in Supabase Storage.
*   **REQ-JOKE-004:** The system SHALL display jokes with associated metadata (title, user, likes, comments, reactions).
*   **REQ-JOKE-005:** The system SHALL provide a custom audio player for joke playback.
*   **REQ-JOKE-006:** The system SHALL allow users to search for jokes by keywords.
*   **REQ-JOKE-007:** The system SHALL allow users to filter jokes by categories.

#### 3.1.3. Social Interactions
*   **REQ-SOCIAL-001:** The system SHALL allow users to like/unlike jokes.
*   **REQ-SOCIAL-002:** The system SHALL allow users to react to jokes with emojis.
*   **REQ-SOCIAL-003:** The system SHALL display real-time reaction counts for jokes.
*   **REQ-SOCIAL-004:** The system SHALL allow users to follow/unfollow other users.
*   **REQ-SOCIAL-005:** The system SHALL display follower/following counts on user profiles.
*   **REQ-SOCIAL-006:** The system SHALL allow users to add, edit, and delete comments on jokes.
*   **REQ-SOCIAL-007:** The system SHALL display comments in a threaded view (if `parent_id` is used).
*   **REQ-SOCIAL-008:** The system SHALL display seasonal rankings and a global leaderboard.
*   **REQ-SOCIAL-009:** The system SHALL allow users to earn and display achievements.

#### 3.1.4. UI/UX
*   **REQ-UI-001:** The system SHALL provide a responsive user interface for mobile and desktop.
*   **REQ-UI-002:** The system SHALL implement a bottom navigation bar for primary mobile navigation.
*   **REQ-UI-003:** The system SHALL display loading indicators during data fetching and processing.
*   **REQ-UI-004:** The system SHALL display clear messages for empty states (e.g., no jokes, no followers).
*   **REQ-UI-005:** The system SHALL provide an onboarding flow for new users.
*   **REQ-UI-006:** The system SHALL prompt users to complete their profile if incomplete.

#### 3.1.5. Internationalization
*   **REQ-I18N-001:** The system SHALL support Arabic and French languages.
*   **REQ-I18N-002:** The system SHALL dynamically switch between RTL and LTR layouts based on selected language.

#### 3.1.6. Content Moderation
*   **REQ-MOD-001:** The system SHALL allow users to report inappropriate jokes.
*   **REQ-MOD-002:** The system SHALL allow users to report other users.
*   **REQ-MOD-003:** The system SHALL allow users to block other users.

#### 3.1.7. Analytics & Monitoring
*   **REQ-ANALYTICS-001:** The system SHALL integrate with Google Analytics for tracking user behavior.
*   **REQ-ANALYTICS-002:** The system SHALL integrate with Sentry for error logging and performance monitoring.

### 3.2. Non-Functional Requirements

#### 3.2.1. Performance
*   **NFR-PERF-001:** Initial page load time SHALL be under 3 seconds on a typical broadband connection.
*   **NFR-PERF-002:** Audio playback SHALL start within 1 second of user interaction.
*   **NFR-PERF-003:** Joke upload time SHALL be optimized for typical audio file sizes (e.g., under 5 seconds for a 30-second joke).
*   **NFR-PERF-004:** UI interactions (e.g., liking, reacting, following) SHALL feel instantaneous with optimistic updates.
*   **NFR-PERF-005:** Data caching SHALL reduce redundant API calls by at least 60%.
*   **NFR-PERF-006:** Component re-renders SHALL be minimized through memoization strategies.
*   **NFR-PERF-007:** Error recovery SHALL be automatic with exponential backoff retry logic.

#### 3.2.2. Security
*   **NFR-SEC-001:** All sensitive data (passwords, API keys) SHALL be stored securely and not exposed client-side.
*   **NFR-SEC-002:** Supabase Row Level Security (RLS) SHALL be correctly implemented for all database tables.
*   **NFR-SEC-003:** User authentication SHALL be handled securely via Supabase Auth.
*   **NFR-SEC-004:** All communication between client and server SHALL be encrypted (HTTPS).

#### 3.2.3. Scalability
*   **NFR-SCAL-001:** The system SHALL be able to handle a growing number of users and jokes without significant performance degradation.
*   **NFR-SCAL-002:** The database schema and indexing SHALL support efficient querying for large datasets.

#### 3.2.4. Maintainability
*   **NFR-MAINT-001:** The codebase SHALL adhere to established coding standards (ESLint, Prettier).
*   **NFR-MAINT-002:** The project SHALL have clear documentation for setup, development, and deployment.
*   **NFR-MAINT-003:** Dependencies SHALL be kept up-to-date to minimize security vulnerabilities and leverage new features.
*   **NFR-MAINT-004:** TypeScript SHALL be used for type safety and better developer experience.
*   **NFR-MAINT-005:** Service layer SHALL abstract business logic from UI components.
*   **NFR-MAINT-006:** Error boundaries SHALL provide graceful error handling and recovery.
*   **NFR-MAINT-007:** Code SHALL be organized in logical modules with clear separation of concerns.

#### 3.2.5. Usability
*   **NFR-USAB-001:** The user interface SHALL be intuitive and easy to navigate for first-time users.
*   **NFR-USAB-002:** Error messages SHALL be clear, concise, and actionable.
*   **NFR-USAB-003:** The application SHALL provide consistent feedback for user actions.

#### 3.2.6. Compatibility
*   **NFR-COMP-001:** The application SHALL be compatible with the latest stable versions of Chrome, Firefox, Safari, and Edge browsers.
*   **NFR-COMP-002:** The application SHALL be fully functional on iOS and Android mobile devices.

### 3.8. Super Admin Management

#### 3.8.1. Admin Authentication & Authorization
*   **REQ-ADMIN-001:** The system SHALL provide a separate admin authentication flow with elevated privileges.
*   **REQ-ADMIN-002:** The system SHALL implement role-based access control (RBAC) with roles: `user`, `admin`, `super_admin`.
*   **REQ-ADMIN-003:** The system SHALL restrict admin panel access to users with `admin` or `super_admin` roles only.
*   **REQ-ADMIN-004:** The system SHALL log all admin actions for audit purposes.

#### 3.8.2. User Management
*   **REQ-ADMIN-005:** Super admins SHALL be able to view all user profiles with detailed information.
*   **REQ-ADMIN-006:** Super admins SHALL be able to suspend/unsuspend user accounts.
*   **REQ-ADMIN-007:** Super admins SHALL be able to delete user accounts and associated content.
*   **REQ-ADMIN-008:** Super admins SHALL be able to promote users to admin role.
*   **REQ-ADMIN-009:** Super admins SHALL be able to view user activity logs and statistics.

#### 3.8.3. Content Moderation
*   **REQ-ADMIN-010:** Admins SHALL be able to view all reported content in a centralized dashboard.
*   **REQ-ADMIN-011:** Admins SHALL be able to approve, reject, or escalate reported content.
*   **REQ-ADMIN-012:** Admins SHALL be able to delete inappropriate jokes and comments.
*   **REQ-ADMIN-013:** Admins SHALL be able to add content warnings or age restrictions to jokes.
*   **REQ-ADMIN-014:** The system SHALL automatically flag content based on configurable keywords and patterns.

#### 3.8.4. Platform Configuration
*   **REQ-ADMIN-015:** Super admins SHALL be able to configure global app settings (maintenance mode, feature flags).
*   **REQ-ADMIN-016:** Super admins SHALL be able to manage joke categories (create, edit, delete).
*   **REQ-ADMIN-017:** Super admins SHALL be able to configure achievement criteria and rewards.
*   **REQ-ADMIN-018:** Super admins SHALL be able to manage seasonal rankings and competitions.
*   **REQ-ADMIN-019:** Super admins SHALL be able to configure notification templates and settings.

#### 3.8.5. Analytics & Reporting
*   **REQ-ADMIN-020:** The admin panel SHALL provide comprehensive analytics dashboard with:
    - User growth metrics
    - Content engagement statistics
    - Platform usage patterns
    - Revenue metrics (if applicable)
*   **REQ-ADMIN-021:** Super admins SHALL be able to export data reports in CSV/JSON formats.
*   **REQ-ADMIN-022:** The system SHALL provide real-time monitoring of platform health and performance.

#### 3.8.6. System Maintenance
*   **REQ-ADMIN-023:** Super admins SHALL be able to enable/disable maintenance mode with custom messages.
*   **REQ-ADMIN-024:** Super admins SHALL be able to manage database backups and restoration.
*   **REQ-ADMIN-025:** Super admins SHALL be able to view and manage system logs and error reports.
*   **REQ-ADMIN-026:** The system SHALL provide automated alerts for critical system issues.

## 4. Data Model (High-Level)

*   **`profiles`:** `id` (UUID, PK), `username` (text, unique), `avatar_url` (text), `bio` (text), `role` (enum: 'user', 'admin', 'super_admin'), `is_suspended` (boolean), `created_at` (timestamptz), `notification_settings` (JSONB), `onboarded` (boolean).
*   **`jokes`:** `id` (UUID, PK), `user_id` (UUID, FK to `profiles`), `title` (text), `content` (text), `audio_url` (text), `voice_effect` (text), `is_private` (boolean), `created_at` (timestamptz), `likes_count` (int), `plays_count` (int), `comments_count` (int).
*   **`emoji_reactions`:** `id` (UUID, PK), `user_id` (UUID, FK to `profiles`), `joke_id` (UUID, FK to `jokes`), `emoji` (text), `created_at` (timestamptz).
*   **`follows`:** `follower_id` (UUID, FK to `profiles`), `following_id` (UUID, FK to `profiles`), `created_at` (timestamptz), PK (`follower_id`, `following_id`).
*   **`comments`:** `id` (UUID, PK), `joke_id` (UUID, FK to `jokes`), `user_id` (UUID, FK to `profiles`), `content` (text), `parent_id` (UUID, FK to `comments`), `is_edited` (boolean), `created_at` (timestamptz).
*   **`categories`:** `id` (UUID, PK), `name` (text), `slug` (text, unique), `created_at` (timestamptz).
*   **`joke_categories`:** `joke_id` (UUID, FK to `jokes`), `category_id` (UUID, FK to `categories`), PK (`joke_id`, `category_id`).
*   **`achievements`:** `id` (UUID, PK), `title` (text), `description` (text), `icon` (text), `badge_color` (text), `category` (text), `requirement` (int), `xp_reward` (int), `created_at` (timestamptz).
*   **`user_achievements`:** `user_id` (UUID, FK to `profiles`), `achievement_id` (UUID, FK to `achievements`), `progress` (int), `unlocked_at` (timestamptz), PK (`user_id`, `achievement_id`).
*   **`seasonal_rankings`:** `id` (UUID, PK), `season_id` (text), `user_id` (UUID, FK to `profiles`), `points` (int), `rank` (text), `updated_at` (timestamptz).
*   **`seasons`:** `id` (text, PK), `start_date` (timestamptz), `end_date` (timestamptz).
*   **`reports`:** `id` (UUID, PK), `reporter_id` (UUID, FK to `profiles`), `reported_item_id` (UUID), `report_type` (enum: 'joke', 'user', 'comment'), `reason` (text), `status` (text), `created_at` (timestamptz).
*   **`user_blocks`:** `blocker_id` (UUID, FK to `profiles`), `blocked_id` (UUID, FK to `profiles`), `created_at` (timestamptz), PK (`blocker_id`, `blocked_id`).
*   **`joke_likes`:** `user_id` (UUID, FK to `profiles`), `joke_id` (UUID, FK to `jokes`), `created_at` (timestamptz), PK (`user_id`, `joke_id`).
*   **`translations`:** `id` (UUID, PK), `language` (text), `key` (text), `value` (text), `created_at` (timestamptz), `updated_at` (timestamptz), UNIQUE (`language`, `key`).
*   **`admin_actions`:** `id` (UUID, PK), `admin_id` (UUID, FK to `profiles`), `action_type` (text), `target_type` (text), `target_id` (UUID), `details` (JSONB), `created_at` (timestamptz).
*   **`app_settings`:** `key` (text, PK), `value` (JSONB), `description` (text), `updated_by` (UUID, FK to `profiles`), `updated_at` (timestamptz).
*   **`feature_flags`:** `id` (UUID, PK), `name` (text, unique), `enabled` (boolean), `description` (text), `target_users` (JSONB), `created_at` (timestamptz), `updated_at` (timestamptz).
*   **`content_warnings`:** `id` (UUID, PK), `joke_id` (UUID, FK to `jokes`), `warning_type` (text), `reason` (text), `added_by` (UUID, FK to `profiles`), `created_at` (timestamptz).

## 5. System Architecture (High-Level)

*   **Frontend:** React application served via a static site host (e.g., GitHub Pages).
*   **Backend:** Supabase (PostgreSQL database, Authentication, Storage, Realtime).
*   **API Interaction:** Direct client-side interaction with Supabase API via `@supabase/supabase-js`.
*   **Deployment:** GitHub Actions for CI/CD to GitHub Pages.
*   **Monitoring:** Sentry for error tracking and performance monitoring; Google Analytics for user behavior analytics.

## 6. Remaining Tasks

This section directly references the `docs/COMPLETION_CHECKLIST.md` for a detailed breakdown of remaining development tasks. As of the completion of this SRD, all tasks in the `COMPLETION_CHECKLIST.md` are marked as complete, indicating the project is ready for production deployment.

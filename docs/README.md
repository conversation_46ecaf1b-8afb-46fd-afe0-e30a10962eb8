# Noukta (نكتة) - Audio Joke Sharing Platform

## Overview
Noukta is a React-based web application that allows users to share and listen to audio jokes in Arabic (specifically Moroccan Darija). The platform features voice effects, reactions, and social interactions, creating a unique audio-first social experience.

## Tech Stack
- React 18 (Frontend Framework)
- Tailwind CSS (Styling)
- Supabase (Backend & Authentication)
- i18next (Internationalization)
- Web Audio API (Audio Processing)
- React Router v6 (Routing)
- Framer Motion (Animations)
- Jest & React Testing Library (Testing)

## Project Architecture

### Frontend Architecture
The application follows a modern React architecture with the following key patterns:
- Context API for state management (Auth, Theme)
- Custom hooks for reusable logic
- Lazy loading and code splitting
- Component-based architecture
- Real-time subscriptions with Supabase

### Backend Architecture (Supabase)
- Authentication system
- Real-time database
- Storage for audio files
- Row Level Security (RLS)
- Automated content moderation

## Project Structure

### `/components`
- **audio/**
  - `AudioPlayer.js`: Custom audio player with voice effect support
  - `AudioRecorder.js`: Recording interface with real-time voice effects
- **auth/**
  - `SocialLogin.js`: Social authentication components
- **common/**
  - `ShareButton.js`: Social sharing functionality
- **layout/**
  - `Header.js`: App header with navigation and auth controls
  - `Footer.js`: App footer with additional links
  - `Navigation.js`: Main navigation component with mobile support
- **reactions/**
  - `ReactionStats.js`: Analytics for user reactions
  - `VoiceReactions.js`: Voice-based reaction system
- **achievements/**
  - `AchievementSystem.js`: User achievement and progression system

### `/pages`
- `Home.js`: Landing page with featured content
- `Record.js`: Audio recording studio with effects
- `TopJokes.js`: Trending and popular jokes
- `RecentJokes.js`: Latest uploaded content
- `Profile.js`: User profiles and statistics
- `Search.js`: Content search functionality
- `Challenges.js`: Community challenges and competitions

### `/hooks`
- `useAudioRecorder.js`: Audio recording management
- `useVoiceEffect.js`: Real-time voice effect processing

### `/utils`
- `sounds.js`: Sound effect utilities

### `/lib`
- `supabase.js`: Database configuration and helpers

### `/locales`
- `ar.js`: Arabic (Darija) translations

## Implemented Features

### Audio Management
- High-quality audio recording
- Real-time voice effects:
  - Normal (Original)
  - Deep Voice
  - High Pitch
  - Robot Effect
  - Echo
- Custom audio player with effect support
- Sound feedback system for interactions

### User Experience
- Progressive Web App (PWA) support
- Responsive design
- RTL support for Arabic
- Infinite scroll for content
- Real-time updates
- Loading states and animations
- Search with debouncing
- Category filtering

### Social Features
- User profiles with statistics
- Voice-based reactions
- Social sharing
- Achievement system with badges:
  - Rookie Joker (10 jokes)
  - Master Storyteller (50 jokes)
  - Comedy Legend (1000 jokes)
  - Trendsetter (3 trending jokes)
- Community challenges

### Content Management
- Automated content moderation
- Category tagging
- Trending content algorithm
- Search functionality
- Content sorting options:
  - Latest
  - Most Popular
  - Most Discussed

### Authentication
- Social login providers
- Session management
- Protected routes
- User verification

## Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/noukta.git
cd noukta
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:

Create a `.env` file in the root directory and add your Supabase credentials:
```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Run Supabase Migrations:

Ensure your local Supabase environment is set up and then run:
```bash
supabase db push
```

5. Start development server:

```bash
npm run dev
```

### Available Scripts
- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run test`: Run test suite
- `npm run eject`: Eject from Create React App
- `npm run check-mvp`: Check MVP progress
- `npm run create-season`: Manually create a new season (for rankings)

## Development

### Code Style
- ESLint configuration for consistent code style
- Prettier for automatic code formatting
- TypeScript for type safety

### Testing
- Jest for unit testing
- React Testing Library for component testing
- Coverage reports in `/coverage`

### Build Process
- Optimized production builds
- Asset optimization
- Code splitting
- PWA configuration

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Android Chrome)

## Deployment

The project can be deployed to GitHub Pages using the provided GitHub Actions workflow. 

1.  **Configure GitHub Secrets:** Add the following secrets to your GitHub repository settings (`Settings > Secrets > Actions`):
    *   `REACT_APP_SUPABASE_URL`
    *   `REACT_APP_SUPABASE_ANON_KEY`
    *   `REACT_APP_SENTRY_DSN` (if using Sentry)
    *   `REACT_APP_GA_MEASUREMENT_ID` (if using Google Analytics)

2.  **Custom Domain (Optional):** If you wish to use a custom domain (e.g., `noukta.com`), ensure your `CNAME` file is correctly configured in the `public` directory and your DNS settings point to GitHub Pages.

3.  **Trigger Deployment:** Pushing to the `main` branch will automatically trigger the deployment workflow. You can also manually trigger it from the GitHub Actions tab.

## Monitoring & Analytics

*   **Error Logging:** Integrated with Sentry for robust error tracking in production.
*   **Analytics:** Integrated with Google Analytics to track user behavior and key performance indicators.
*   **Uptime Monitoring:** It is recommended to set up an external uptime monitoring service (e.g., UptimeRobot, Pingdom) to receive alerts if the production site experiences downtime.

## Contributing
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact
For questions and support, please [open an issue](https://github.com/yourusername/noukta/issues) in the repository.

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const TABLES_TO_CHECK = [
  'profiles',
  'jokes',
  'user_achievements',
  'seasonal_rankings',
  'community_spotlights',
];

// Map of frontend model fields (from src/types/supabase.ts and codebase knowledge)
const frontendModels = {
  profiles: [
    'id', 'username', 'avatar_url', 'bio', 'created_at', 'updated_at', 'notification_settings', 'badges', 'following_count', 'followers_count', 'jokes_count'
  ],
  jokes: [
    'id', 'title', 'audio_url', 'user_id', 'category_id', 'created_at', 'updated_at', 'likes_count', 'plays_count', 'reports_count', 'is_approved', 'is_private', 'transcription', 'age_restriction', 'content', 'voice_effect', 'comments_count'
  ],
  user_achievements: [
    'user_id', 'achievement_id', 'progress', 'unlocked_at', 'created_at'
  ],
  seasonal_rankings: [
    'id', 'season_id', 'user_id', 'points', 'rank', 'updated_at'
  ],
  community_spotlights: [
    'id', 'user_id', 'category', 'start_date', 'end_date', 'metrics', 'featured_joke_id', 'created_at'
  ]
};

async function getTableColumns(client, table) {
  const res = await client.query(`
    SELECT column_name FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = $1
    ORDER BY ordinal_position
  `, [table]);
  return res.rows.map(r => r.column_name);
}

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || process.env.SUPABASE_DB_URL || process.env.NEXT_PUBLIC_SUPABASE_DB_URL
  });
  await client.connect();

  let hasIssues = false;

  for (const table of TABLES_TO_CHECK) {
    const dbColumns = await getTableColumns(client, table);
    const frontendFields = frontendModels[table] || [];
    const missingInDb = frontendFields.filter(f => !dbColumns.includes(f));
    const extraInDb = dbColumns.filter(c => !frontendFields.includes(c));
    console.log(`\n=== ${table} ===`);
    console.log('DB Columns:', dbColumns);
    console.log('Frontend Model:', frontendFields);
    if (missingInDb.length > 0) {
      hasIssues = true;
      console.warn('❌ Missing in DB:', missingInDb);
    }
    if (extraInDb.length > 0) {
      hasIssues = true;
      console.warn('⚠️ Extra in DB (not in frontend model):', extraInDb);
    }
    if (missingInDb.length === 0 && extraInDb.length === 0) {
      console.log('✅ Schema matches frontend model.');
    }
  }

  await client.end();
  if (hasIssues) {
    console.log('\nSchema audit completed with issues. Please review the warnings above.');
  } else {
    console.log('\nSchema audit completed. All checked tables match the frontend models.');
  }
}

if (require.main === module) {
  main().catch(err => {
    console.error('Error during schema audit:', err);
    process.exit(1);
  });
} 
-- Noukta Storage Setup
-- Run this to set up storage buckets and policies

-- Create storage buckets (if they don't exist)
INSERT INTO storage.buckets (id, name, public)
VALUES 
  ('jokes', 'jokes', true),
  ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for jokes bucket
CREATE POLICY "Anyone can view jokes" ON storage.objects
  FOR SELECT USING (bucket_id = 'jokes');

CREATE POLICY "Authenticated users can upload jokes" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'jokes' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can update their own jokes" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'jokes' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can delete their own jokes" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'jokes' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

-- Storage policies for avatars bucket
CREATE POLICY "Anyone can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can update their own avatars" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "Users can delete their own avatars" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'avatars' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

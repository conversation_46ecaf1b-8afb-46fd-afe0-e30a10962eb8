-- Fix follows table check constraint
-- The issue is likely that the check constraint prevents users from following themselves

-- First, let's see what the constraint is
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'public.follows'::regclass 
AND contype = 'c';

-- Drop the problematic check constraint
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_check;

-- Recreate the constraint with proper logic
ALTER TABLE public.follows 
ADD CONSTRAINT follows_check 
CHECK (follower_id != following_id);

-- Also ensure we have the right foreign key constraints
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_follower_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_following_id_fkey;

-- Add foreign key constraints to profiles table (not auth.users)
ALTER TABLE public.follows 
ADD CONSTRAINT follows_follower_id_fkey 
FOREIGN KEY (follower_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.follows 
ADD CONSTRAINT follows_following_id_fkey 
FOREIGN KEY (following_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';

-- Log completion
DO $$ 
BEGIN
    RAISE NOTICE 'Follows table constraints fixed.';
END $$;

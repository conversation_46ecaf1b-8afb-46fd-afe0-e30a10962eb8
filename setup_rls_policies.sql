-- Noukta RLS Policies Setup
-- Run this AFTER setup_database.sql to create all Row Level Security policies

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles 
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

-- Jokes policies
CREATE POLICY "Public jokes are viewable by everyone" ON public.jokes 
    FOR SELECT USING (NOT is_private OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own jokes" ON public.jokes 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own jokes" ON public.jokes 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own jokes" ON public.jokes 
    FOR DELETE USING (auth.uid() = user_id);

-- Categories policies (public read, admin write)
CREATE POLICY "Categories are viewable by everyone" ON public.categories 
    FOR SELECT USING (true);

-- Joke categories policies
CREATE POLICY "Joke categories are viewable by everyone" ON public.joke_categories 
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their joke categories" ON public.joke_categories 
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.jokes 
            WHERE jokes.id = joke_categories.joke_id 
            AND jokes.user_id = auth.uid()
        )
    );

-- Emoji reactions policies
CREATE POLICY "Reactions are viewable by everyone" ON public.emoji_reactions 
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own reactions" ON public.emoji_reactions 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reactions" ON public.emoji_reactions 
    FOR DELETE USING (auth.uid() = user_id);

-- Comments policies
CREATE POLICY "Comments are viewable by everyone" ON public.comments 
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own comments" ON public.comments 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON public.comments 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON public.comments 
    FOR DELETE USING (auth.uid() = user_id);

-- Follows policies
CREATE POLICY "Follows are viewable by everyone" ON public.follows 
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own follows" ON public.follows 
    FOR ALL USING (auth.uid() = follower_id);

-- Joke likes policies
CREATE POLICY "Likes are viewable by everyone" ON public.joke_likes 
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own likes" ON public.joke_likes 
    FOR ALL USING (auth.uid() = user_id);

-- Achievements policies
CREATE POLICY "Achievements are viewable by everyone" ON public.achievements 
    FOR SELECT USING (true);

-- User achievements policies
CREATE POLICY "User achievements are viewable by everyone" ON public.user_achievements 
    FOR SELECT USING (true);

CREATE POLICY "Users can view their own achievements" ON public.user_achievements 
    FOR ALL USING (auth.uid() = user_id);

-- Reports policies
CREATE POLICY "Users can insert reports" ON public.reports 
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

CREATE POLICY "Users can view their own reports" ON public.reports 
    FOR SELECT USING (auth.uid() = reporter_id);

-- User blocks policies
CREATE POLICY "Users can manage their own blocks" ON public.user_blocks 
    FOR ALL USING (auth.uid() = blocker_id);

-- Seasonal rankings policies
CREATE POLICY "Rankings are viewable by everyone" ON public.seasonal_rankings 
    FOR SELECT USING (true);

CREATE POLICY "Users can view their own rankings" ON public.seasonal_rankings 
    FOR ALL USING (auth.uid() = user_id);

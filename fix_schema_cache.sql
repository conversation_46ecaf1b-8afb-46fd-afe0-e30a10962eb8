-- Fix Schema Cache Issues
-- Run this to refresh PostgREST schema cache and fix relationship issues

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

-- Verify foreign key constraints exist
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name = 'jokes';

-- Ensure the foreign key constraint exists (recreate if needed)
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'jokes_user_id_fkey' 
        AND table_name = 'jokes'
    ) THEN
        ALTER TABLE public.jokes DROP CONSTRAINT jokes_user_id_fkey;
    END IF;
    
    -- Recreate the constraint
    ALTER TABLE public.jokes 
    ADD CONSTRAINT jokes_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
END $$;

-- Refresh schema cache again
NOTIFY pgrst, 'reload schema';

# Noukta Database Setup Guide

## 🚀 Quick Setup

To fix the "relation 'public.profiles' does not exist" error, you need to run the database setup scripts in your Supabase instance.

### Step 1: Access Supabase SQL Editor

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `drgojtmiplmzikzfejdc`
3. Navigate to **SQL Editor** in the left sidebar

### Step 2: Run Setup Scripts (In Order)

Execute these SQL files **in the exact order** listed below:

#### 1. Create Tables and Schema
```sql
-- Copy and paste the content of setup_database.sql
-- This creates all tables with proper relationships
```

#### 2. Set Up Row Level Security Policies
```sql
-- Copy and paste the content of setup_rls_policies.sql
-- This creates security policies for data access
```

#### 3. Create Database Functions
```sql
-- Copy and paste the content of setup_functions.sql
-- This creates stored procedures and triggers
```

#### 4. Add Initial Data
```sql
-- Copy and paste the content of setup_initial_data.sql
-- This adds categories, achievements, and indexes
```

### Step 3: Verify Setup

After running all scripts, test the connection using the Connection Test component in the app:

1. Go to `/auth` page in your app
2. Scroll down to "Supabase Connection Test"
3. Click "Test Connection"
4. Should show "All services connected successfully!"

## 📋 What Gets Created

### Core Tables
- **profiles** - User profile information
- **jokes** - Audio jokes with metadata
- **categories** - Joke categories
- **joke_categories** - Many-to-many relationship
- **emoji_reactions** - Emoji reactions to jokes
- **comments** - Comments on jokes
- **follows** - User follow relationships
- **joke_likes** - Like relationships
- **achievements** - Achievement definitions
- **user_achievements** - User achievement progress
- **reports** - Content reporting system
- **user_blocks** - User blocking system
- **seasonal_rankings** - Leaderboard data

### Security Features
- **Row Level Security (RLS)** enabled on all tables
- **Policies** for secure data access
- **User authentication** integration
- **Data isolation** between users

### Performance Features
- **Indexes** on frequently queried columns
- **Full-text search** for jokes (Arabic support)
- **Triggers** for automatic count updates
- **Functions** for complex operations

### Default Data
- **10 Categories** (Comedy, Family, Work, etc.)
- **15 Achievements** with XP rewards
- **Search optimization** for Arabic content

## 🔧 Troubleshooting

### If you get permission errors:
1. Make sure you're logged in as the project owner
2. Check that RLS policies are correctly applied
3. Verify the auth.users table exists

### If functions fail:
1. Ensure you have the `uuid-ossp` extension enabled
2. Check that all tables are created before running functions
3. Verify trigger creation was successful

### If search doesn't work:
1. Make sure the search vector column is populated
2. Check that the Arabic text search configuration is available
3. Verify the search indexes are created

## 🎯 Next Steps

After successful setup:

1. **Test Authentication** - Try signing up/signing in
2. **Test Core Features** - Create jokes, add reactions
3. **Verify Search** - Test joke search functionality
4. **Check Performance** - Monitor query performance

## 📝 Manual Verification Queries

Run these in SQL Editor to verify setup:

```sql
-- Check if all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check categories
SELECT * FROM public.categories;

-- Check achievements
SELECT * FROM public.achievements;
```

## 🚨 Important Notes

- **Run scripts in order** - Dependencies matter
- **Don't skip RLS setup** - Security is crucial
- **Test thoroughly** - Verify each step works
- **Backup first** - If you have existing data

The database setup is now complete and your app should work properly!

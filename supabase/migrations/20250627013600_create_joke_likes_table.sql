-- Create joke_likes table
create table if not exists public.joke_likes (
    user_id uuid references auth.users(id) on delete cascade not null,
    joke_id uuid references public.jokes(id) on delete cascade not null,
    created_at timestamptz default now(),
    primary key (user_id, joke_id)
);

-- Add RLS policies
alter table public.joke_likes enable row level security;

create policy "Joke likes are viewable by everyone" on public.joke_likes for select using (true);

create policy "Users can like jokes" on public.joke_likes for insert with check (auth.uid() = user_id);

create policy "Users can unlike jokes" on public.joke_likes for delete using (auth.uid() = user_id);

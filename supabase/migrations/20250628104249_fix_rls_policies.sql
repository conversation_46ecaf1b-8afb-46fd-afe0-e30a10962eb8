-- Drop existing policies
DROP POLICY "Users can insert their own jokes" ON public.jokes;
DROP POLICY "Users can update their own jokes" ON public.jokes;
DROP POLICY "Users can delete their own jokes" ON public.jokes;
DROP POLICY "Users can update their own profile" ON public.profiles;

-- Recreate policies with explicit casting
create policy "Users can insert their own jokes" on public.jokes for insert with check (auth.uid() = user_id);

create policy "Users can update their own jokes" on public.jokes for update using (auth.uid() = user_id);

create policy "Users can delete their own jokes" on public.jokes for delete using (auth.uid() = user_id);

create policy "Users can update their own profile" on public.profiles for update using (auth.uid() = id);
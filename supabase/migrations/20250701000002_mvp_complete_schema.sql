-- MVP Complete Schema Update
-- This migration adds all missing features for MVP launch
-- Includes: Admin roles, Enhanced reports, Content warnings, App settings, Feature flags

-- =====================================================
-- 1. UPDATE PROFILES TABLE FOR ADMIN ROLES
-- =====================================================

-- Add role column to profiles table
DO $$ 
BEGIN
    -- Create user role enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'admin', 'super_admin');
    END IF;
    
    -- Add role column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
        ALTER TABLE public.profiles ADD COLUMN role user_role DEFAULT 'user';
    END IF;
    
    -- Add is_suspended column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'is_suspended') THEN
        ALTER TABLE public.profiles ADD COLUMN is_suspended boolean DEFAULT false;
    END IF;
    
    -- Add onboarded column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'onboarded') THEN
        ALTER TABLE public.profiles ADD COLUMN onboarded boolean DEFAULT false;
    END IF;
END $$;

-- Create index on role for performance
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);
CREATE INDEX IF NOT EXISTS profiles_is_suspended_idx ON public.profiles(is_suspended);

-- =====================================================
-- 2. ENHANCE REPORTS SYSTEM
-- =====================================================

-- Update reports table with enhanced features
DO $$
BEGIN
    -- Add missing columns to reports table if they don't exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reports') THEN
        -- Add description column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reports' AND column_name = 'description') THEN
            ALTER TABLE public.reports ADD COLUMN description text;
        END IF;
        
        -- Add admin_notes column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reports' AND column_name = 'admin_notes') THEN
            ALTER TABLE public.reports ADD COLUMN admin_notes text;
        END IF;
        
        -- Add reviewed_by column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reports' AND column_name = 'reviewed_by') THEN
            ALTER TABLE public.reports ADD COLUMN reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL;
        END IF;
        
        -- Add reviewed_at column if missing
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'reports' AND column_name = 'reviewed_at') THEN
            ALTER TABLE public.reports ADD COLUMN reviewed_at timestamptz;
        END IF;
    END IF;
END $$;

-- =====================================================
-- 3. CREATE ADMIN MANAGEMENT TABLES
-- =====================================================

-- Create app_settings table for global configuration
CREATE TABLE IF NOT EXISTS public.app_settings (
    key text PRIMARY KEY,
    value jsonb NOT NULL,
    description text,
    updated_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_at timestamptz DEFAULT now()
);

-- Create feature_flags table for feature toggles
CREATE TABLE IF NOT EXISTS public.feature_flags (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    name text UNIQUE NOT NULL,
    enabled boolean DEFAULT false,
    description text,
    target_users jsonb DEFAULT '[]'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create content_warnings table
CREATE TABLE IF NOT EXISTS public.content_warnings (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE NOT NULL,
    warning_type text NOT NULL,
    reason text,
    added_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at timestamptz DEFAULT now()
);

-- Create admin_actions table for audit logging
CREATE TABLE IF NOT EXISTS public.admin_actions (
    id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    action_type text NOT NULL,
    target_type text,
    target_id uuid,
    details jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now()
);

-- =====================================================
-- 4. ADD MISSING INDEXES FOR PERFORMANCE
-- =====================================================

-- Reports table indexes
CREATE INDEX IF NOT EXISTS reports_status_created_at_idx ON public.reports(status, created_at DESC);
CREATE INDEX IF NOT EXISTS reports_report_type_idx ON public.reports(report_type);
CREATE INDEX IF NOT EXISTS reports_reviewed_by_idx ON public.reports(reviewed_by);

-- Content warnings indexes
CREATE INDEX IF NOT EXISTS content_warnings_joke_id_idx ON public.content_warnings(joke_id);
CREATE INDEX IF NOT EXISTS content_warnings_warning_type_idx ON public.content_warnings(warning_type);

-- Admin actions indexes
CREATE INDEX IF NOT EXISTS admin_actions_admin_id_idx ON public.admin_actions(admin_id);
CREATE INDEX IF NOT EXISTS admin_actions_created_at_idx ON public.admin_actions(created_at DESC);
CREATE INDEX IF NOT EXISTS admin_actions_action_type_idx ON public.admin_actions(action_type);

-- Feature flags indexes
CREATE INDEX IF NOT EXISTS feature_flags_enabled_idx ON public.feature_flags(enabled);

-- =====================================================
-- 5. ENABLE RLS ON NEW TABLES
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_warnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_actions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. CREATE RLS POLICIES
-- =====================================================

-- App Settings Policies (Super Admin only)
CREATE POLICY "Super admins can manage app settings" ON public.app_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'super_admin'
        )
    );

-- Feature Flags Policies (Admin and Super Admin)
CREATE POLICY "Admins can view feature flags" ON public.feature_flags
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
        )
    );

CREATE POLICY "Super admins can manage feature flags" ON public.feature_flags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'super_admin'
        )
    );

-- Content Warnings Policies
CREATE POLICY "Everyone can view content warnings" ON public.content_warnings
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage content warnings" ON public.content_warnings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
        )
    );

-- Admin Actions Policies (Audit log)
CREATE POLICY "Admins can view admin actions" ON public.admin_actions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
        )
    );

CREATE POLICY "Admins can log their own actions" ON public.admin_actions
    FOR INSERT WITH CHECK (auth.uid() = admin_id);

-- Enhanced Reports Policies (update existing)
DROP POLICY IF EXISTS "Admins can update reports" ON public.reports;
CREATE POLICY "Admins can update reports" ON public.reports
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'super_admin')
        )
    );

-- =====================================================
-- 7. CREATE UTILITY FUNCTIONS
-- =====================================================

-- Function to check if user has admin role
CREATE OR REPLACE FUNCTION is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id AND role IN ('admin', 'super_admin')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has super admin role
CREATE OR REPLACE FUNCTION is_super_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id AND role = 'super_admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
    action_type text,
    target_type text DEFAULT NULL,
    target_id uuid DEFAULT NULL,
    details jsonb DEFAULT '{}'::jsonb
)
RETURNS void AS $$
BEGIN
    INSERT INTO public.admin_actions (admin_id, action_type, target_type, target_id, details)
    VALUES (auth.uid(), action_type, target_type, target_id, details);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enhanced report stats function
CREATE OR REPLACE FUNCTION get_report_stats()
RETURNS json AS $$
DECLARE
    result json;
BEGIN
    SELECT json_build_object(
        'total_reports', COUNT(*),
        'pending_reports', COUNT(*) FILTER (WHERE status = 'pending'),
        'reviewed_reports', COUNT(*) FILTER (WHERE status IN ('approved', 'rejected')),
        'approved_reports', COUNT(*) FILTER (WHERE status = 'approved'),
        'rejected_reports', COUNT(*) FILTER (WHERE status = 'rejected'),
        'escalated_reports', COUNT(*) FILTER (WHERE status = 'escalated'),
        'reports_today', COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE),
        'reports_this_week', COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '7 days')
    ) INTO result
    FROM public.reports;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. INSERT DEFAULT DATA
-- =====================================================

-- Insert default app settings
INSERT INTO public.app_settings (key, value, description) VALUES
    ('maintenance_mode', 'false', 'Enable/disable maintenance mode'),
    ('max_joke_duration', '300', 'Maximum joke duration in seconds'),
    ('profanity_filter_enabled', 'true', 'Enable automatic profanity filtering'),
    ('auto_moderation_enabled', 'true', 'Enable automatic content moderation'),
    ('registration_enabled', 'true', 'Allow new user registrations')
ON CONFLICT (key) DO NOTHING;

-- Insert default feature flags
INSERT INTO public.feature_flags (name, enabled, description) VALUES
    ('comments_enabled', true, 'Enable comments on jokes'),
    ('reactions_enabled', true, 'Enable emoji reactions'),
    ('following_enabled', true, 'Enable user following system'),
    ('leaderboard_enabled', true, 'Enable leaderboard feature'),
    ('admin_panel_enabled', true, 'Enable admin panel access')
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- 9. UPDATE TRIGGERS
-- =====================================================

-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at columns
DROP TRIGGER IF EXISTS update_app_settings_updated_at ON public.app_settings;
CREATE TRIGGER update_app_settings_updated_at
    BEFORE UPDATE ON public.app_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_feature_flags_updated_at ON public.feature_flags;
CREATE TRIGGER update_feature_flags_updated_at
    BEFORE UPDATE ON public.feature_flags
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 10. FEED ALGORITHM FUNCTIONS
-- =====================================================

-- Function to get jokes with engagement metrics
CREATE OR REPLACE FUNCTION get_jokes_with_engagement(
    time_filter timestamptz DEFAULT '1970-01-01'::timestamptz,
    limit_count integer DEFAULT 20,
    offset_count integer DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    audio_url text,
    user_id uuid,
    created_at timestamptz,
    updated_at timestamptz,
    is_private boolean,
    effect text,
    duration integer,
    username text,
    avatar_url text,
    bio text,
    likes_count bigint,
    reactions_count bigint,
    comments_count bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        j.id,
        j.title,
        j.content,
        j.audio_url,
        j.user_id,
        j.created_at,
        j.updated_at,
        j.is_private,
        j.effect,
        j.duration,
        p.username,
        p.avatar_url,
        p.bio,
        COALESCE(l.likes_count, 0) as likes_count,
        COALESCE(r.reactions_count, 0) as reactions_count,
        COALESCE(c.comments_count, 0) as comments_count
    FROM public.jokes j
    LEFT JOIN public.profiles p ON j.user_id = p.id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as likes_count
        FROM public.joke_likes
        GROUP BY joke_id
    ) l ON j.id = l.joke_id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as reactions_count
        FROM public.emoji_reactions
        GROUP BY joke_id
    ) r ON j.id = r.joke_id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as comments_count
        FROM public.comments
        GROUP BY joke_id
    ) c ON j.id = c.joke_id
    WHERE j.is_private = false
    AND j.created_at >= time_filter
    ORDER BY (COALESCE(l.likes_count, 0) + COALESCE(r.reactions_count, 0) * 1.5 + COALESCE(c.comments_count, 0) * 3) DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get trending jokes
CREATE OR REPLACE FUNCTION get_trending_jokes(
    time_filter timestamptz DEFAULT NOW() - INTERVAL '24 hours',
    limit_count integer DEFAULT 20,
    offset_count integer DEFAULT 0
)
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    audio_url text,
    user_id uuid,
    created_at timestamptz,
    updated_at timestamptz,
    is_private boolean,
    effect text,
    duration integer,
    username text,
    avatar_url text,
    bio text,
    likes_count bigint,
    reactions_count bigint,
    comments_count bigint,
    trending_score numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        j.id,
        j.title,
        j.content,
        j.audio_url,
        j.user_id,
        j.created_at,
        j.updated_at,
        j.is_private,
        j.effect,
        j.duration,
        p.username,
        p.avatar_url,
        p.bio,
        COALESCE(l.likes_count, 0) as likes_count,
        COALESCE(r.reactions_count, 0) as reactions_count,
        COALESCE(c.comments_count, 0) as comments_count,
        -- Trending score: engagement / age_hours
        CASE
            WHEN EXTRACT(EPOCH FROM (NOW() - j.created_at)) / 3600 > 0 THEN
                (COALESCE(l.likes_count, 0) + COALESCE(r.reactions_count, 0) * 1.5 + COALESCE(c.comments_count, 0) * 3) /
                (EXTRACT(EPOCH FROM (NOW() - j.created_at)) / 3600)
            ELSE 0
        END as trending_score
    FROM public.jokes j
    LEFT JOIN public.profiles p ON j.user_id = p.id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as likes_count
        FROM public.joke_likes
        GROUP BY joke_id
    ) l ON j.id = l.joke_id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as reactions_count
        FROM public.emoji_reactions
        GROUP BY joke_id
    ) r ON j.id = r.joke_id
    LEFT JOIN (
        SELECT joke_id, COUNT(*) as comments_count
        FROM public.comments
        GROUP BY joke_id
    ) c ON j.id = c.joke_id
    WHERE j.is_private = false
    AND j.created_at >= time_filter
    ORDER BY trending_score DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get mutual followers
CREATE OR REPLACE FUNCTION get_mutual_followers(user1_id uuid, user2_id uuid)
RETURNS TABLE (
    id uuid,
    username text,
    avatar_url text,
    bio text
) AS $$
BEGIN
    RETURN QUERY
    SELECT p.id, p.username, p.avatar_url, p.bio
    FROM public.profiles p
    WHERE p.id IN (
        SELECT f1.following_id
        FROM public.follows f1
        WHERE f1.follower_id = user1_id
        INTERSECT
        SELECT f2.following_id
        FROM public.follows f2
        WHERE f2.follower_id = user2_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 11. LEADERBOARD FUNCTIONS
-- =====================================================

-- Function to get overall leaderboard
CREATE OR REPLACE FUNCTION get_overall_leaderboard(
    time_filter timestamptz DEFAULT '1970-01-01'::timestamptz,
    limit_count integer DEFAULT 50,
    offset_count integer DEFAULT 0
)
RETURNS TABLE (
    user_id uuid,
    username text,
    avatar_url text,
    bio text,
    total_jokes bigint,
    total_likes bigint,
    total_reactions bigint,
    total_comments bigint,
    total_followers bigint,
    streak_days integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id as user_id,
        p.username,
        p.avatar_url,
        p.bio,
        COALESCE(j.joke_count, 0) as total_jokes,
        COALESCE(l.like_count, 0) as total_likes,
        COALESCE(r.reaction_count, 0) as total_reactions,
        COALESCE(c.comment_count, 0) as total_comments,
        COALESCE(f.follower_count, 0) as total_followers,
        COALESCE(s.streak_days, 0) as streak_days
    FROM public.profiles p
    LEFT JOIN (
        SELECT user_id, COUNT(*) as joke_count
        FROM public.jokes
        WHERE created_at >= time_filter
        GROUP BY user_id
    ) j ON p.id = j.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as like_count
        FROM public.joke_likes jl
        JOIN public.jokes j ON jl.joke_id = j.id
        WHERE jl.created_at >= time_filter
        GROUP BY j.user_id
    ) l ON p.id = l.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as reaction_count
        FROM public.emoji_reactions er
        JOIN public.jokes j ON er.joke_id = j.id
        WHERE er.created_at >= time_filter
        GROUP BY j.user_id
    ) r ON p.id = r.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as comment_count
        FROM public.comments cm
        JOIN public.jokes j ON cm.joke_id = j.id
        WHERE cm.created_at >= time_filter
        GROUP BY j.user_id
    ) c ON p.id = c.user_id
    LEFT JOIN (
        SELECT following_id, COUNT(*) as follower_count
        FROM public.follows
        WHERE created_at >= time_filter
        GROUP BY following_id
    ) f ON p.id = f.following_id
    LEFT JOIN (
        SELECT user_id,
               CASE
                   WHEN MAX(created_at::date) = CURRENT_DATE THEN
                       COUNT(DISTINCT created_at::date)
                   ELSE 0
               END as streak_days
        FROM public.jokes
        WHERE created_at >= time_filter
        GROUP BY user_id
    ) s ON p.id = s.user_id
    ORDER BY (
        COALESCE(j.joke_count, 0) * 10 +
        COALESCE(l.like_count, 0) * 2 +
        COALESCE(r.reaction_count, 0) * 3 +
        COALESCE(c.comment_count, 0) * 5 +
        COALESCE(f.follower_count, 0) * 8 +
        COALESCE(s.streak_days, 0) * 5
    ) DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get engagement leaderboard
CREATE OR REPLACE FUNCTION get_engagement_leaderboard(
    time_filter timestamptz DEFAULT '1970-01-01'::timestamptz,
    limit_count integer DEFAULT 50,
    offset_count integer DEFAULT 0
)
RETURNS TABLE (
    user_id uuid,
    username text,
    avatar_url text,
    bio text,
    total_jokes bigint,
    total_likes bigint,
    total_reactions bigint,
    total_comments bigint,
    engagement_score numeric
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id as user_id,
        p.username,
        p.avatar_url,
        p.bio,
        COALESCE(j.joke_count, 0) as total_jokes,
        COALESCE(l.like_count, 0) as total_likes,
        COALESCE(r.reaction_count, 0) as total_reactions,
        COALESCE(c.comment_count, 0) as total_comments,
        (COALESCE(l.like_count, 0) + COALESCE(r.reaction_count, 0) * 1.5 + COALESCE(c.comment_count, 0) * 2) as engagement_score
    FROM public.profiles p
    LEFT JOIN (
        SELECT user_id, COUNT(*) as joke_count
        FROM public.jokes
        WHERE created_at >= time_filter
        GROUP BY user_id
    ) j ON p.id = j.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as like_count
        FROM public.joke_likes jl
        JOIN public.jokes j ON jl.joke_id = j.id
        WHERE jl.created_at >= time_filter
        GROUP BY j.user_id
    ) l ON p.id = l.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as reaction_count
        FROM public.emoji_reactions er
        JOIN public.jokes j ON er.joke_id = j.id
        WHERE er.created_at >= time_filter
        GROUP BY j.user_id
    ) r ON p.id = r.user_id
    LEFT JOIN (
        SELECT j.user_id, COUNT(*) as comment_count
        FROM public.comments cm
        JOIN public.jokes j ON cm.joke_id = j.id
        WHERE cm.created_at >= time_filter
        GROUP BY j.user_id
    ) c ON p.id = c.user_id
    WHERE (COALESCE(l.like_count, 0) + COALESCE(r.reaction_count, 0) + COALESCE(c.comment_count, 0)) > 0
    ORDER BY engagement_score DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

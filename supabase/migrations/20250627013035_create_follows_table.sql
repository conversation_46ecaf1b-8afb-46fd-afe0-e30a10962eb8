-- Create follows table
create table if not exists public.follows (
    follower_id uuid references auth.users(id) on delete cascade not null,
    following_id uuid references auth.users(id) on delete cascade not null,
    created_at timestamptz default now(),
    primary key (follower_id, following_id)
);

-- Add RLS policies
alter table public.follows enable row level security;

create policy "Follows are viewable by everyone" on public.follows for select using (true);

create policy "Users can follow other users" on public.follows for insert with check (auth.uid() = follower_id);

create policy "Users can unfollow other users" on public.follows for delete using (auth.uid() = follower_id);

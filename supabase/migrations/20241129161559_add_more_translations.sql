-- Add more missing translations
INSERT INTO translations (language, key, value)
VALUES

    -- French translations

    -- Arabic translations
    ('fr', '
        id,
        title,
        content,
        audio_url,
        user_id,
        created_at,
        profiles (
          username,
          avatar_url
        ),
        joke_categories (
          categories (
            name,
            slug
          )
        )
      ', '[FR] 
        id,
        title,
        content,
        audio_url,
        user_id,
        created_at,
        profiles (
          username,
          avatar_url
        ),
        joke_categories (
          categories (
            name,
            slug
          )
        )
      '),
    ('fr', '
        id,
        category,
        featured_joke_id,
        featured_joke:featured_joke_id (
          id,
          title,
          content
        )
      ', '[FR] 
        id,
        category,
        featured_joke_id,
        featured_joke:featured_joke_id (
          id,
          title,
          content
        )
      '),
    ('fr', 'comments.deleteConfirm', '[FR] comments.deleteConfirm'),
    ('fr', 'comments.edited', '[FR] comments.edited'),
    ('fr', 'comments.title', '[FR] comments.title'),
    ('fr', 'errors.generic', '[FR] errors.generic'),
    ('fr', 'comments.placeholder', '[FR] comments.placeholder'),
    ('fr', 'comments.empty', '[FR] comments.empty'),
    ('fr', 'navigation.record', '[FR] navigation.record'),
    ('fr', 'errors.loadingJokes', '[FR] errors.loadingJokes'),
    ('fr', 'jokes.noJokesFound', '[FR] jokes.noJokesFound'),
    ('fr', 'common.loadMore', '[FR] common.loadMore'),
    ('fr', '
                        id,
                        title,
                        audio_url,
                        created_at,
                        likes_count,
                        plays_count,
                        comments_count,
                        user_id,
                        profiles!jokes_user_id_fkey (
                            username,
                            avatar_url
                        ),
                        joke_categories (
                            categories (
                                id,
                                name,
                                slug
                            )
                        )
                    ', '[FR] 
                        id,
                        title,
                        audio_url,
                        created_at,
                        likes_count,
                        plays_count,
                        comments_count,
                        user_id,
                        profiles!jokes_user_id_fkey (
                            username,
                            avatar_url
                        ),
                        joke_categories (
                            categories (
                                id,
                                name,
                                slug
                            )
                        )
                    '),
    ('fr', 'search.placeholder', '[FR] search.placeholder'),
    ('fr', 'sharing.shareJoke', '[FR] sharing.shareJoke'),
    ('fr', 'common.close', '[FR] common.close'),
    ('fr', 'sharing.copyLink', '[FR] sharing.copyLink'),
    ('fr', 'errors.reactions.load', '[FR] errors.reactions.load'),
    ('fr', 'errors.audio.maxRetries', '[FR] errors.audio.maxRetries'),
    ('fr', 'errors.audio.permission', '[FR] errors.audio.permission'),
    ('fr', 'errors.audio.format', '[FR] errors.audio.format'),
    ('fr', 'errors.audio.network', '[FR] errors.audio.network'),
    ('fr', 'errors.audio.aborted', '[FR] errors.audio.aborted'),
    ('fr', 'errors.audio.decode', '[FR] errors.audio.decode'),
    ('fr', 'errors.audio.general', '[FR] errors.audio.general'),
    ('fr', 'errors.auth.required', '[FR] errors.auth.required'),
    ('fr', 'errors.reactions.toggle', '[FR] errors.reactions.toggle'),
    ('fr', 'jokeBox.buffering', '[FR] jokeBox.buffering'),
    ('fr', '
            id,
            user_id,
            category,
            start_date,
            end_date,
            metrics,
            featured_joke:featured_joke_id (
              id,
              title,
              content,
              audio_url,
              metrics
            )
          ', '[FR] 
            id,
            user_id,
            category,
            start_date,
            end_date,
            metrics,
            featured_joke:featured_joke_id (
              id,
              title,
              content,
              audio_url,
              metrics
            )
          '),
    ('fr', 'errors.microphone.access', '[FR] errors.microphone.access'),
    ('fr', 'errors.form.required', '[FR] errors.form.required'),
    ('fr', 'errors.upload.failed', '[FR] errors.upload.failed'),
    ('fr', 'recorder.titlePlaceholder', '[FR] recorder.titlePlaceholder'),
    ('fr', 'recorder.category', '[FR] recorder.category'),
    ('fr', 'recorder.uploading', '[FR] recorder.uploading'),
    ('fr', 'recorder.submit', '[FR] recorder.submit'),
    ('fr', 'categories.${id}', '[FR] categories.${id}'),
    ('fr', '\n', '[FR] \n'),
    ('fr', '
        *,
        profiles:user_id (username, avatar_url)
      ', '[FR] 
        *,
        profiles:user_id (username, avatar_url)
      '),
    ('fr', '
        *,
        jokes (*)
      ', '[FR] 
        *,
        jokes (*)
      '),
    ('fr', '
        *,
        user:user_id (username, avatar_url)
      ', '[FR] 
        *,
        user:user_id (username, avatar_url)
      '),
    ('fr', '
        *,
        profiles:user_id (username, avatar_url),
        category:category_id (*)
      ', '[FR] 
        *,
        profiles:user_id (username, avatar_url),
        category:category_id (*)
      '),
    ('fr', '
          *,
          profiles:user_id (username, avatar_url)
        ', '[FR] 
          *,
          profiles:user_id (username, avatar_url)
        '),
    ('fr', 'home.noJokes', '[FR] home.noJokes'),
    ('fr', '
            *,
            profiles:user_id (username, avatar_url)
          ', '[FR] 
            *,
            profiles:user_id (username, avatar_url)
          '),
    ('fr', 'errors.achievements.fetch', '[FR] errors.achievements.fetch'),
    ('fr', 'achievements.title', '[FR] achievements.title'),
    ('fr', 'achievements.subtitle', '[FR] achievements.subtitle'),
    ('fr', 'achievements.completed', '[FR] achievements.completed'),
    ('fr', 'achievements.total', '[FR] achievements.total'),
    ('fr', 'achievements.completion', '[FR] achievements.completion'),
    ('fr', 'achievements.points', '[FR] achievements.points'),
    ('fr', 'achievements.${achievement.type}.title', '[FR] achievements.${achievement.type}.title'),
    ('fr', 'achievements.${achievement.type}.description', '[FR] achievements.${achievement.type}.description'),
    ('fr', '
            *,
            participants:challenge_participants(count),
            jokes:challenge_jokes(count)
          ', '[FR] 
            *,
            participants:challenge_participants(count),
            jokes:challenge_jokes(count)
          '),
    ('fr', 'errors.favorites.fetch', '[FR] errors.favorites.fetch'),
    ('fr', 'favorites.title', '[FR] favorites.title'),
    ('fr', 'favorites.subtitle', '[FR] favorites.subtitle'),
    ('fr', 'favorites.empty', '[FR] favorites.empty'),
    ('fr', '
            jokes:joke_id (
              *,
              profiles:user_id (username, avatar_url)
            )
          ', '[FR] 
            jokes:joke_id (
              *,
              profiles:user_id (username, avatar_url)
            )
          '),
    ('fr', '
                category_id,
                categories (
                    id,
                    name,
                    slug
                )
            ', '[FR] 
                category_id,
                categories (
                    id,
                    name,
                    slug
                )
            '),
    ('fr', '
                *,
                profiles!jokes_user_id_fkey (
                    username,
                    avatar_url
                ),
                joke_categories!inner (
                    categories (
                        id,
                        name,
                        slug
                    )
                )
            ', '[FR] 
                *,
                profiles!jokes_user_id_fkey (
                    username,
                    avatar_url
                ),
                joke_categories!inner (
                    categories (
                        id,
                        name,
                        slug
                    )
                )
            '),
    ('fr', '
        id,
        category,
        start_date,
        end_date,
        metrics,
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          audio_url,
          user_id,
          created_at,
          profiles:user_id (
            username,
            avatar_url
          )
        )
      ', '[FR] 
        id,
        category,
        start_date,
        end_date,
        metrics,
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          audio_url,
          user_id,
          created_at,
          profiles:user_id (
            username,
            avatar_url
          )
        )
      '),
    ('fr', '
        progress,
        unlocked_at,
        achievement:achievement_id (
          id,
          title,
          description,
          icon,
          badge_color,
          category,
          requirement,
          xp_reward
        )
      ', '[FR] 
        progress,
        unlocked_at,
        achievement:achievement_id (
          id,
          title,
          description,
          icon,
          badge_color,
          category,
          requirement,
          xp_reward
        )
      '),
    ('ar', '
        id,
        title,
        content,
        audio_url,
        user_id,
        created_at,
        profiles (
          username,
          avatar_url
        ),
        joke_categories (
          categories (
            name,
            slug
          )
        )
      ', '[AR] 
        id,
        title,
        content,
        audio_url,
        user_id,
        created_at,
        profiles (
          username,
          avatar_url
        ),
        joke_categories (
          categories (
            name,
            slug
          )
        )
      '),
    ('ar', '
        id,
        category,
        featured_joke_id,
        featured_joke:featured_joke_id (
          id,
          title,
          content
        )
      ', '[AR] 
        id,
        category,
        featured_joke_id,
        featured_joke:featured_joke_id (
          id,
          title,
          content
        )
      '),
    ('ar', 'comments.deleteConfirm', '[AR] comments.deleteConfirm'),
    ('ar', 'comments.edited', '[AR] comments.edited'),
    ('ar', 'comments.title', '[AR] comments.title'),
    ('ar', 'errors.generic', '[AR] errors.generic'),
    ('ar', 'comments.placeholder', '[AR] comments.placeholder'),
    ('ar', 'comments.empty', '[AR] comments.empty'),
    ('ar', 'navigation.record', '[AR] navigation.record'),
    ('ar', 'errors.loadingJokes', '[AR] errors.loadingJokes'),
    ('ar', 'jokes.noJokesFound', '[AR] jokes.noJokesFound'),
    ('ar', 'common.loadMore', '[AR] common.loadMore'),
    ('ar', '
                        id,
                        title,
                        audio_url,
                        created_at,
                        likes_count,
                        plays_count,
                        comments_count,
                        user_id,
                        profiles!jokes_user_id_fkey (
                            username,
                            avatar_url
                        ),
                        joke_categories (
                            categories (
                                id,
                                name,
                                slug
                            )
                        )
                    ', '[AR] 
                        id,
                        title,
                        audio_url,
                        created_at,
                        likes_count,
                        plays_count,
                        comments_count,
                        user_id,
                        profiles!jokes_user_id_fkey (
                            username,
                            avatar_url
                        ),
                        joke_categories (
                            categories (
                                id,
                                name,
                                slug
                            )
                        )
                    '),
    ('ar', 'search.placeholder', '[AR] search.placeholder'),
    ('ar', 'sharing.shareJoke', '[AR] sharing.shareJoke'),
    ('ar', 'common.close', '[AR] common.close'),
    ('ar', 'sharing.copyLink', '[AR] sharing.copyLink'),
    ('ar', 'errors.reactions.load', '[AR] errors.reactions.load'),
    ('ar', 'errors.audio.maxRetries', '[AR] errors.audio.maxRetries'),
    ('ar', 'errors.audio.permission', '[AR] errors.audio.permission'),
    ('ar', 'errors.audio.format', '[AR] errors.audio.format'),
    ('ar', 'errors.audio.network', '[AR] errors.audio.network'),
    ('ar', 'errors.audio.aborted', '[AR] errors.audio.aborted'),
    ('ar', 'errors.audio.decode', '[AR] errors.audio.decode'),
    ('ar', 'errors.audio.general', '[AR] errors.audio.general'),
    ('ar', 'errors.auth.required', '[AR] errors.auth.required'),
    ('ar', 'errors.reactions.toggle', '[AR] errors.reactions.toggle'),
    ('ar', 'jokeBox.buffering', '[AR] jokeBox.buffering'),
    ('ar', '
            id,
            user_id,
            category,
            start_date,
            end_date,
            metrics,
            featured_joke:featured_joke_id (
              id,
              title,
              content,
              audio_url,
              metrics
            )
          ', '[AR] 
            id,
            user_id,
            category,
            start_date,
            end_date,
            metrics,
            featured_joke:featured_joke_id (
              id,
              title,
              content,
              audio_url,
              metrics
            )
          '),
    ('ar', 'errors.microphone.access', '[AR] errors.microphone.access'),
    ('ar', 'errors.form.required', '[AR] errors.form.required'),
    ('ar', 'errors.upload.failed', '[AR] errors.upload.failed'),
    ('ar', 'recorder.titlePlaceholder', '[AR] recorder.titlePlaceholder'),
    ('ar', 'recorder.category', '[AR] recorder.category'),
    ('ar', 'recorder.uploading', '[AR] recorder.uploading'),
    ('ar', 'recorder.submit', '[AR] recorder.submit'),
    ('ar', 'categories.${id}', '[AR] categories.${id}'),
    ('ar', '\n', '[AR] \n'),
    ('ar', '
        *,
        profiles:user_id (username, avatar_url)
      ', '[AR] 
        *,
        profiles:user_id (username, avatar_url)
      '),
    ('ar', '
        *,
        jokes (*)
      ', '[AR] 
        *,
        jokes (*)
      '),
    ('ar', '
        *,
        user:user_id (username, avatar_url)
      ', '[AR] 
        *,
        user:user_id (username, avatar_url)
      '),
    ('ar', '
        *,
        profiles:user_id (username, avatar_url),
        category:category_id (*)
      ', '[AR] 
        *,
        profiles:user_id (username, avatar_url),
        category:category_id (*)
      '),
    ('ar', '
          *,
          profiles:user_id (username, avatar_url)
        ', '[AR] 
          *,
          profiles:user_id (username, avatar_url)
        '),
    ('ar', 'home.noJokes', '[AR] home.noJokes'),
    ('ar', '
            *,
            profiles:user_id (username, avatar_url)
          ', '[AR] 
            *,
            profiles:user_id (username, avatar_url)
          '),
    ('ar', 'errors.achievements.fetch', '[AR] errors.achievements.fetch'),
    ('ar', 'achievements.title', '[AR] achievements.title'),
    ('ar', 'achievements.subtitle', '[AR] achievements.subtitle'),
    ('ar', 'achievements.completed', '[AR] achievements.completed'),
    ('ar', 'achievements.total', '[AR] achievements.total'),
    ('ar', 'achievements.completion', '[AR] achievements.completion'),
    ('ar', 'achievements.points', '[AR] achievements.points'),
    ('ar', 'achievements.${achievement.type}.title', '[AR] achievements.${achievement.type}.title'),
    ('ar', 'achievements.${achievement.type}.description', '[AR] achievements.${achievement.type}.description'),
    ('ar', '
            *,
            participants:challenge_participants(count),
            jokes:challenge_jokes(count)
          ', '[AR] 
            *,
            participants:challenge_participants(count),
            jokes:challenge_jokes(count)
          '),
    ('ar', 'errors.favorites.fetch', '[AR] errors.favorites.fetch'),
    ('ar', 'favorites.title', '[AR] favorites.title'),
    ('ar', 'favorites.subtitle', '[AR] favorites.subtitle'),
    ('ar', 'favorites.empty', '[AR] favorites.empty'),
    ('ar', '
            jokes:joke_id (
              *,
              profiles:user_id (username, avatar_url)
            )
          ', '[AR] 
            jokes:joke_id (
              *,
              profiles:user_id (username, avatar_url)
            )
          '),
    ('ar', '
                category_id,
                categories (
                    id,
                    name,
                    slug
                )
            ', '[AR] 
                category_id,
                categories (
                    id,
                    name,
                    slug
                )
            '),
    ('ar', '
                *,
                profiles!jokes_user_id_fkey (
                    username,
                    avatar_url
                ),
                joke_categories!inner (
                    categories (
                        id,
                        name,
                        slug
                    )
                )
            ', '[AR] 
                *,
                profiles!jokes_user_id_fkey (
                    username,
                    avatar_url
                ),
                joke_categories!inner (
                    categories (
                        id,
                        name,
                        slug
                    )
                )
            '),
    ('ar', '
        id,
        category,
        start_date,
        end_date,
        metrics,
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          audio_url,
          user_id,
          created_at,
          profiles:user_id (
            username,
            avatar_url
          )
        )
      ', '[AR] 
        id,
        category,
        start_date,
        end_date,
        metrics,
        featured_joke:featured_joke_id (
          id,
          title,
          content,
          audio_url,
          user_id,
          created_at,
          profiles:user_id (
            username,
            avatar_url
          )
        )
      '),
    ('ar', '
        progress,
        unlocked_at,
        achievement:achievement_id (
          id,
          title,
          description,
          icon,
          badge_color,
          category,
          requirement,
          xp_reward
        )
      ', '[AR] 
        progress,
        unlocked_at,
        achievement:achievement_id (
          id,
          title,
          description,
          icon,
          badge_color,
          category,
          requirement,
          xp_reward
        )
      ')
ON CONFLICT (language, key) 
DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = TIMEZONE('utc'::text, NOW());
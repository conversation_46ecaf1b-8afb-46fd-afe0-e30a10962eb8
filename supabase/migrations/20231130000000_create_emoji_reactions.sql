-- Enable UUID extension if not already enabled
create extension if not exists "uuid-ossp";

-- Create emoji reactions table
create table if not exists public.emoji_reactions (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    joke_id uuid references public.jokes(id) on delete cascade not null,
    emoji text not null,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    
    -- Ensure a user can only react once with the same emoji on a joke
    constraint unique_user_joke_emoji unique (user_id, joke_id, emoji)
);

-- Create indexes for better query performance
create index if not exists emoji_reactions_joke_id_idx on public.emoji_reactions(joke_id);
create index if not exists emoji_reactions_user_id_idx on public.emoji_reactions(user_id);
create index if not exists emoji_reactions_emoji_idx on public.emoji_reactions(emoji);

-- Add RLS policies
alter table public.emoji_reactions enable row level security;

-- Allow read access to everyone
create policy "Emoji reactions are viewable by everyone"
    on public.emoji_reactions
    for select
    using (true);

-- Allow authenticated users to add reactions
create policy "Authenticated users can add reactions"
    on public.emoji_reactions
    for insert
    to authenticated
    with check (auth.uid() = user_id);

-- Allow users to remove their own reactions
create policy "Users can delete their own reactions"
    on public.emoji_reactions
    for delete
    to authenticated
    using (auth.uid() = user_id);

-- Create function to get reaction counts for a joke
create or replace function public.get_joke_reaction_counts(joke_uuid uuid)
returns table (
    emoji text,
    count bigint
)
language sql
security definer
as $$
    select emoji, count(*)::bigint
    from public.emoji_reactions
    where joke_id = joke_uuid
    group by emoji;
$$;

-- Create function to get user's reactions for a joke
create or replace function public.get_user_joke_reactions(joke_uuid uuid, user_uuid uuid)
returns table (
    emoji text
)
language sql
security definer
as $$
    select emoji
    from public.emoji_reactions
    where joke_id = joke_uuid and user_id = user_uuid;
$$;

-- Add comment to explain the purpose of the table
comment on table public.emoji_reactions is 'Stores emoji reactions on jokes';

-- Add comments on columns
comment on column public.emoji_reactions.id is 'The unique identifier for the reaction';
comment on column public.emoji_reactions.user_id is 'The user who made the reaction';
comment on column public.emoji_reactions.joke_id is 'The joke that was reacted to';
comment on column public.emoji_reactions.emoji is 'The emoji used in the reaction';
comment on column public.emoji_reactions.created_at is 'When the reaction was created';
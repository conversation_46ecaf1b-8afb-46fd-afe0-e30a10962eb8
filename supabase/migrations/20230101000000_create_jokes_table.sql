-- Create jokes table
create table if not exists public.jokes (
    id uuid default uuid_generate_v4() primary key,
    user_id uuid references auth.users(id) on delete cascade not null,
    audio_url text not null,
    title text not null,
    created_at timestamptz default now(),
    likes_count int default 0,
    plays_count int default 0,
    comments_count int default 0,
    voice_effect text,
    is_private boolean default false
);

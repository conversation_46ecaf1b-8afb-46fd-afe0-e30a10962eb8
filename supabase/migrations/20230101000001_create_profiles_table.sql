-- Create profiles table
create table if not exists public.profiles (
    id uuid references auth.users(id) on delete cascade not null primary key,
    username text unique,
    avatar_url text,
    bio text,
    created_at timestamptz default now(),
    notification_settings jsonb default '{"new_comments": true, "new_reactions": true, "new_followers": true}'::jsonb
);

-- Set up RLS
alter table public.profiles enable row level security;

create policy "Public profiles are viewable by everyone." on public.profiles for select using (true);

create policy "Users can insert their own profile." on public.profiles for insert with check (auth.uid() = id);

create policy "Users can update own profile." on public.profiles for update using (auth.uid() = id);

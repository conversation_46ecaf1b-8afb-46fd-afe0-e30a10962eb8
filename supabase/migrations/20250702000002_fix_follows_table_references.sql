-- Fix follows table constraints and foreign keys
-- This fixes the check constraint violation error and ensures proper relationships

-- The check constraint already exists correctly: CHECK ((follower_id <> following_id))
-- So we only need to fix the foreign key constraints

-- Ensure we have the right foreign key constraints to profiles table
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_follower_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_following_id_fkey;

-- Add foreign key constraints to profiles table (not auth.users)
ALTER TABLE public.follows
ADD CONSTRAINT follows_follower_id_fkey
FOREIGN KEY (follower_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.follows
ADD CONSTRAINT follows_following_id_fkey
FOREIGN KEY (following_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Follows table foreign key constraints fixed to reference profiles table.';
END $$;

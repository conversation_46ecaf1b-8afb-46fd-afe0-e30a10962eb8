-- Create a helper function to check joke ownership
CREATE OR REPLACE FUNCTION is_own_joke(joke_id uuid)
RETURNS boolean AS $$
  SELECT auth.uid() = (SELECT user_id FROM jokes WHERE id = joke_id);
$$ LANGUAGE sql SECURITY DEFINER;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can insert their own jokes" ON public.jokes;
DROP POLICY IF EXISTS "Users can update their own jokes" ON public.jokes;
DROP POLICY IF EXISTS "Users can delete their own jokes" ON public.jokes;

-- Recreate policies using the helper function
CREATE POLICY "Users can insert their own jokes" ON public.jokes
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own jokes" ON public.jokes
  FOR UPDATE
  USING (is_own_joke(id));

CREATE POLICY "Users can delete their own jokes" ON public.jokes
  FOR DELETE
  USING (is_own_joke(id));

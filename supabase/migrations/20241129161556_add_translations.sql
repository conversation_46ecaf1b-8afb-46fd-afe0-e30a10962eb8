-- Create translations table if not exists
CREATE TABLE IF NOT EXISTS translations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    language TEXT NOT NULL,
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(language, key)
);

-- Add RLS policies
ALTER TABLE translations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Enable read access for all users"
ON translations FOR SELECT
USING (true);

-- Insert French translations
INSERT INTO translations (language, key, value)
VALUES
    ('fr', 'user.anonymous', 'Utilisateur anonyme'),
    ('fr', 'social.likes', 'J''aime'),
    ('fr', 'social.comments', 'Commentaires'),
    ('fr', 'social.follow', 'Suivre'),
    ('fr', 'social.following', 'Suivi'),
    ('fr', 'social.unfollow', 'Ne plus suivre'),
    ('fr', 'errors.audio.permission', 'Veuillez autoriser l''accès audio'),
    ('fr', 'errors.audio.maxRetries', 'Nombre maximal de tentatives atteint'),
    ('fr', 'errors.reactions.load', 'Erreur lors du chargement des réactions'),
    ('fr', 'errors.audio.format', 'Format audio non pris en charge'),
    ('fr', 'errors.audio.network', 'Erreur réseau lors du chargement audio')
ON CONFLICT (language, key) 
DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = TIMEZONE('utc'::text, NOW());

-- Insert Arabic translations
INSERT INTO translations (language, key, value)
VALUES
    ('ar', 'user.anonymous', 'مستخدم مجهول'),
    ('ar', 'social.likes', 'إعجاب'),
    ('ar', 'social.comments', 'تعليق'),
    ('ar', 'social.follow', 'متابعة'),
    ('ar', 'social.following', 'متابَع'),
    ('ar', 'social.unfollow', 'إلغاء المتابعة'),
    ('ar', 'errors.audio.permission', 'يرجى السماح بالوصول إلى الصوت'),
    ('ar', 'errors.audio.maxRetries', 'تم الوصول إلى الحد الأقصى من المحاولات'),
    ('ar', 'errors.reactions.load', 'خطأ في تحميل التفاعلات'),
    ('ar', 'errors.audio.format', 'تنسيق الصوت غير مدعوم'),
    ('ar', 'errors.audio.network', 'خطأ في الشبكة أثناء تحميل الصوت')
ON CONFLICT (language, key) 
DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = TIMEZONE('utc'::text, NOW());
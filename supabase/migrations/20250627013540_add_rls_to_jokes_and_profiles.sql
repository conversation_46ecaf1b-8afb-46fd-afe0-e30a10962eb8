-- Add RLS policies for jokes table
alter table public.jokes enable row level security;

create policy "Jokes are viewable by everyone" on public.jokes for select using (true);

create policy "Users can insert their own jokes" on public.jokes for insert with check (auth.uid() = user_id);

create policy "Users can update their own jokes" on public.jokes for update using (auth.uid() = user_id);

create policy "Users can delete their own jokes" on public.jokes for delete using (auth.uid() = user_id);

-- Add RLS policies for profiles table
alter table public.profiles enable row level security;

create policy "Profiles are viewable by everyone" on public.profiles for select using (true);

create policy "Users can update their own profile" on public.profiles for update using (auth.uid() = id);

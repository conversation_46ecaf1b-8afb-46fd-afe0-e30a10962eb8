-- Create Reports System for Content Moderation
-- This migration creates the necessary tables and functions for content reporting

-- Create enum for report types
CREATE TYPE report_type AS ENUM ('joke', 'user', 'comment');

-- Create enum for report status
CREATE TYPE report_status AS ENUM ('pending', 'reviewed', 'approved', 'rejected', 'escalated');

-- Create enum for report reasons
CREATE TYPE report_reason AS ENUM (
  'inappropriate_content',
  'spam',
  'harassment',
  'hate_speech',
  'violence',
  'sexual_content',
  'copyright',
  'misinformation',
  'other'
);

-- Create reports table
CREATE TABLE IF NOT EXISTS public.reports (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  reporter_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  reported_item_id uuid NOT NULL,
  reported_user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  report_type report_type NOT NULL,
  reason report_reason NOT NULL,
  description text,
  status report_status DEFAULT 'pending',
  reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  reviewed_at timestamptz,
  admin_notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_blocks table for blocking functionality
CREATE TABLE IF NOT EXISTS public.user_blocks (
  blocker_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  blocked_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  reason text,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (blocker_id, blocked_id),
  CONSTRAINT no_self_block CHECK (blocker_id != blocked_id)
);

-- Create content_warnings table
CREATE TABLE IF NOT EXISTS public.content_warnings (
  id uuid DEFAULT uuid_generate_v4() PRIMARY KEY,
  joke_id uuid REFERENCES public.jokes(id) ON DELETE CASCADE NOT NULL,
  warning_type text NOT NULL,
  reason text,
  added_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS reports_reporter_id_idx ON public.reports(reporter_id);
CREATE INDEX IF NOT EXISTS reports_reported_item_id_idx ON public.reports(reported_item_id);
CREATE INDEX IF NOT EXISTS reports_status_idx ON public.reports(status);
CREATE INDEX IF NOT EXISTS reports_created_at_idx ON public.reports(created_at DESC);
CREATE INDEX IF NOT EXISTS user_blocks_blocker_id_idx ON public.user_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS user_blocks_blocked_id_idx ON public.user_blocks(blocked_id);
CREATE INDEX IF NOT EXISTS content_warnings_joke_id_idx ON public.content_warnings(joke_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS trigger AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for reports table
DROP TRIGGER IF EXISTS update_reports_updated_at ON public.reports;
CREATE TRIGGER update_reports_updated_at
    BEFORE UPDATE ON public.reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on all tables
ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_warnings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for reports
CREATE POLICY "Users can create reports" ON public.reports
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

CREATE POLICY "Users can view their own reports" ON public.reports
  FOR SELECT USING (auth.uid() = reporter_id);

CREATE POLICY "Admins can view all reports" ON public.reports
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Admins can update reports" ON public.reports
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'super_admin')
    )
  );

-- RLS Policies for user_blocks
CREATE POLICY "Users can manage their own blocks" ON public.user_blocks
  FOR ALL USING (auth.uid() = blocker_id);

CREATE POLICY "Users can see if they are blocked" ON public.user_blocks
  FOR SELECT USING (auth.uid() = blocked_id);

-- RLS Policies for content_warnings
CREATE POLICY "Everyone can view content warnings" ON public.content_warnings
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage content warnings" ON public.content_warnings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'super_admin')
    )
  );

-- Function to check if user is blocked
CREATE OR REPLACE FUNCTION is_user_blocked(target_user_id uuid, current_user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_blocks 
    WHERE blocker_id = target_user_id 
    AND blocked_id = current_user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get report statistics
CREATE OR REPLACE FUNCTION get_report_stats()
RETURNS json AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'total_reports', COUNT(*),
    'pending_reports', COUNT(*) FILTER (WHERE status = 'pending'),
    'reviewed_reports', COUNT(*) FILTER (WHERE status = 'reviewed'),
    'approved_reports', COUNT(*) FILTER (WHERE status = 'approved'),
    'rejected_reports', COUNT(*) FILTER (WHERE status = 'rejected')
  ) INTO result
  FROM public.reports;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create leaderboard RPC functions
-- This migration creates the missing RPC functions for the leaderboard

-- Function to get overall leaderboard
CREATE OR REPLACE FUNCTION get_overall_leaderboard(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  avatar_url TEXT,
  total_likes BIGINT,
  total_jokes BIGINT,
  total_plays BIGINT,
  score BIGINT
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as user_id,
    p.username,
    p.avatar_url,
    COALESCE(SUM(j.likes_count), 0) as total_likes,
    COUNT(j.id) as total_jokes,
    COALESCE(SUM(j.plays_count), 0) as total_plays,
    -- Calculate score: likes * 3 + plays * 1 + jokes * 2
    (COALESCE(SUM(j.likes_count), 0) * 3 + 
     COALESCE(SUM(j.plays_count), 0) * 1 + 
     COUNT(j.id) * 2) as score
  FROM profiles p
  LEFT JOIN jokes j ON p.id = j.user_id AND j.is_private = false
  GROUP BY p.id, p.username, p.avatar_url
  HAVING COUNT(j.id) > 0  -- Only include users with at least one joke
  ORDER BY score DESC
  LIMIT limit_count;
END;
$$;

-- Function to get weekly leaderboard
CREATE OR REPLACE FUNCTION get_weekly_leaderboard(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  avatar_url TEXT,
  weekly_likes BIGINT,
  weekly_jokes BIGINT,
  weekly_plays BIGINT,
  weekly_score BIGINT
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as user_id,
    p.username,
    p.avatar_url,
    COALESCE(SUM(j.likes_count), 0) as weekly_likes,
    COUNT(j.id) as weekly_jokes,
    COALESCE(SUM(j.plays_count), 0) as weekly_plays,
    -- Calculate weekly score
    (COALESCE(SUM(j.likes_count), 0) * 3 + 
     COALESCE(SUM(j.plays_count), 0) * 1 + 
     COUNT(j.id) * 2) as weekly_score
  FROM profiles p
  LEFT JOIN jokes j ON p.id = j.user_id 
    AND j.is_private = false 
    AND j.created_at >= NOW() - INTERVAL '7 days'
  GROUP BY p.id, p.username, p.avatar_url
  HAVING COUNT(j.id) > 0  -- Only include users with jokes this week
  ORDER BY weekly_score DESC
  LIMIT limit_count;
END;
$$;

-- Function to get monthly leaderboard
CREATE OR REPLACE FUNCTION get_monthly_leaderboard(limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  avatar_url TEXT,
  monthly_likes BIGINT,
  monthly_jokes BIGINT,
  monthly_plays BIGINT,
  monthly_score BIGINT
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as user_id,
    p.username,
    p.avatar_url,
    COALESCE(SUM(j.likes_count), 0) as monthly_likes,
    COUNT(j.id) as monthly_jokes,
    COALESCE(SUM(j.plays_count), 0) as monthly_plays,
    -- Calculate monthly score
    (COALESCE(SUM(j.likes_count), 0) * 3 + 
     COALESCE(SUM(j.plays_count), 0) * 1 + 
     COUNT(j.id) * 2) as monthly_score
  FROM profiles p
  LEFT JOIN jokes j ON p.id = j.user_id 
    AND j.is_private = false 
    AND j.created_at >= NOW() - INTERVAL '30 days'
  GROUP BY p.id, p.username, p.avatar_url
  HAVING COUNT(j.id) > 0  -- Only include users with jokes this month
  ORDER BY monthly_score DESC
  LIMIT limit_count;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_overall_leaderboard(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_weekly_leaderboard(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_monthly_leaderboard(INTEGER) TO authenticated;

-- Grant execute permissions to anon users (for public leaderboard)
GRANT EXECUTE ON FUNCTION get_overall_leaderboard(INTEGER) TO anon;
GRANT EXECUTE ON FUNCTION get_weekly_leaderboard(INTEGER) TO anon;
GRANT EXECUTE ON FUNCTION get_monthly_leaderboard(INTEGER) TO anon;

-- Log completion
DO $$ 
BEGIN
    RAISE NOTICE 'Leaderboard RPC functions created successfully.';
END $$;

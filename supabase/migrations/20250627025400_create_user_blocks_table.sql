-- Create user_blocks table
create table if not exists public.user_blocks (
    blocker_id uuid references auth.users(id) on delete cascade not null,
    blocked_id uuid references auth.users(id) on delete cascade not null,
    created_at timestamptz default now(),
    primary key (blocker_id, blocked_id)
);

-- Add RLS policies
alter table public.user_blocks enable row level security;

create policy "Users can view their own blocks" on public.user_blocks for select using (auth.uid() = blocker_id or auth.uid() = blocked_id);

create policy "Users can block other users" on public.user_blocks for insert with check (auth.uid() = blocker_id);

create policy "Users can unblock other users" on public.user_blocks for delete using (auth.uid() = blocker_id);

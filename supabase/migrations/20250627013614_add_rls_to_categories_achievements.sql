-- Add RLS policies for categories table
alter table public.categories enable row level security;

create policy "Categories are viewable by everyone" on public.categories for select using (true);

-- Add RLS policies for joke_categories table
alter table public.joke_categories enable row level security;

create policy "Joke categories are viewable by everyone" on public.joke_categories for select using (true);

-- Add RLS policies for achievements table
alter table public.achievements enable row level security;

create policy "Achievements are viewable by everyone" on public.achievements for select using (true);

-- Add RLS policies for user_achievements table
alter table public.user_achievements enable row level security;

create policy "User achievements are viewable by everyone" on public.user_achievements for select using (true);

create policy "Users can insert their own user achievements" on public.user_achievements for insert with check (auth.uid() = user_id);

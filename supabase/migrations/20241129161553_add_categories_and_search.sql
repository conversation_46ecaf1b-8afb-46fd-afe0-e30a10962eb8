-- Create categories table if not exists
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create joke_categories junction table if not exists
CREATE TABLE IF NOT EXISTS joke_categories (
    joke_id UUID REFERENCES jokes(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (joke_id, category_id)
);

-- Add content column to jokes table if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'jokes' AND column_name = 'content') THEN
        ALTER TABLE jokes ADD COLUMN content TEXT;
    END IF;
END $$;

-- Add search vector if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'jokes' AND column_name = 'search_vector') THEN
        ALTER TABLE jokes ADD COLUMN search_vector tsvector 
            GENERATED ALWAYS AS (
                setweight(to_tsvector('arabic', COALESCE(content, '')), 'A')
            ) STORED;
    END IF;
END $$;

-- Create search index if not exists
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'jokes_search_idx') THEN
        CREATE INDEX jokes_search_idx ON jokes USING GIN (search_vector);
    END IF;
END $$;

-- Create achievements table if not exists
CREATE TABLE IF NOT EXISTS achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    badge_color TEXT NOT NULL,
    category TEXT NOT NULL,
    requirement INTEGER NOT NULL,
    xp_reward INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create user_achievements table if not exists
CREATE TABLE IF NOT EXISTS user_achievements (
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    progress INTEGER NOT NULL DEFAULT 0,
    unlocked_at TIMESTAMP WITH TIME ZONE,
    PRIMARY KEY (user_id, achievement_id)
);

-- Create community_spotlights table if not exists
CREATE TABLE IF NOT EXISTS community_spotlights (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    metrics JSONB NOT NULL DEFAULT '{}',
    featured_joke_id UUID REFERENCES jokes(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Insert default categories if they don't exist
INSERT INTO categories (name, slug)
SELECT name, slug
FROM (VALUES
    ('General', 'general'),
    ('Puns', 'puns'),
    ('Situational', 'situational'),
    ('Cultural', 'cultural'),
    ('Wordplay', 'wordplay'),
    ('Family Friendly', 'family-friendly')
) AS new_categories(name, slug)
WHERE NOT EXISTS (
    SELECT 1 FROM categories c 
    WHERE c.slug = new_categories.slug
);

-- Add notification settings to profiles if column doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'profiles' AND column_name = 'notification_settings') THEN
        ALTER TABLE profiles ADD COLUMN notification_settings JSONB DEFAULT '{
            "new_comments": true,
            "new_reactions": true,
            "new_followers": true
        }'::jsonb;
    END IF;
END $$; 
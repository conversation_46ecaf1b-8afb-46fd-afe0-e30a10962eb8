-- Set the default value of user_id to the currently authenticated user
ALTER TABLE public.jokes
ALTER COLUMN user_id SET DEFAULT auth.uid();

-- Drop the existing insert policy
DROP POLICY IF EXISTS "Users can insert their own jokes" ON public.jokes;

-- Create a new, simpler insert policy
CREATE POLICY "Authenticated users can insert jokes" ON public.jokes
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- Add updated_at column to profiles if it does not exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE public.profiles ADD COLUMN updated_at timestamptz DEFAULT now();
  END IF;
END $$;

-- Create or replace trigger function to update updated_at on row change
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop and recreate trigger to ensure it exists
DROP TRIGGER IF EXISTS set_updated_at_on_profiles ON public.profiles;
CREATE TRIGGER set_updated_at_on_profiles
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();

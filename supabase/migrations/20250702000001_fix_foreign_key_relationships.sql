-- Fix Foreign Key Relationships for PostgREST
-- This migration fixes the foreign key constraints to enable proper relationship queries
-- Created: 2025-07-02

-- First, check current foreign key constraints
DO $$ 
BEGIN
    RAISE NOTICE 'Checking current foreign key constraints...';
END $$;

-- Drop existing foreign key constraints that might be incorrect
ALTER TABLE public.jokes DROP CONSTRAINT IF EXISTS jokes_user_id_fkey;
ALTER TABLE public.joke_likes DROP CONSTRAINT IF EXISTS joke_likes_user_id_fkey;
ALTER TABLE public.joke_likes DROP CONSTRAINT IF EXISTS joke_likes_joke_id_fkey;
ALTER TABLE public.comments DROP CONSTRAINT IF EXISTS comments_user_id_fkey;
ALTER TABLE public.comments DROP CONSTRAINT IF EXISTS comments_joke_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_follower_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_following_id_fkey;

-- Create the correct foreign key constraints
-- jokes.user_id should reference profiles.id (not auth.users.id)
ALTER TABLE public.jokes 
ADD CONSTRAINT jokes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- joke_likes.user_id should reference profiles.id
ALTER TABLE public.joke_likes 
ADD CONSTRAINT joke_likes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- joke_likes.joke_id should reference jokes.id
ALTER TABLE public.joke_likes 
ADD CONSTRAINT joke_likes_joke_id_fkey 
FOREIGN KEY (joke_id) REFERENCES public.jokes(id) ON DELETE CASCADE;

-- comments.user_id should reference profiles.id
ALTER TABLE public.comments 
ADD CONSTRAINT comments_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- comments.joke_id should reference jokes.id
ALTER TABLE public.comments 
ADD CONSTRAINT comments_joke_id_fkey 
FOREIGN KEY (joke_id) REFERENCES public.jokes(id) ON DELETE CASCADE;

-- follows.follower_id should reference profiles.id
ALTER TABLE public.follows 
ADD CONSTRAINT follows_follower_id_fkey 
FOREIGN KEY (follower_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- follows.following_id should reference profiles.id
ALTER TABLE public.follows 
ADD CONSTRAINT follows_following_id_fkey 
FOREIGN KEY (following_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Add any missing foreign key constraints for other tables
-- user_blocks table
ALTER TABLE public.user_blocks DROP CONSTRAINT IF EXISTS user_blocks_blocker_id_fkey;
ALTER TABLE public.user_blocks DROP CONSTRAINT IF EXISTS user_blocks_blocked_id_fkey;

ALTER TABLE public.user_blocks 
ADD CONSTRAINT user_blocks_blocker_id_fkey 
FOREIGN KEY (blocker_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.user_blocks 
ADD CONSTRAINT user_blocks_blocked_id_fkey 
FOREIGN KEY (blocked_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- reports table (if exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reports' AND table_schema = 'public') THEN
        ALTER TABLE public.reports DROP CONSTRAINT IF EXISTS reports_reporter_id_fkey;
        ALTER TABLE public.reports DROP CONSTRAINT IF EXISTS reports_reported_user_id_fkey;
        
        ALTER TABLE public.reports 
        ADD CONSTRAINT reports_reporter_id_fkey 
        FOREIGN KEY (reporter_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
        
        ALTER TABLE public.reports 
        ADD CONSTRAINT reports_reported_user_id_fkey 
        FOREIGN KEY (reported_user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- Log completion
DO $$ 
BEGIN
    RAISE NOTICE 'Foreign key relationships have been fixed successfully.';
    RAISE NOTICE 'PostgREST schema cache has been refreshed.';
END $$;

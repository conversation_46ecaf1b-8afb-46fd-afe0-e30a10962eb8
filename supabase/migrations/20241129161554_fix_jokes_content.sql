-- Drop the search vector first if it exists
DO $$ 
BEGIN 
    IF EXISTS (SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'jokes' AND column_name = 'search_vector') THEN
        ALTER TABLE jokes DROP COLUMN search_vector;
    END IF;
END $$;

-- Ensure content column exists and is properly typed
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'jokes' AND column_name = 'content') THEN
        ALTER TABLE jokes ADD COLUMN content TEXT;
    END IF;
END $$;

-- Update existing rows to have content if needed
UPDATE jokes 
SET content = title 
WHERE content IS NULL AND title IS NOT NULL;

-- Recreate the search vector
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'jokes' AND column_name = 'search_vector') THEN
        ALTER TABLE jokes ADD COLUMN search_vector tsvector 
            GENERATED ALWAYS AS (
                setweight(to_tsvector('arabic', COALESCE(content, '')), 'A')
            ) STORED;
        
        CREATE INDEX IF NOT EXISTS jokes_search_idx ON jokes USING GIN (search_vector);
    END IF;
END $$; 
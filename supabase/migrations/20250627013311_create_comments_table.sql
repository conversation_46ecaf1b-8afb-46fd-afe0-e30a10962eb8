-- Create comments table
create table if not exists public.comments (
    id uuid default uuid_generate_v4() primary key,
    created_at timestamptz default now(),
    joke_id uuid references public.jokes(id) on delete cascade not null,
    user_id uuid references auth.users(id) on delete cascade not null,
    content text not null,
    parent_id uuid references public.comments(id) on delete cascade,
    is_edited boolean default false
);

-- Add RLS policies
alter table public.comments enable row level security;

create policy "Comments are viewable by everyone" on public.comments for select using (true);

create policy "Users can insert their own comments" on public.comments for insert with check (auth.uid() = user_id);

create policy "Users can update their own comments" on public.comments for update with check (auth.uid() = user_id);

create policy "Users can delete their own comments" on public.comments for delete using (auth.uid() = user_id);

-- Drop existing function if it exists
drop function if exists public.toggle_emoji_reaction(text, uuid);
drop function if exists public.toggle_emoji_reaction(uuid, text);

-- Add update policy for emoji reactions
create policy "Users can update their own reactions"
    on public.emoji_reactions
    for update
    to authenticated
    using (auth.uid() = user_id)
    with check (auth.uid() = user_id);

-- Drop and recreate the unique constraint to handle updates
alter table public.emoji_reactions 
    drop constraint if exists unique_user_joke_emoji;

alter table public.emoji_reactions 
    add constraint unique_user_joke_emoji 
    unique nulls not distinct (user_id, joke_id, emoji);

-- Create function to toggle emoji reaction with correct parameter order
create or replace function public.toggle_emoji_reaction(
    p_emoji text,
    p_joke_id uuid,
    out success boolean,
    out is_added boolean,
    out error text
)
language plpgsql
security definer
as $$
declare
    v_user_id uuid;
    v_exists boolean;
begin
    -- Get the current user ID
    v_user_id := auth.uid();
    
    -- Check if user is authenticated
    if v_user_id is null then
        success := false;
        is_added := false;
        error := 'User must be authenticated';
        return;
    end if;

    -- Check if reaction exists
    select exists(
        select 1 
        from public.emoji_reactions 
        where joke_id = p_joke_id 
        and user_id = v_user_id 
        and emoji = p_emoji
    ) into v_exists;

    -- If reaction exists, delete it
    if v_exists then
        delete from public.emoji_reactions 
        where joke_id = p_joke_id 
        and user_id = v_user_id 
        and emoji = p_emoji;
        
        success := true;
        is_added := false;
        error := null;
    -- If reaction doesn't exist, insert it
    else
        insert into public.emoji_reactions (joke_id, user_id, emoji)
        values (p_joke_id, v_user_id, p_emoji);
        
        success := true;
        is_added := true;
        error := null;
    end if;

exception when others then
    success := false;
    is_added := false;
    error := SQLERRM;
end;
$$; 
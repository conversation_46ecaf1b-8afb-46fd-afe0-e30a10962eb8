-- Ensure content is populated for all jokes
UPDATE jokes 
SET content = COALESCE(content, title, '')
WHERE content IS NULL;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_community_spotlights_end_date 
ON community_spotlights(end_date);

CREATE INDEX IF NOT EXISTS idx_community_spotlights_category 
ON community_spotlights(category);

-- Ensure foreign key constraint exists
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE constraint_name = 'community_spotlights_featured_joke_id_fkey'
    ) THEN
        ALTER TABLE community_spotlights
        ADD CONSTRAINT community_spotlights_featured_joke_id_fkey
        FOREIGN KEY (featured_joke_id) REFERENCES jokes(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add RLS policies for community_spotlights if they don't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'community_spotlights' 
        AND policyname = 'Enable read access for all users'
    ) THEN
        CREATE POLICY "Enable read access for all users" 
        ON community_spotlights FOR SELECT 
        USING (true);
    END IF;
END $$;

ALTER TABLE community_spotlights ENABLE ROW LEVEL SECURITY;

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS get_spotlighted_jokes();

-- Create a function to get spotlighted jokes with content
CREATE OR REPLACE FUNCTION get_spotlighted_jokes()
RETURNS TABLE (
    id uuid,
    title text,
    content text,
    audio_url text,
    user_id uuid,
    created_at timestamptz,
    spotlight_start_date timestamptz,
    spotlight_end_date timestamptz
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        j.id,
        j.title,
        j.content,
        j.audio_url,
        j.user_id,
        j.created_at,
        cs.start_date as spotlight_start_date,
        cs.end_date as spotlight_end_date
    FROM jokes j
    JOIN community_spotlights cs ON j.id = cs.featured_joke_id
    WHERE cs.end_date > NOW()
    ORDER BY cs.start_date DESC;
END;
$$ LANGUAGE plpgsql; 
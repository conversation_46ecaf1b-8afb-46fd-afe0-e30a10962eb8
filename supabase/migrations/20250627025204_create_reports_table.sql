-- Create reports table
create type public.report_type as enum (
    'joke',
    'user',
    'comment'
);

create table if not exists public.reports (
    id uuid default uuid_generate_v4() primary key,
    created_at timestamptz default now(),
    reporter_id uuid references auth.users(id) on delete set null,
    reported_item_id uuid not null,
    report_type public.report_type not null,
    reason text,
    status text default 'pending'
);

-- Add RLS policies
alter table public.reports enable row level security;

create policy "Users can insert reports" on public.reports for insert with check (auth.uid() = reporter_id);

create policy "<PERSON><PERSON> can view all reports" on public.reports for select using (true); -- TODO: Restrict to admin role

create policy "<PERSON><PERSON> can update report status" on public.reports for update using (true); -- TODO: Restrict to admin role

-- Fix Foreign Key Relationships for Supabase PostgREST
-- This script fixes the foreign key constraints to enable proper relationship queries

-- 1. First, let's check the current foreign key constraints
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('jokes', 'profiles', 'joke_likes', 'comments', 'follows');

-- 2. Drop existing foreign key constraints that might be incorrect
ALTER TABLE public.jokes DROP CONSTRAINT IF EXISTS jokes_user_id_fkey;
ALTER TABLE public.joke_likes DROP CONSTRAINT IF EXISTS joke_likes_user_id_fkey;
ALTER TABLE public.joke_likes DROP CONSTRAINT IF EXISTS joke_likes_joke_id_fkey;
ALTER TABLE public.comments DROP CONSTRAINT IF EXISTS comments_user_id_fkey;
ALTER TABLE public.comments DROP CONSTRAINT IF EXISTS comments_joke_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_follower_id_fkey;
ALTER TABLE public.follows DROP CONSTRAINT IF EXISTS follows_following_id_fkey;

-- 3. Create the correct foreign key constraints
-- jokes.user_id should reference profiles.id (not auth.users.id)
ALTER TABLE public.jokes 
ADD CONSTRAINT jokes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- joke_likes.user_id should reference profiles.id
ALTER TABLE public.joke_likes 
ADD CONSTRAINT joke_likes_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- joke_likes.joke_id should reference jokes.id
ALTER TABLE public.joke_likes 
ADD CONSTRAINT joke_likes_joke_id_fkey 
FOREIGN KEY (joke_id) REFERENCES public.jokes(id) ON DELETE CASCADE;

-- comments.user_id should reference profiles.id
ALTER TABLE public.comments 
ADD CONSTRAINT comments_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- comments.joke_id should reference jokes.id
ALTER TABLE public.comments 
ADD CONSTRAINT comments_joke_id_fkey 
FOREIGN KEY (joke_id) REFERENCES public.jokes(id) ON DELETE CASCADE;

-- follows.follower_id should reference profiles.id
ALTER TABLE public.follows 
ADD CONSTRAINT follows_follower_id_fkey 
FOREIGN KEY (follower_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- follows.following_id should reference profiles.id
ALTER TABLE public.follows 
ADD CONSTRAINT follows_following_id_fkey 
FOREIGN KEY (following_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- 4. Refresh PostgREST schema cache
NOTIFY pgrst, 'reload schema';

-- 5. Verify the new foreign key constraints
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    tc.constraint_name
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN ('jokes', 'profiles', 'joke_likes', 'comments', 'follows')
ORDER BY tc.table_name, kcu.column_name;

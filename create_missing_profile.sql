-- Create missing profile for the current user
-- Run this to manually create a profile if the trigger didn't work

-- Check if profile exists for the user
SELECT 'Profile exists' as status, * FROM public.profiles 
WHERE id = '690f229f-e603-494d-a5eb-c66e1514039c';

-- Create profile if it doesn't exist
INSERT INTO public.profiles (id, username, onboarded, created_at)
VALUES (
  '690f229f-e603-494d-a5eb-c66e1514039c',
  'agnostimous',
  false,
  now()
)
ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  onboarded = EXCLUDED.onboarded;

-- Verify the profile was created
SELECT 'Profile after creation' as status, * FROM public.profiles 
WHERE id = '690f229f-e603-494d-a5eb-c66e1514039c';
